image: node:19-bullseye

stages:
    - Setup
    - Test
    - Build
    - Review
    - Publish

Cache Dependencies:
    stage: Setup
    tags:
        - docker
    cache:
        key:
            files:
                - package-lock.json
        paths:
            - node_modules/
        policy: push
    script:
        - npm ci --prefer-offline
    rules:
        - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
        - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

Build Project:
    stage: Build
    interruptible: true
    cache:
        key:
            files:
                - package-lock.json
        paths:
            - node_modules/
        policy: pull
    tags:
        - docker
    script:
        - cd "$CI_PROJECT_DIR"
        - npm ci --prefer-offline
    artifacts:
        expire_in: never
        name: 'ui_build_files'
        expose_as: 'UI build files'
        paths:
            - dist/
    rules:
        - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
        - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH

Design Review:
    stage: Review
    interruptible: true
    cache:
        key:
            files:
                - package-lock.json
        paths:
            - node_modules/
        policy: pull
    tags:
        - docker
    script:
        - cd "$CI_PROJECT_DIR"
        - npm ci --prefer-offline
        - export NODE_OPTIONS="--max-old-space-size=4096"
        - npx chromatic --project-token $CHROMATIC_PROJECT_TOKEN
    rules:
        - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
        - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH

Deploy Storybook:
    stage: Publish
    interruptible: true
    cache:
        key:
            files:
                - package-lock.json
        paths:
            - node_modules/
        policy: pull
    tags:
        - docker
    script:
        - cd "$CI_PROJECT_DIR"
        - npm ci --prefer-offline
        - export NODE_OPTIONS="--max-old-space-size=4096"
        - npx chromatic --project-token $CHROMATIC_PROJECT_TOKEN --auto-accept-changes --exit-zero-on-changes
    rules:
        - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

Lint:
    stage: Test
    interruptible: true
    cache:
        key:
            files:
                - package-lock.json
        paths:
            - node_modules/
        policy: pull
    tags:
        - docker
    script:
        - npm run lint:ci
    rules:
        - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
        - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH

Unit Test:
    interruptible: true
    stage: Test
    cache:
        key:
            files:
                - package-lock.json
        paths:
            - node_modules/
        policy: pull
    tags:
        - docker
    script:
        - npm run test:ci
        - npm run coverage
    artifacts:
        when: always
        reports:
            junit: $CI_PROJECT_DIR/test-report.xml
            coverage_report:
                coverage_format: cobertura
                path: $CI_PROJECT_DIR/coverage/cobertura-coverage.xml

    rules:
        - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH

Publish:
    stage: Publish
    interruptible: true
    cache:
        key:
            files:
                - package-lock.json
        paths:
            - node_modules/
        policy: pull
    tags:
        - docker
    script:
        - cd "$CI_PROJECT_DIR"
        - npm ci --prefer-offline
        - |
            # Get current version and increment minor version
            PACKAGE_VERSION=$(node -p "require('./package.json').version")
            IFS='.' read -ra VERSION_PARTS <<< "$PACKAGE_VERSION"
            MAJOR=${VERSION_PARTS[0]}
            MINOR=${VERSION_PARTS[1]}
            PATCH=${VERSION_PARTS[2]}
            NEW_PATCH=$((PATCH + 1))
            NEW_VERSION="$MAJOR.$MINOR.$NEW_PATCH"

            # Update package.json with new version
            npm version $NEW_VERSION --no-git-tag-version

            # Configure git for commit
            git config --global user.email "******************"
            git config --global user.name "GitLab CI"

            # Commit the version change
            git add package.json package-lock.json
            git commit -m "MS-X Bump version to $NEW_VERSION [skip ci]"
            git push https://gitlab-ci-token:${CI_JOB_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git HEAD:${CI_COMMIT_BRANCH}
        - echo "@services:registry=https://${CI_SERVER_HOST}/api/v4/packages/npm/" > .npmrc
        - echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}" >> .npmrc
        - cat .npmrc
        - npm publish --verbose
    rules:
        - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    artifacts:
        expire_in: never
        name: 'ui_build_files'
        expose_as: 'UI build files'
        paths:
            - dist/
