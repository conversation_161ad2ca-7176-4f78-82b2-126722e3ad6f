# Ovation UI Component Library

## Usage
### Package Setup
1. Consuming repos need to be configured to point to the @services:registry, instructions: https://git.goboomtown.com/services/library/ui-component-library/-/packages/4702
2. Run `npm install @services/ui-component-library --save` 
3. Import styles into a css file, or directly into `main.ts` inside a vue application
```css
@import '@services/ui-component-library/styles';
@import '/app.css'
```
```js
import '@services/ui-component-library/styles'
import App from "./App.vue";
```
> Note: Import ui-component-library styles above application styles to allow local component styles to override theme defaults

### Component Usage
Import components and use within SFCs in vue applications. 
```js
<script setup>
import { CxmButton } from '@services/ui-component-library'
</script>

<template>
   <CxmButton text="Submit"/>
</template>
```
## Development
### Development Setup

1. `<NAME_EMAIL>:services/library/ui-component-library.git` and `cd ui-component-library`
2. `npm ci`
3. `npm run storybook` for storybook and `npm run test` to watch tests.

### Component Structure

All components are inside `src/components`, and should follow the naming convention `CxmDemo` where `Demo` is the component name. This will make it easier for users of the library to use the components, knowing they are all namespaced to `cxm`.

### Storybook

Each component should have a corresponding storybook in the `src/stories` directory. To run storybook, run `npm run storybook`. Build a static version of Storybook with `npm run build-storybook`.

## Testing

Testing is done with a combination of `vitest`, `testing-library/vue`, and `happy-dom`.

 - `npm run test` - Runs vitest in watch mode
 - `npm run test:ci` - Runs vitest once, for CI pipeline purposes
 - `npm run test:ui` - Runs Vitest in watch mode, additionally opening a web portal to view test status

### Linting and Formatting

Linting and formatting are both done with `eslint`, configured inside of `package.json` `eslintConfig` configuration property. Your IDE should be able to pick up this eslint configuration and format/lint automatically.

### Building and Publishing
- Vite is configured to output a compiled library in esm and cjs formats 
- Types are automatically generated and published with the npm package. 
- `npm run build` outputs to the `dist` directory.

### Recommended IDE Setup

- [VS Code](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur) + [TypeScript Vue Plugin (Volar)](https://marketplace.visualstudio.com/items?itemName=Vue.vscode-typescript-vue-plugin).

### Type Support For `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [TypeScript Vue Plugin (Volar)](https://marketplace.visualstudio.com/items?itemName=Vue.vscode-typescript-vue-plugin) to make the TypeScript language service aware of `.vue` types.

If the standalone TypeScript plugin doesn't feel fast enough to you, Volar has also implemented a [Take Over Mode](https://github.com/johnsoncodehk/volar/discussions/471#discussioncomment-1361669) that is more performant. You can enable it by the following steps:

1. Disable the built-in TypeScript Extension
   1. Run `Extensions: Show Built-in Extensions` from VSCode's command palette
   2. Find `TypeScript and JavaScript Language Features`, right click and select `Disable (Workspace)`
2. Reload the VSCode window by running `Developer: Reload Window` from the command palette.