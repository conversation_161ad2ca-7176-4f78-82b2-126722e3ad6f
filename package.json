{"name": "@services/ui-component-library", "version": "1.0.77", "type": "module", "files": ["src"], "main": "./src/index.ts", "module": "./src/index.ts", "exports": {".": {"types": "./src/index.ts", "import": "./src/index.ts", "require": "./src/index.ts"}, "./components/*": "./src/components/*", "./themes/*": "./src/themes/*", "./utils/*": "./src/utils/*", "./directives/*": "./src/directives/*"}, "types": "./src/index.ts", "publishConfig": {"@services:registry": "https://git.goboomtown.com/api/v4/projects/281/packages/npm/"}, "scripts": {"build": "vite build && vue-tsc --emitDeclarationOnly", "build:types": "vue-tsc --emitDeclarationOnly -p tsconfig.json", "build-storybook": "storybook build", "chromatic": "chromatic", "coverage": "vitest run --coverage", "dev": "vite", "lint": "eslint --fix --ext .ts,vue --ignore-path .gitignore --ignore-pattern 'src/stories/**' src", "lint:ci": "eslint --ext .ts,vue --ignore-path .gitignore", "preview": "vite preview", "storybook": "storybook dev -p 6006", "test": "vitest", "test:ci": "vitest run --reporter=junit --reporter=default --outputFile.junit=./test-report.xml", "test:ui": "vitest --ui", "format": "prettier --write src/"}, "peerDependencies": {"core-js": "^3.8.3", "sass": "^1.34.1", "vue": "^3.4.0"}, "dependencies": {"@primevue/themes": "^4.3.4", "luxon": "^3.3.0", "primeicons": "^7.0.0", "primevue": "^4.3.4", "quill": "^1.3.7", "uuid": "^11.1.0"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.4", "@csstools/postcss-cascade-layers": "^5.0.1", "@rollup/plugin-typescript": "^11.0.0", "@storybook/addon-a11y": "^8.6.4", "@storybook/addon-essentials": "^8.6.4", "@storybook/addon-interactions": "^8.6.4", "@storybook/addon-links": "^8.6.4", "@storybook/addon-mdx-gfm": "^8.6.4", "@storybook/addon-styling": "^1.3.7", "@storybook/blocks": "^8.6.4", "@storybook/builder-vite": "^8.6.4", "@storybook/test": "^8.6.4", "@storybook/vue3": "^8.6.4", "@storybook/vue3-vite": "^8.6.4", "@tailwindcss/postcss": "^4.0.14", "@testing-library/vue": "^6.6.1", "@types/luxon": "^3.3.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "@vitejs/plugin-vue": "^5.0.4", "@vitest/coverage-v8": "^3.0.7", "@vitest/ui": "^3.0.7", "@vue/eslint-config-typescript": "^11.0.2", "autoprefixer": "^10.4.17", "chromatic": "^11.27.0", "eslint": "^8.33.0", "eslint-plugin-vue": "^9.9.0", "happy-dom": "^17.1.8", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "postcss-preset-env": "^10.1.5", "react": "^18.2.0", "react-dom": "^18.2.0", "sass": "^1.58.0", "sass-loader": "^10", "storybook": "^8.6.4", "tailwindcss": "^3.4.17", "typescript": "^5.3.3", "vite": "^6.2.0", "vite-plugin-dts": "^4.5.0", "vitest": "^3.0.7", "vue-tsc": "^2.0.6"}, "eslintConfig": {"root": true, "env": {"browser": true, "es6": true, "node": true}, "extends": ["eslint:recommended", "plugin:vue/vue3-recommended", "@vue/typescript/recommended"], "parserOptions": {"parser": "@typescript-eslint/parser", "ecmaVersion": "latest", "ecmaFeatures": {"jsx": true}}, "rules": {"no-unused-expressions": "off", "no-unused-vars": "off", "eqeqeq": "off", "plugin:vue/vue3-essential": "off", "vue/require-default-prop": "off", "vue/html-self-closing": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">= 18.13.0", "npm": ">= 9.0.0"}}