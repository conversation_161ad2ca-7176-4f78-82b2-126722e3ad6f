import type { Meta, StoryObj } from '@storybook/vue3';
import BravoLoginScreen from '../components/BravoLoginScreen.vue';

const meta = {
    title: 'Components/BravoLoginScreen',
    component: BravoLoginScreen,
    tags: ['autodocs'],
    parameters: {
        layout: 'fullscreen',
        docs: {
            story: {
                height: '500px',
            },
        },
    },
    argTypes: {
        onSubmit: {
            description: 'Emitted when the form is submitted with login credentials',
            table: {
                type: {
                    summary: '(event: { email: string; password: string; rememberMe: boolean }) => void',
                },
            },
        },
        onForgotPassword: {
            description: 'Emitted when the forgot password button is clicked',
            table: {
                type: {
                    summary: '() => void',
                },
            },
        },
    },
} satisfies Meta<typeof BravoLoginScreen>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {},
    render: (args) => ({
        components: { BravoLoginScreen },
        setup() {
            const onSubmit = (credentials: { email: string; password: string; rememberMe: boolean }) => {
                console.log('Login submitted:', credentials);
            };

            const onForgotPassword = () => {
                console.log('Forgot password clicked');
                alert('Forgot password functionality would be implemented here');
            };

            return { args, onSubmit, onForgotPassword };
        },
        template: '<BravoLoginScreen v-bind="args" @submit="onSubmit" @forgot-password="onForgotPassword" />',
    }),
};
