import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import BravoChip from '../components/BravoChip.vue';

const meta = {
    title: 'Misc/Chip',
    component: BravoChip,
    tags: ['autodocs'],
    argTypes: {
        label: { control: 'text' },
        icon: { control: 'text' },
        image: { control: 'text' },
        removable: { control: 'boolean' },
    },
} satisfies Meta<typeof BravoChip>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
    args: {
        label: 'Basic Chip',
    },
};

export const Icons: Story = {
    render: () => ({
        components: { BravoChip },
        template: `
      <div class="flex gap-2">
        <BravoChip label="Apple" icon="pi pi-apple" />
        <BravoChip label="Facebook" icon="pi pi-facebook" />
        <BravoChip label="Google" icon="pi pi-google" />
      </div>
    `,
    }),
};

export const Image: Story = {
    render: () => ({
        components: { BravoChip },
        template: `
      <div class="flex gap-2">
        <BravoChip 
          label="<PERSON>" 
          image="https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png" 
        />
        <BravoChip 
          label="Asiya Javayant" 
          image="https://primefaces.org/cdn/primevue/images/avatar/asiyajavayant.png" 
        />
      </div>
    `,
    }),
};

export const Removable: Story = {
    args: {
        label: 'Removable Chip',
        removable: true,
    },
};

export const Styling: Story = {
    render: () => ({
        components: { BravoChip },
        template: `
      <div class="flex gap-2">
        <BravoChip label="Action" class="bg-primary" />
        <BravoChip label="Comedy" class="bg-success" />
        <BravoChip label="Mystery" class="bg-info" />
        <BravoChip label="Horror" class="bg-warning" />
      </div>
    `,
    }),
};
