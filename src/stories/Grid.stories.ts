import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import { Meta, StoryObj } from '@storybook/vue3';
// Storybook Documentation: https://storybook.js.org/docs/vue/writing-stories/introduction

const products = [
    {
        id: 1,
        name: 'one',
        type: 'amazing',
        price: '$100',
    },
    {
        id: 2,
        name: 'two',
        type: 'great',
        price: '$50',
    },
    {
        id: 3,
        name: 'three',
        type: 'fantastic',
        price: '$75',
    },
    {
        id: 4,
        name: 'four',
        type: 'awesome',
        price: '$120',
    },
    {
        id: 5,
        name: 'five',
        type: 'amazing',
        price: '$90',
    },
    {
        id: 6,
        name: 'six',
        type: 'great',
        price: '$60',
    },
    {
        id: 7,
        name: 'seven',
        type: 'fantastic',
        price: '$85',
    },
    {
        id: 8,
        name: 'eight',
        type: 'awesome',
        price: '$110',
    },
    {
        id: 9,
        name: 'nine',
        type: 'amazing',
        price: '$105',
    },
    {
        id: 10,
        name: 'ten',
        type: 'great',
        price: '$55',
    },
    {
        id: 11,
        name: 'eleven',
        type: 'fantastic',
        price: '$80',
    },
    {
        id: 12,
        name: 'twelve',
        type: 'awesome',
        price: '$125',
    },
    {
        id: 13,
        name: 'thirteen',
        type: 'amazing',
        price: '$95',
    },
    {
        id: 14,
        name: 'fourteen',
        type: 'great',
        price: '$65',
    },
    {
        id: 15,
        name: 'fifteen',
        type: 'fantastic',
        price: '$88',
    },
    {
        id: 16,
        name: 'sixteen',
        type: 'awesome',
        price: '$115',
    },
    {
        id: 17,
        name: 'seventeen',
        type: 'amazing',
        price: '$110',
    },
    {
        id: 18,
        name: 'eighteen',
        type: 'great',
        price: '$58',
    },
    {
        id: 19,
        name: 'nineteen',
        type: 'fantastic',
        price: '$82',
    },
    {
        id: 20,
        name: 'twenty',
        type: 'awesome',
        price: '$130',
    },
];

const columns = [
    { field: 'id', header: 'ID' },
    { field: 'name', header: 'Name' },
    { field: 'type', header: 'Type' },
    { field: 'price', header: 'Price' },
];

// Default export defines metadata about your component
export default {
    component: DataTable,
    title: 'Legacy & Custom/Grid',
    parameters: {
        docs: {
            description: {
                component: 'Grids display data to users in a structured format',
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        // Add controls for specific arguments
    },
} as Meta<typeof DataTable>;

type Story = StoryObj<typeof DataTable>;

export const Default: Story = {
    args: { value: products },
    render: (args: any) => ({
        components: {
            DataTable,
            Column,
        },
        data: () => ({ ...args, columns: columns }),
        template: `
          <div>
          <DataTable :value="value" :rowHover="true" paginator :rows="10" :rowsPerPageOptions="[10, 20, 50]" tableStyle="min-width: 50rem">
            <Column v-for="col of columns" :key="col.id" :field="col.field" :header="col.header"></Column>
          </DataTable>
          </div>`,
    }),
};
