import type { Meta, StoryObj } from '@storybook/vue3';
import BravoRelativeDateTime from '../components/BravoRelativeDateTime.vue';
import { ref } from 'vue';

// Meta information for the component
const meta = {
    title: 'Form/BravoRelativeDateTime',
    component: BravoRelativeDateTime,
    tags: ['autodocs'],
    parameters: {
        layout: 'fullscreen',
        docs: {
            description: {
                component: `
**BravoRelativeDateTime** is a versatile date and time picker component that supports both fixed dates and relative durations.

## Features
- **Fixed Date Mode**: Traditional date/time picker with calendar interface
- **Relative Date Mode**: Duration picker with units (Hours, Days, Weeks)
- **Toggle Support**: Switch between fixed and relative modes
- **Time Selection**: Hours, minutes, and AM/PM selection for fixed dates
- **ISO8601 Duration**: Outputs duration in standard ISO format (PT2H, P3D, P1W)

## Usage
The component can be used in two main modes:
- \`type="date"\` - Shows fixed date picker (can toggle to relative)
- \`type="duration"\` - Shows only relative duration picker

The \`modelValue\` accepts either a Date object for fixed dates or an ISO8601 duration string for relative dates.
                `,
            },
        },
    },
    argTypes: {
        modelValue: { 
            control: 'text',
            description: 'The current value - Date object for fixed dates, ISO8601 string for durations'
        },
        type: {
            control: 'select',
            options: ['date', 'duration'],
            description: 'Component mode - "date" allows toggling, "duration" is duration-only'
        },
    },
} satisfies Meta<typeof BravoRelativeDateTime>;

export default meta;
type Story = StoryObj<typeof meta>;

// Fixed Date Mode (Default)
export const FixedDate: Story = {
    parameters: {
        docs: {
            description: {
                story: 'Default mode showing a fixed date picker. Click the input to open the dialog where you can switch between fixed and relative modes.',
            },
            source: {
                code: `
<template>
  <BravoRelativeDateTime
    v-model="dateValue"
    type="date"
    @relativeDatePickerApply="handleApply"
    @relativeDatePickerCancel="handleCancel"
  />
</template>

<script setup>
import { ref } from 'vue';
import BravoRelativeDateTime from './BravoRelativeDateTime.vue';

const dateValue = ref(new Date());

const handleApply = (value) => {
  console.log('Applied:', value);
};

const handleCancel = () => {
  console.log('Cancelled');
};
</script>
                `,
            },
        },
    },
    render: () => ({
        components: { BravoRelativeDateTime },
        setup() {
            const dateValue = ref(new Date());
            
            const handleApply = (value: Date | string) => {
                console.log('Applied:', value);
            };
            
            const handleCancel = () => {
                console.log('Cancelled');
            };
            
            return { dateValue, handleApply, handleCancel };
        },
        template: `
            <div style="padding: 20px; max-width: 400px;">
                <h4>Fixed Date Mode</h4>
                <p>Current value: {{ dateValue }}</p>
                <BravoRelativeDateTime
                    v-model="dateValue"
                    type="date"
                    @relativeDatePickerApply="handleApply"
                    @relativeDatePickerCancel="handleCancel"
                />
            </div>
        `,
    }),
};

// Duration Only Mode
export const DurationOnly: Story = {
    parameters: {
        docs: {
            description: {
                story: 'Duration-only mode that only allows relative date selection. The value is stored as an ISO8601 duration string.',
            },
            source: {
                code: `
<template>
  <BravoRelativeDateTime
    v-model="durationValue"
    type="duration"
  />
</template>

<script setup>
import { ref } from 'vue';

const durationValue = ref('PT2H'); // 2 hours
</script>
                `,
            },
        },
    },
    render: () => ({
        components: { BravoRelativeDateTime },
        setup() {
            const durationValue = ref('PT2H'); // 2 hours
            
            return { durationValue };
        },
        template: `
            <div style="padding: 20px; max-width: 400px;">
                <h4>Duration Only Mode</h4>
                <p>Current value: {{ durationValue }}</p>
                <BravoRelativeDateTime
                    v-model="durationValue"
                    type="duration"
                />
            </div>
        `,
    }),
};

// Pre-filled with Hours Duration
export const WithHoursDuration: Story = {
    parameters: {
        docs: {
            description: {
                story: 'Example with a pre-filled hours duration (PT4H = 4 hours).',
            },
        },
    },
    render: () => ({
        components: { BravoRelativeDateTime },
        setup() {
            const durationValue = ref('PT4H'); // 4 hours
            
            return { durationValue };
        },
        template: `
            <div style="padding: 20px; max-width: 400px;">
                <h4>4 Hours Duration</h4>
                <p>Current value: {{ durationValue }}</p>
                <BravoRelativeDateTime
                    v-model="durationValue"
                    type="duration"
                />
            </div>
        `,
    }),
};

// Pre-filled with Days Duration
export const WithDaysDuration: Story = {
    parameters: {
        docs: {
            description: {
                story: 'Example with a pre-filled days duration (P3D = 3 days).',
            },
        },
    },
    render: () => ({
        components: { BravoRelativeDateTime },
        setup() {
            const durationValue = ref('P3D'); // 3 days
            
            return { durationValue };
        },
        template: `
            <div style="padding: 20px; max-width: 400px;">
                <h4>3 Days Duration</h4>
                <p>Current value: {{ durationValue }}</p>
                <BravoRelativeDateTime
                    v-model="durationValue"
                    type="duration"
                />
            </div>
        `,
    }),
};

// Pre-filled with Weeks Duration
export const WithWeeksDuration: Story = {
    parameters: {
        docs: {
            description: {
                story: 'Example with a pre-filled weeks duration (P2W = 2 weeks).',
            },
        },
    },
    render: () => ({
        components: { BravoRelativeDateTime },
        setup() {
            const durationValue = ref('P2W'); // 2 weeks
            
            return { durationValue };
        },
        template: `
            <div style="padding: 20px; max-width: 400px;">
                <h4>2 Weeks Duration</h4>
                <p>Current value: {{ durationValue }}</p>
                <BravoRelativeDateTime
                    v-model="durationValue"
                    type="duration"
                />
            </div>
        `,
    }),
};

// Interactive Example with Event Handling
export const InteractiveExample: Story = {
    parameters: {
        docs: {
            description: {
                story: 'Interactive example showing event handling and real-time value updates.',
            },
        },
    },
    render: () => ({
        components: { BravoRelativeDateTime },
        setup() {
            const dateValue = ref(new Date());
            const events = ref<string[]>([]);
            
            const handleUpdate = (value: Date | string) => {
                events.value.unshift(`Value updated: ${value}`);
                if (events.value.length > 5) events.value.pop();
            };
            
            const handleApply = (value: Date | string) => {
                events.value.unshift(`Apply clicked: ${value}`);
                if (events.value.length > 5) events.value.pop();
            };
            
            const handleCancel = () => {
                events.value.unshift('Cancel clicked');
                if (events.value.length > 5) events.value.pop();
            };
            
            return { dateValue, events, handleUpdate, handleApply, handleCancel };
        },
        template: `
            <div style="padding: 20px; max-width: 600px;">
                <h4>Interactive Example</h4>
                <div style="margin-bottom: 20px;">
                    <strong>Current Value:</strong> {{ dateValue }}
                </div>
                
                <BravoRelativeDateTime
                    v-model="dateValue"
                    type="date"
                    @update:modelValue="handleUpdate"
                    @relativeDatePickerApply="handleApply"
                    @relativeDatePickerCancel="handleCancel"
                />
                
                <div style="margin-top: 20px;">
                    <h5>Recent Events:</h5>
                    <ul style="font-family: monospace; font-size: 12px;">
                        <li v-for="event in events" :key="event">{{ event }}</li>
                    </ul>
                </div>
            </div>
        `,
    }),
};

// Comparison with Original CXM Component
export const ComparisonExample: Story = {
    parameters: {
        docs: {
            description: {
                story: 'Side-by-side comparison showing the new Bravo component alongside equivalent functionality.',
            },
        },
    },
    render: () => ({
        components: { BravoRelativeDateTime },
        setup() {
            const bravoDateValue = ref(new Date());
            const bravoDurationValue = ref('PT1H');
            
            return { bravoDateValue, bravoDurationValue };
        },
        template: `
            <div style="padding: 20px; display: grid; grid-template-columns: 1fr 1fr; gap: 30px; max-width: 800px;">
                <div>
                    <h4>Bravo Fixed Date Mode</h4>
                    <p style="font-size: 12px; color: #666;">Value: {{ bravoDateValue }}</p>
                    <BravoRelativeDateTime
                        v-model="bravoDateValue"
                        type="date"
                    />
                </div>
                
                <div>
                    <h4>Bravo Duration Mode</h4>
                    <p style="font-size: 12px; color: #666;">Value: {{ bravoDurationValue }}</p>
                    <BravoRelativeDateTime
                        v-model="bravoDurationValue"
                        type="duration"
                    />
                </div>
            </div>
        `,
    }),
}; 