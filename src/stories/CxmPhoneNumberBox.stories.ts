import InputGroup from 'primevue/inputgroup';
import InputGroupAddon from 'primevue/inputgroupaddon';
import InputMask from 'primevue/inputmask';

import { Meta, StoryObj } from '@storybook/vue3';

export default {
    component: InputMask,
    title: 'Legacy & Custom/Inputs/CxmPhoneNumberBox',
    parameters: {
        docs: {
            description: {
                component: 'Input a phone number box',
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {},
} as Meta<typeof InputMask>;

type Story = StoryObj<typeof InputMask>;

export const Default: Story = {
    args: {},
    render: (args) => ({
        components: {
            InputGroup,
            InputGroupAddon,
            InputMask,
        },
        data: () => ({ args, value: '' }),
        template: `
        <InputGroup>
          <InputGroupAddon>
              <i class="pi pi-phone"></i>
          </InputGroupAddon>
          <InputMask id="phone" v-model="value2" mask="(*************" placeholder="(*************" v-bind="args" />
      </InputGroup>
      `,
    }),
};
