import type { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import BravoMenu from '../components/BravoMenu.vue';
import BravoButton from '../components/BravoButton.vue';
import BravoAvatar from '../components/BravoAvatar.vue';

interface BravoMenuItem {
    label: string;
    items?: BravoMenuItem[];
    icon?: string;
    command?: () => void;
    url?: string;
}

const meta = {
    title: 'Menu/Menu',
    component: BravoMenu,
    tags: ['autodocs'],
    argTypes: {
        model: { control: 'object' },
        popup: { control: 'boolean' },
        pt: { control: 'object' },
    },
} satisfies Meta<typeof BravoMenu>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    args: {
        model: [
            {
                label: 'Options',
                items: [
                    { label: 'New', icon: 'pi pi-plus', command: () => console.log('New clicked') },
                    { label: 'Delete', icon: 'pi pi-trash', command: () => console.log('Delete clicked') },
                ],
            },
            {
                label: 'Navigate',
                items: [
                    { label: 'Vue Website', icon: 'pi pi-external-link', url: 'https://vuejs.org/' },
                    { label: 'Router', icon: 'pi pi-upload', command: () => console.log('Router clicked') },
                ],
            },
        ],
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoMenu :model="model" />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoMenu },
        setup() {
            const menuItems = args.model as BravoMenuItem[];
            return { menuItems };
        },
        template: '<BravoMenu :model="menuItems" />',
    }),
};

// Popup Menu
export const Popup: Story = {
    args: {
        popup: true,
        model: [
            {
                label: 'Options',
                items: [
                    { label: 'Refresh', icon: 'pi pi-refresh', command: () => console.log('Refresh clicked') },
                    { label: 'Export', icon: 'pi pi-upload', command: () => console.log('Export clicked') },
                ],
            },
        ],
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
    <div class="card flex justify-center">
        <BravoButton type="button" icon="pi pi-ellipsis-v" @click="toggle" aria-haspopup="true" aria-controls="overlay_menu" />
        <BravoMenu ref="menu" id="overlay_menu" :model="items" :popup="true" />
    </div>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoMenu, BravoButton },
        setup() {
            const menu = ref();
            const menuItems = args.model as BravoMenuItem[];

            const toggle = (event: Event) => {
                if (menu.value) {
                    menu.value.toggle(event);
                }
            };

            return { menuItems, menu, toggle };
        },
        template: `
      <div class="card flex justify-center">
        <BravoButton 
          type="button" 
          icon="pi pi-ellipsis-v" 
          @click="toggle" 
          aria-haspopup="true" 
          aria-controls="overlay_menu"
        />
        <BravoMenu 
          ref="menu" 
          id="overlay_menu" 
          :model="menuItems" 
          :popup="true" 
        />
      </div>
    `,
    }),
};

// Profile Menu
export const ProfileMenu: Story = {
    args: {
        model: [
            {
                items: [
                    { label: 'Profile', icon: 'pi pi-user', command: () => console.log('Profile clicked') },
                    { label: 'Help Site', icon: 'pi pi-question-circle', command: () => console.log('Help clicked') },
                    { label: 'Product Updates', icon: 'pi pi-bell', command: () => console.log('Updates clicked') },
                    { label: 'Status Page', icon: 'pi pi-check-circle', command: () => console.log('Status clicked') },
                    { label: 'Sign Out', icon: 'pi pi-sign-out', command: () => console.log('Sign out clicked') },
                ]
            }
        ],
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
    <BravoMenu :model="items">
        <template #start>
            <div class="flex items-center p-3">
                <BravoAvatar image="https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png" class="mr-2" />
                <div>
                    <div class="font-bold">Amy Elsner</div>
                    <div class="text-sm">Administrator</div>
                </div>
            </div>
        </template>
    </BravoMenu>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoMenu, BravoAvatar },
        setup() {
            const menuItems = args.model as BravoMenuItem[];
            return { menuItems };
        },
        template: `
        <BravoMenu :model="menuItems">
            <template #start>
                <div class="flex items-center p-3">
                    <BravoAvatar image="https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png" class="mr-2" />
                    <div>
                        <div class="font-bold">Amy Elsner</div>
                        <div class="text-sm">Administrator</div>
                    </div>
                </div>
            </template>
        </BravoMenu>
        `,
    }),
};
