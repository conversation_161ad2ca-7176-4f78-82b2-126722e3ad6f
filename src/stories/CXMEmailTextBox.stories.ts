import InputText from 'primevue/inputtext';
import InputGroup from 'primevue/inputgroup';
import Button from 'primevue/button';
import type { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';

const meta = {
    component: InputText,
    title: 'Legacy & Custom/Inputs/EmailTextBox',
    parameters: {
        docs: {
            description: {
                component: 'Input for emails.',
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        size: { control: 'select', options: ['small', 'normal', 'large'] },
        type: { control: 'select', options: ['text', 'password'] },
        disabled: { control: 'boolean' },
        onClick: { action: 'clicked' },
    },
} as Meta<typeof InputText>;

export default meta;
type Story = StoryObj<typeof InputText>;

export const Default: Story = {
    args: { type: 'email' },
    render: (args) => ({
        components: {
            InputText,
            InputGroup,
            Button,
        },
        setup() {
            const value = ref('');
            const clear = () => {
                value.value = '';
            };
            return { args, value, clear };
        },
        template: `
        <InputGroup>
            <Button icon="pi pi-envelope" :outlined="true" />
            <InputText v-model="value" />
        </InputGroup>
       `,
    }),
};
