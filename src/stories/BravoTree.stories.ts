import type { Meta, StoryObj } from '@storybook/vue3';
import BravoTree from '../components/BravoTree.vue';
import { ref } from 'vue';

interface BravoTreeProps {
    value?: any[];
    expandedKeys?: object;
    selectionKeys?: object;
    selectionMode?: 'single' | 'multiple' | 'checkbox';
    metaKeySelection?: boolean;
    loading?: boolean;
    loadingIcon?: string;
    filter?: boolean;
    filterMode?: 'lenient' | 'strict';
}

const meta = {
    title: 'Data/Tree',
    component: BravoTree,
    tags: ['autodocs'],
    argTypes: {
        value: { control: 'object' },
        expandedKeys: { control: 'object' },
        selectionKeys: { control: 'object' },
        selectionMode: { control: 'select', options: ['single', 'multiple', 'checkbox'] },
        metaKeySelection: { control: 'boolean' },
        loading: { control: 'boolean' },
        loadingIcon: { control: 'text' },
        filter: { control: 'boolean' },
        filterMode: { control: 'select', options: ['lenient', 'strict'] },
    },
} as Meta<typeof BravoTree>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoTree :value="nodes" />
</template>

<script setup>
import { ref } from 'vue';

const nodes = ref([
  {
    key: '0',
    label: 'Documents',
    children: [
      {
        key: '0-0',
        label: 'Work',
        children: [
          { key: '0-0-0', label: 'Expenses.doc' },
          { key: '0-0-1', label: 'Resume.doc' }
        ]
      },
      {
        key: '0-1',
        label: 'Home',
        children: [
          { key: '0-1-0', label: 'Invoices.txt' }
        ]
      }
    ]
  },
  {
    key: '1',
    label: 'Pictures',
    children: [
      { key: '1-0', label: 'barcelona.jpg' },
      { key: '1-1', label: 'logo.jpg' },
      { key: '1-2', label: 'primeui.png' }
    ]
  }
]);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoTree },
        setup() {
            const nodes = ref([
                {
                    key: '0',
                    label: 'Documents',
                    children: [
                        {
                            key: '0-0',
                            label: 'Work',
                            children: [
                                { key: '0-0-0', label: 'Expenses.doc' },
                                { key: '0-0-1', label: 'Resume.doc' },
                            ],
                        },
                        {
                            key: '0-1',
                            label: 'Home',
                            children: [{ key: '0-1-0', label: 'Invoices.txt' }],
                        },
                    ],
                },
                {
                    key: '1',
                    label: 'Pictures',
                    children: [
                        { key: '1-0', label: 'barcelona.jpg' },
                        { key: '1-1', label: 'logo.jpg' },
                        { key: '1-2', label: 'primeui.png' },
                    ],
                },
            ]);

            return { nodes };
        },
        template: `
      <BravoTree :value="nodes" />
    `,
    }),
};

// Selection story
export const Selection: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoTree
    :value="nodes"
    selectionMode="checkbox"
    v-model:selectionKeys="selectedKeys"
    :filter="true"
  />
</template>

<script setup>
import { ref } from 'vue';

const nodes = ref([
  {
    key: '0',
    label: 'Documents',
    children: [
      {
        key: '0-0',
        label: 'Work',
        children: [
          { key: '0-0-0', label: 'Expenses.doc' },
          { key: '0-0-1', label: 'Resume.doc' }
        ]
      },
      {
        key: '0-1',
        label: 'Home',
        children: [
          { key: '0-1-0', label: 'Invoices.txt' }
        ]
      }
    ]
  },
  {
    key: '1',
    label: 'Pictures',
    children: [
      { key: '1-0', label: 'barcelona.jpg' },
      { key: '1-1', label: 'logo.jpg' },
      { key: '1-2', label: 'primeui.png' }
    ]
  }
]);

const selectedKeys = ref({});
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoTree },
        setup() {
            const nodes = ref([
                {
                    key: '0',
                    label: 'Documents',
                    children: [
                        {
                            key: '0-0',
                            label: 'Work',
                            children: [
                                { key: '0-0-0', label: 'Expenses.doc' },
                                { key: '0-0-1', label: 'Resume.doc' },
                            ],
                        },
                        {
                            key: '0-1',
                            label: 'Home',
                            children: [{ key: '0-1-0', label: 'Invoices.txt' }],
                        },
                    ],
                },
                {
                    key: '1',
                    label: 'Pictures',
                    children: [
                        { key: '1-0', label: 'barcelona.jpg' },
                        { key: '1-1', label: 'logo.jpg' },
                        { key: '1-2', label: 'primeui.png' },
                    ],
                },
            ]);

            const selectedKeys = ref({});

            return { nodes, selectedKeys };
        },
        template: `
      <BravoTree
        :value="nodes"
        selectionMode="checkbox"
        v-model:selectionKeys="selectedKeys"
        :filter="true"
      />
    `,
    }),
};
