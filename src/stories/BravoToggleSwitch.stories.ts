import type { Meta, StoryObj } from '@storybook/vue3';
import BravoToggleSwitch from '../components/BravoToggleSwitch.vue';
import { ref } from 'vue';

const meta = {
    title: 'Form/ToggleSwitch',
    component: BravoToggleSwitch,
    tags: ['autodocs'],
    argTypes: {
        modelValue: { control: 'boolean' },
        disabled: { control: 'boolean' },
    },
} satisfies Meta<typeof BravoToggleSwitch>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoToggleSwitch v-model="checked" />
</template>

<script setup>
import { ref } from 'vue';

const checked = ref(false);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoToggleSwitch },
        setup() {
            const checked = ref(false);
            return { checked };
        },
        template: `
      <BravoToggleSwitch
        v-model="checked"
      />
    `,
    }),
};

export const Disabled: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoToggleSwitch v-model="checked" disabled />
</template>

<script setup>
import { ref } from 'vue';

const checked = ref(true);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoToggleSwitch },
        setup() {
            const checked = ref(true);
            return { checked };
        },
        template: `
      <BravoToggleSwitch
        v-model="checked"
        disabled
      />
    `,
    }),
};
