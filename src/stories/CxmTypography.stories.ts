import type { Meta, StoryObj } from '@storybook/vue3';
import Caption1 from '../components/BravoTypography/BravoCaption1.vue';
import Caption2 from '../components/BravoTypography/BravoCaption2.vue';
import Caption3 from '../components/BravoTypography/BravoCaption3.vue';
import BravoBodyBold from '../components/BravoTypography/BravoBodyBold.vue';
import BodyUnderline from '../components/BravoTypography/BravoBodyUnderline.vue';
import Paragraph from '../components/BravoTypography/BravoParagraph.vue';
import Subhead from '../components/BravoTypography/BravoSubhead.vue';
import Headline from '../components/BravoTypography/BravoHeadline.vue';
import LargeTitle from '../components/BravoTypography/BravoLargeTitle.vue';
import Body from '../components/BravoTypography/BravoBody.vue';
import Title3 from '../components/BravoTypography/BravoTitle3.vue';
import SmallCaption from '../components/BravoTypography/BravoSmallCaption.vue';
import { BravoTypography } from '../components/BravoTypography';

const meta = {
    title: 'Legacy & Custom/Typography',
    tags: ['autodocs'],
    parameters: {
        layout: 'centered',
    },
    decorators: [() => ({ template: '<div style="padding: 20px;"><story/></div>' })],
} satisfies Meta;

export default meta;
type Story = StoryObj<typeof meta>;

export const AllTypography: Story = {
    render: () => ({
        components: {
            LargeTitle,
            Headline,
            Title3,
            Subhead,
            Body,
            BravoBodyBold,
            BodyUnderline,
            Paragraph,
            Caption1,
            Caption2,
            Caption3,
            SmallCaption,
        },
        template: `
      <div style="display: flex; flex-direction: column; gap: 24px;">
        <div>
          <Paragraph style="margin-bottom: 8px;">Large Title</Paragraph>
          <LargeTitle>This is Large Title Text</LargeTitle>
        </div>

        <div>
          <Paragraph style="margin-bottom: 8px;">Headline</Paragraph>
          <Headline>This is Headline Text</Headline>
        </div>

        <div>
          <Paragraph style="margin-bottom: 8px;">Title 3</Paragraph>
          <Title3>This is Title 3 Text</Title3>
        </div>

        <div>
          <Paragraph style="margin-bottom: 8px;">Subhead</Paragraph>
          <Subhead>This is Subhead Text</Subhead>
        </div>

        <div>
          <Paragraph style="margin-bottom: 8px;">Body</Paragraph>
          <Body>This is Body Text</Body>
        </div>

        <div>
          <Paragraph style="margin-bottom: 8px;">Body Bold (13px, 700)</Paragraph>
          <BravoBodyBold>This is Body Bold Text</BravoBodyBold>
        </div>
        
        <div>
          <Paragraph style="margin-bottom: 8px;">Body Underline (13px, 500, underlined)</Paragraph>
          <BodyUnderline>This is Body Underline Text</BodyUnderline>
        </div>
        
        <div>
          <Paragraph style="margin-bottom: 8px;">Paragraph (13px, 400)</Paragraph>
          <Paragraph>This is Paragraph Text</Paragraph>
        </div>

        <div>
          <Paragraph style="margin-bottom: 8px;">Caption 1 (12px, 500)</Paragraph>
          <Caption1>This is Caption 1 Text</Caption1>
        </div>
        
        <div>
          <Paragraph style="margin-bottom: 8px;">Caption 2 (12px, 400)</Paragraph>
          <Caption2>This is Caption 2 Text</Caption2>
        </div>

        <div>
          <Paragraph style="margin-bottom: 8px;">Caption 3 (12px, 500, uppercase)</Paragraph>
          <Caption3>This is Caption 3 Text</Caption3>
        </div>

        <div>
          <Paragraph style="margin-bottom: 8px;">Small Caption</Paragraph>
          <SmallCaption>This is Small Caption Text</SmallCaption>
        </div>
      </div>
    `,
    }),
};

// Add individual stories for new components
export const LargeTitleText: Story = {
    render: () => ({
        components: { LargeTitle },
        template: '<LargeTitle>Large Title Example Text</LargeTitle>',
    }),
};

export const HeadlineText: Story = {
    render: () => ({
        components: { Headline },
        template: '<Headline>Headline Example Text</Headline>',
    }),
};

export const Title3Text: Story = {
    render: () => ({
        components: { Title3 },
        template: '<Title3>Title 3 Example Text</Title3>',
    }),
};

export const SubheadText: Story = {
    render: () => ({
        components: { Subhead },
        template: '<Subhead>Subhead Example Text</Subhead>',
    }),
};

export const BodyText: Story = {
    render: () => ({
        components: { Body },
        template: '<Body>Body Example Text</Body>',
    }),
};

export const SmallCaptionText: Story = {
    render: () => ({
        components: { SmallCaption },
        template: '<SmallCaption>Small Caption Example Text</SmallCaption>',
    }),
};

// Individual component stories
export const CaptionThree: Story = {
    render: () => ({
        components: { Caption3 },
        template: '<Caption3>CAPTION 3 EXAMPLE TEXT</Caption3>',
    }),
};

export const CaptionOne: Story = {
    render: () => ({
        components: { Caption1 },
        template: '<Caption1>Caption 1 Example Text</Caption1>',
    }),
};

export const CaptionTwo: Story = {
    render: () => ({
        components: { Caption2 },
        template: '<Caption2>Caption 2 Example Text</Caption2>',
    }),
};

export const BoldText: Story = {
    render: () => ({
        components: { BravoBodyBold },
        template: '<BravoBodyBold>Body Bold Example Text</BravoBodyBold>',
    }),
};

export const UnderlinedText: Story = {
    render: () => ({
        components: { BodyUnderline },
        template: '<BodyUnderline>Body Underline Example Text</BodyUnderline>',
    }),
};

export const ParagraphText: Story = {
    render: () => ({
        components: { Paragraph },
        template: '<Paragraph>Paragraph Example Text</Paragraph>',
    }),
};

export const GenericTypography: Story = {
    render: () => ({
        components: { BravoTypography },
        template: `
      <div style="display: flex; flex-direction: column; gap: 16px;">
        <BravoTypography variant="largeTitle">This is using the generic component - Large Title</BravoTypography>
        <BravoTypography variant="bodyBold">This is using the generic component - Body Bold</BravoTypography>
        <BravoTypography variant="caption3">THIS IS USING THE GENERIC COMPONENT - CAPTION 3</BravoTypography>
      </div>
    `,
    }),
};

export const DynamicTypography: Story = {
    render: () => ({
        components: { BravoTypography },
        data() {
            return {
                currentVariant: 'title1' as const,
            };
        },
        template: `
      <div style="display: flex; flex-direction: column; gap: 16px;">
        <select v-model="currentVariant">
          <option value="title1">Title 1</option>
          <option value="body">Body</option>
          <option value="caption1">Caption 1</option>
          <option value="headline">Headline</option>
        </select>
        <BravoTypography :variant="currentVariant">
          This text will change style based on the selected variant
        </BravoTypography>
      </div>
    `,
    }),
};
