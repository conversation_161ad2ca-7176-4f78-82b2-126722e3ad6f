import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import BravoMenubar from '../components/BravoMenubar.vue';

interface MenubarItem {
    label: string;
    icon?: string;
    items?: MenubarItem[];
    command?: () => void;
    url?: string;
    disabled?: boolean;
}

const meta = {
    title: 'Menu/Menubar',
    component: BravoMenubar,
    tags: ['autodocs'],
    argTypes: {
        model: { control: 'object' },
        pt: { control: 'object' },
    },
} satisfies Meta<typeof BravoMenubar>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    args: {
        model: [
            {
                label: 'File',
                icon: 'pi pi-fw pi-file',
                items: [
                    {
                        label: 'New',
                        icon: 'pi pi-fw pi-plus',
                        items: [
                            { label: 'Document', icon: 'pi pi-fw pi-file' },
                            { label: 'Spreadsheet', icon: 'pi pi-fw pi-file-excel' },
                        ],
                    },
                    { label: 'Open', icon: 'pi pi-fw pi-folder-open' },
                    { label: 'Save', icon: 'pi pi-fw pi-save' },
                ],
            },
            {
                label: 'Edit',
                icon: 'pi pi-fw pi-pencil',
                items: [
                    { label: 'Cut', icon: 'pi pi-fw pi-cut' },
                    { label: 'Copy', icon: 'pi pi-fw pi-copy' },
                    { label: 'Paste', icon: 'pi pi-fw pi-paste' },
                ],
            },
            {
                label: 'Help',
                icon: 'pi pi-fw pi-question-circle',
                items: [
                    { label: 'Documentation', icon: 'pi pi-fw pi-file' },
                    { label: 'About', icon: 'pi pi-fw pi-info-circle' },
                ],
            },
        ],
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <Menubar :model="model" />
</template>
`,
            },
        },
        layout: 'padded',
        viewport: {
            defaultViewport: 'desktop',
        },
    },
    render: (args) => ({
        components: { BravoMenubar },
        setup() {
            return { args };
        },
        template: '<div style="padding: 20px; min-height: 300px;"><BravoMenubar v-bind="args" /></div>',
    }),
};

// With Custom Start and End Templates
export const WithTemplates: Story = {
    args: {
        model: [
            {
                label: 'File',
                icon: 'pi pi-fw pi-file',
                items: [
                    { label: 'New', icon: 'pi pi-fw pi-plus' },
                    { label: 'Open', icon: 'pi pi-fw pi-folder-open' },
                ],
            },
            {
                label: 'Edit',
                icon: 'pi pi-fw pi-pencil',
                items: [
                    { label: 'Cut', icon: 'pi pi-fw pi-cut' },
                    { label: 'Copy', icon: 'pi pi-fw pi-copy' },
                ],
            },
        ],
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <Menubar :model="model">
    <template #start>
      <img alt="logo" src="https://primefaces.org/cdn/primevue/images/logo.svg" height="40" class="mr-2" />
    </template>
    <template #end>
      <InputText placeholder="Search" type="text" />
    </template>
  </Menubar>
</template>
`,
            },
        },
        layout: 'padded',
        viewport: {
            defaultViewport: 'desktop',
        },
    },
    render: (args) => ({
        components: { BravoMenubar },
        template: `
      <div style="padding: 20px; min-height: 300px;">
        <BravoMenubar v-bind="args">
          <template #start>
            <img alt="logo" src="https://primefaces.org/cdn/primevue/images/logo.svg" height="40" class="mr-2" />
          </template>
          <template #end>
            <input placeholder="Search" type="text" class="p-inputtext" />
          </template>
        </BravoMenubar>
      </div>
    `,
    }),
};
