import type { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import BravoTieredMenu from '../components/BravoTieredMenu.vue';
import Button from 'primevue/button';

interface TieredMenuItem {
    label: string;
    icon?: string;
    items?: TieredMenuItem[];
    command?: () => void;
    url?: string;
    disabled?: boolean;
}

const meta = {
    title: 'Menu/TieredMenu',
    component: BravoTieredMenu,
    tags: ['autodocs'],
    argTypes: {
        model: { control: 'object' },
        popup: { control: 'boolean' },
        pt: { control: 'object' },
    },
} satisfies Meta<typeof BravoTieredMenu>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    args: {
        model: [
            {
                label: 'File',
                icon: 'pi pi-fw pi-file',
                items: [
                    {
                        label: 'New',
                        icon: 'pi pi-fw pi-plus',
                        items: [
                            { label: 'Document', icon: 'pi pi-fw pi-file' },
                            { label: 'Spreadsheet', icon: 'pi pi-fw pi-file-excel' },
                        ],
                    },
                    { label: 'Open', icon: 'pi pi-fw pi-folder-open' },
                    { label: 'Save', icon: 'pi pi-fw pi-save' },
                ],
            },
            {
                label: 'Edit',
                icon: 'pi pi-fw pi-pencil',
                items: [
                    { label: 'Cut', icon: 'pi pi-fw pi-cut' },
                    { label: 'Copy', icon: 'pi pi-fw pi-copy' },
                    { label: 'Paste', icon: 'pi pi-fw pi-paste' },
                ],
            },
        ],
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <TieredMenu :model="model" />
</template>
`,
            },
        },
        layout: 'padded',
        viewport: {
            defaultViewport: 'desktop',
        },
    },
    render: (args) => ({
        components: { BravoTieredMenu },
        setup() {
            return { args };
        },
        template: '<div style="padding: 20px;"><BravoTieredMenu v-bind="args" /></div>',
    }),
};

// Popup Menu
export const Popup: Story = {
    args: {
        popup: true,
        model: [
            {
                label: 'File',
                icon: 'pi pi-fw pi-file',
                items: [
                    { label: 'New', icon: 'pi pi-fw pi-plus' },
                    { label: 'Open', icon: 'pi pi-fw pi-folder-open' },
                    { label: 'Save', icon: 'pi pi-fw pi-save' },
                ],
            },
            {
                label: 'Edit',
                icon: 'pi pi-fw pi-pencil',
                items: [
                    { label: 'Cut', icon: 'pi pi-fw pi-cut' },
                    { label: 'Copy', icon: 'pi pi-fw pi-copy' },
                    { label: 'Paste', icon: 'pi pi-fw pi-paste' },
                ],
            },
        ],
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <Button type="button" label="Show" @click="toggle" />
  <TieredMenu ref="menu" :model="model" popup />
</template>
`,
            },
        },
        layout: 'padded',
        viewport: {
            defaultViewport: 'desktop',
        },
    },
    render: (args) => ({
        components: { BravoTieredMenu, Button },
        setup() {
            const menu = ref();

            const toggle = (event: Event) => {
                menu.value?.toggle(event);
            };

            return { args, menu, toggle };
        },
        template: `
      <div style="padding: 20px;">
        <Button type="button" label="Show" @click="toggle" />
        <BravoTieredMenu ref="menu" v-bind="args" />
      </div>
    `,
    }),
};
