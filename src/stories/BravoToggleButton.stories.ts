import type { Meta, StoryObj } from '@storybook/vue3';
import BravoToggleButton from '../components/BravoToggleButton.vue';
import { ref } from 'vue';

const meta = {
    title: 'Form/ToggleButton',
    component: BravoToggleButton,
    tags: ['autodocs'],
    argTypes: {
        modelValue: { control: 'boolean' },
        onLabel: { control: 'text' },
        offLabel: { control: 'text' },
        onIcon: { control: 'text' },
        offIcon: { control: 'text' },
    },
} satisfies Meta<typeof BravoToggleButton>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoToggleButton
    v-model="checked"
    onLabel="On"
    offLabel="Off"
  />
</template>

<script setup>
import { ref } from 'vue';

const checked = ref(false);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoToggleButton },
        setup() {
            const checked = ref(false);
            return { checked };
        },
        template: `
      <BravoToggleButton
        v-model="checked"
        onLabel="On"
        offLabel="Off"
      />
    `,
    }),
};

export const WithIcons: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoToggleButton
    v-model="checked"
    onLabel="Yes"
    offLabel="No"
    onIcon="pi pi-check"
    offIcon="pi pi-times"
  />
</template>

<script setup>
import { ref } from 'vue';

const checked = ref(false);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoToggleButton },
        setup() {
            const checked = ref(false);
            return { checked };
        },
        template: `
      <BravoToggleButton
        v-model="checked"
        onLabel="Yes"
        offLabel="No"
        onIcon="pi pi-check"
        offIcon="pi pi-times"
      />
    `,
    }),
};
