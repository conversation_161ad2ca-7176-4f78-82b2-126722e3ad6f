import { Meta, StoryObj } from '@storybook/vue3';
import { CxmLoaderGrid } from '../components';

type BaseLoaderGridProps = {
    isLoading?: boolean;
};

const defaultLoaderGrid: BaseLoaderGridProps = {
    isLoading: true,
};

export default {
    component: CxmLoaderGrid,
    title: 'components/Loaders/LoaderGrid',
    tags: ['autodocs'],
    parameters: {
        docs: {
            description: {
                component: `LoaderGrid is static today but will be customizable in the future.`,
            },
        },
        layout: 'fullscreen',
    },
} as Meta<typeof CxmLoaderGrid>;

type Story = StoryObj<typeof CxmLoaderGrid>;

export const Default: Story = {
    args: { isLoading: defaultLoaderGrid.isLoading },
};
