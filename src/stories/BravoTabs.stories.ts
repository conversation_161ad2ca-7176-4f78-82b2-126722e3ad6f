import type { Meta, StoryObj } from '@storybook/vue3';
import BravoTabs from '../components/BravoTabs.vue';
import BravoTabList from '../components/BravoTabList.vue';
import BravoTab from '../components/BravoTab.vue';
import BravoTabPanels from '../components/BravoTabPanels.vue';
import BravoTabPanel from '../components/BravoTabPanel.vue';

const meta = {
    title: 'Panel/Tabs',
    component: BravoTabs,
    tags: ['autodocs'],
    argTypes: {
        value: { control: 'text' },
        scrollable: { control: 'boolean' },
    },
} satisfies Meta<typeof BravoTabs>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    args: {
        value: "0",
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoTabs value="0">
    <BravoTabList>
      <BravoTab value="0">Header I</BravoTab>
      <BravoTab value="1">Header II</BravoTab>
      <BravoTab value="2">Header III</BravoTab>
    </BravoTabList>
    <BravoTabPanels>
      <BravoTabPanel value="0">
        <p class="m-0">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        </p>
      </BravoTabPanel>
      <BravoTabPanel value="1">
        <p class="m-0">
          Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.
        </p>
      </BravoTabPanel>
      <BravoTabPanel value="2">
        <p class="m-0">
          At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum.
        </p>
      </BravoTabPanel>
    </BravoTabPanels>
  </BravoTabs>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoTabs, BravoTabList, BravoTab, BravoTabPanels, BravoTabPanel },
        setup() {
            return { args };
        },
        template: `
      <BravoTabs v-bind="args">
        <BravoTabList>
          <BravoTab value="0">Header I</BravoTab>
          <BravoTab value="1">Header II</BravoTab>
          <BravoTab value="2">Header III</BravoTab>
        </BravoTabList>
        <BravoTabPanels>
          <BravoTabPanel value="0">
            <p class="m-0">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </p>
          </BravoTabPanel>
          <BravoTabPanel value="1">
            <p class="m-0">
              Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.
            </p>
          </BravoTabPanel>
          <BravoTabPanel value="2">
            <p class="m-0">
              At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum.
            </p>
          </BravoTabPanel>
        </BravoTabPanels>
      </BravoTabs>
    `,
    }),
};

// Scrollable story
export const Scrollable: Story = {
    args: {
        value: "0",
        scrollable: true,
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoTabs value="0" scrollable>
    <BravoTabList>
      <BravoTab v-for="n in 20" :key="n" :value="String(n-1)">Tab {{n}}</BravoTab>
    </BravoTabList>
    <BravoTabPanels>
      <BravoTabPanel v-for="n in 20" :key="n" :value="String(n-1)">
        <p class="m-0">Tab {{n}} Content</p>
      </BravoTabPanel>
    </BravoTabPanels>
  </BravoTabs>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoTabs, BravoTabList, BravoTab, BravoTabPanels, BravoTabPanel },
        setup() {
            return { args };
        },
        template: `
      <BravoTabs v-bind="args">
        <BravoTabList>
          <BravoTab v-for="n in 20" :key="n" :value="String(n-1)">Tab {{n}}</BravoTab>
        </BravoTabList>
        <BravoTabPanels>
          <BravoTabPanel v-for="n in 20" :key="n" :value="String(n-1)">
            <p class="m-0">Tab {{n}} Content</p>
          </BravoTabPanel>
        </BravoTabPanels>
      </BravoTabs>
    `,
    }),
};
