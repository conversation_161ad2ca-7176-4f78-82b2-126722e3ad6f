import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3';
import BravoCard from '../components/BravoCard.vue';
import BravoButton from '../components/BravoButton.vue';

const meta = {
    title: 'Panel/Card',
    component: BravoCard,
    tags: ['autodocs'],
    argTypes: {
        header: { control: 'text' },
        title: { control: 'text' },
        subtitle: { control: 'text' },
        pt: { control: 'object' },
    },
} satisfies Meta<typeof BravoCard>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <Card>
    <template #title>Simple Card</template>
    <template #content>
      Lorem ipsum dolor sit amet, consectetur adipisicing elit. Inventore sed consequuntur error repudiandae 
      numquam deserunt quisquam repellat libero asperiores earum nam nobis, culpa ratione quam perferendis esse, 
      cupiditate neque quas!
    </template>
  </BravoCard>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoCard },
        template: `
      <BravoCard v-bind="args">
        <template #title>Simple Card</template>
        <template #content>
          <p class="m-0">
            Lorem ipsum dolor sit amet, consectetur adipisicing elit. Inventore sed consequuntur error repudiandae 
            numquam deserunt quisquam repellat libero asperiores earum nam nobis, culpa ratione quam perferendis esse, 
            cupiditate neque quas!
          </p>
        </template>
      </BravoCard>
    `,
    }),
};

// Advanced story with header image and footer
export const Advanced: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <Card style="width: 25rem">
    <template #header>
      <img alt="user header" src="demo/images/usercard.png" />
    </template>
    <template #title>Advanced Card</template>
    <template #subtitle>Card subtitle</template>
    <template #content>
      Lorem ipsum dolor sit amet, consectetur adipisicing elit...
    </template>
    <template #footer>
      <Button label="Save" icon="pi pi-check" />
      <Button label="Cancel" severity="secondary" icon="pi pi-times" style="margin-left: .5em" />
    </template>
  </Card>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoCard, BravoButton },
        template: `
      <BravoCard style="width: 25rem" v-bind="args">
        <template #header>
          <img alt="user header" src="https://primefaces.org/cdn/primevue/images/usercard.png" />
        </template>
        <template #title>Advanced Card</template>
        <template #subtitle>Card subtitle</template>
        <template #content>
          <p class="m-0">
            Lorem ipsum dolor sit amet, consectetur adipisicing elit. Inventore sed consequuntur error repudiandae 
            numquam deserunt quisquam repellat libero asperiores earum nam nobis, culpa ratione quam perferendis esse, 
            cupiditate neque quas!
          </p>
        </template>
        <template #footer>
          <div class="flex gap-2">
            <Button label="Save" icon="pi pi-check" />
            <Button label="Cancel" severity="secondary" icon="pi pi-times" />
          </div>
        </template>
      </BravoCard>
    `,
    }),
};
