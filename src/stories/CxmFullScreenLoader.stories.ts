import { Meta, StoryObj } from '@storybook/vue3';
import { CxmFullScreenLoader } from '../components';

type BaseFullScreenLoaderProps = {
    isLoading?: boolean;
};

const defaultFullScreenLoader: BaseFullScreenLoaderProps = {
    isLoading: true,
};

export default {
    component: CxmFullScreenLoader,
    title: 'Legacy & Custom/Loaders/FullScreenLoader',
    tags: ['autodocs'],
    parameters: {
        docs: {
            description: {
                component: `FullScreenLoader can be sized with CSS height and width properties.`,
            },
        },
        layout: 'fullscreen',
    },
} as Meta<typeof CxmFullScreenLoader>;

type Story = StoryObj<typeof CxmFullScreenLoader>;

export const Default: Story = {
    args: { isLoading: defaultFullScreenLoader.isLoading },
};
