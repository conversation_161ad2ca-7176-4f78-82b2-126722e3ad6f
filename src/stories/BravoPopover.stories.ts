import type { Meta, StoryObj } from '@storybook/vue3';
import BravoPopover from '../components/BravoPopover.vue';
import BravoButton from '../components/BravoButton.vue';
import { ref } from 'vue';

const meta = {
    title: 'Overlay/Popover',
    component: BravoPopover,
    tags: ['autodocs'],
    argTypes: {
        visible: { control: 'boolean' },
        target: { control: 'object' },
        dismissable: { control: 'boolean' },
        showCloseIcon: { control: 'boolean' },
        modal: { control: 'boolean' },
        appendTo: { control: 'text' },
        baseZIndex: { control: 'number' },
        autoZIndex: { control: 'boolean' },
        scrollHeight: { control: 'text' },
    },
} satisfies Meta<typeof BravoPopover>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Popover
export const Basic: Story = {
    render: (args) => ({
        components: { BravoPopover, BravoButton },
        setup() {
            const popover = ref();

            const togglePopover = (event: MouseEvent) => {
                popover.value.toggle(event);
            };

            return { popover, togglePopover };
        },
        template: `
      <div class="card flex justify-content-center">
        <BravoButton type="button" label="Toggle" @click="togglePopover" />
        <BravoPopover ref="popover">
          <div class="p-3">
            <h5>Basic Popover</h5>
            <p class="m-0">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </p>
          </div>
        </BravoPopover>
      </div>
    `,
    }),
};

// Mouse Tracking
export const MouseTracking: Story = {
    render: (args) => ({
        components: { BravoPopover, BravoButton },
        setup() {
            const popover = ref();
            const visible = ref(false);

            const showPopover = (event: MouseEvent) => {
                popover.value.show(event);
            };

            const hidePopover = () => {
                if (popover.value) {
                    popover.value.hide();
                }
            };

            return { popover, visible, showPopover, hidePopover };
        },
        template: `
      <div class="card flex justify-content-center">
        <BravoButton type="button" label="Hover" @mouseover="showPopover" @mouseleave="hidePopover" />
        <BravoPopover ref="popover" v-model:visible="visible">
          <div class="p-3">
            <h5>Mouse Tracking</h5>
            <p class="m-0">Popover will follow the mouse cursor.</p>
          </div>
        </BravoPopover>
      </div>
    `,
    }),
};

// Close Button
export const CloseButton: Story = {
    render: (args) => ({
        components: { BravoPopover, BravoButton },
        setup() {
            const popover = ref();

            const showPopover = (event: MouseEvent) => {
                popover.value.toggle(event);
            };

            return { popover, showPopover };
        },
        template: `
      <div class="card flex justify-content-center">
        <BravoButton type="button" label="Show" @click="showPopover" />
        <BravoPopover ref="popover" :showCloseIcon="true">
          <div class="p-3">
            <h5>Dismissible Popover</h5>
            <p class="m-0">Click the close icon to hide.</p>
          </div>
        </BravoPopover>
      </div>
    `,
    }),
};

// Menu Example
export const MenuExample: Story = {
    render: (args) => ({
        components: { BravoPopover, BravoButton },
        setup() {
            const popover = ref();

            const toggleMenu = (event: MouseEvent) => {
                popover.value.toggle(event);
            };

            const handleMenuClick = (action: string) => {
                alert(`Action selected: ${action}`);
                popover.value.toggle(); // Close the menu after selection
            };

            return { popover, toggleMenu, handleMenuClick };
        },
        template: `
      <div class="card flex justify-content-center">
        <BravoButton type="button" label="Show Menu" icon="pi pi-bars" @click="toggleMenu" />
        <BravoPopover ref="popover">
          <div class="p-3" style="min-width: 200px;">
            <div class="menu-item" @click="handleMenuClick('Edit')" style="padding: 8px; cursor: pointer; border-bottom: 1px solid #eee;">
              <i class="pi pi-pencil" style="margin-right: 8px;"></i> Edit
            </div>
            <div class="menu-item" @click="handleMenuClick('Delete')" style="padding: 8px; cursor: pointer; border-bottom: 1px solid #eee;">
              <i class="pi pi-trash" style="margin-right: 8px;"></i> Delete
            </div>
            <div class="menu-item" @click="handleMenuClick('Share')" style="padding: 8px; cursor: pointer; border-bottom: 1px solid #eee;">
              <i class="pi pi-share-alt" style="margin-right: 8px;"></i> Share
            </div>
            <div class="menu-item" @click="handleMenuClick('Download')" style="padding: 8px; cursor: pointer;">
              <i class="pi pi-download" style="margin-right: 8px;"></i> Download
            </div>
          </div>
        </BravoPopover>
      </div>
    `,
    }),
};

// Target Prop Example
export const WithTargetProp: Story = {
    render: () => ({
        components: { BravoPopover, BravoButton },
        setup() {
            const popover = ref();
            const targetButton = ref();
            const targetElement = ref();

            const showPopover = (event: MouseEvent) => {
                // Show popover using the target prop element
                if (popover.value && targetElement.value) {
                    popover.value.show(event);
                }
            };

            return { popover, targetButton, targetElement, showPopover };
        },
        template: `
      <div class="card flex justify-content-center gap-4">
        <BravoButton ref="targetButton" type="button" label="Target Button" />
        <BravoButton type="button" label="Trigger Popover" @click="showPopover" />
        <BravoPopover ref="popover" :target="targetButton?.$el">
          <div class="p-3">
            <h5>Target Prop Example</h5>
            <p class="m-0">
              This popover is anchored to the "Target Button" using the target prop,
              but triggered by the "Trigger Popover" button.
            </p>
          </div>
        </BravoPopover>
      </div>
    `,
    }),
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="card flex justify-content-center gap-4">
    <BravoButton ref="targetButton" type="button" label="Target Button" />
    <BravoButton type="button" label="Trigger Popover" @click="showPopover" />
    <BravoPopover ref="popover" :target="targetButton?.$el">
      <div class="p-3">
        <h5>Target Prop Example</h5>
        <p class="m-0">
          This popover is anchored to the "Target Button" using the target prop,
          but triggered by the "Trigger Popover" button.
        </p>
      </div>
    </BravoPopover>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import BravoPopover from '../components/BravoPopover.vue';
import BravoButton from '../components/BravoButton.vue';

const popover = ref();
const targetButton = ref();

const showPopover = (event) => {
  if (popover.value && targetButton.value) {
    popover.value.show(event);
  }
};
</script>
                `,
            },
        },
    },
};

// Override Target Example
export const OverrideTarget: Story = {
    render: () => ({
        components: { BravoPopover, BravoButton },
        setup() {
            const popover = ref();
            const defaultTarget = ref();
            const overrideTarget = ref();

            const showAtDefault = (event: MouseEvent) => {
                // Uses the target prop (defaultTarget.$el)
                popover.value.toggle(event);
            };

            const showAtOverride = (event: MouseEvent) => {
                // Overrides the target prop with overrideTarget.$el
                popover.value.toggle(event, overrideTarget.value?.$el);
            };

            return { popover, defaultTarget, overrideTarget, showAtDefault, showAtOverride };
        },
        template: `
      <div class="card flex justify-content-center gap-4">
        <BravoButton ref="defaultTarget" type="button" label="Default Target" @click="showAtDefault" />
        <BravoButton ref="overrideTarget" type="button" label="Override Target" @click="showAtOverride" />
        <BravoPopover ref="popover" :target="defaultTarget?.$el">
          <div class="p-3">
            <h5>Target Override Example</h5>
            <p class="m-0">
              Click "Default Target" to anchor here, or "Override Target" to anchor there.
              The override target parameter takes precedence over the target prop.
            </p>
          </div>
        </BravoPopover>
      </div>
    `,
    }),
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="card flex justify-content-center gap-4">
    <BravoButton ref="defaultTarget" type="button" label="Default Target" @click="showAtDefault" />
    <BravoButton ref="overrideTarget" type="button" label="Override Target" @click="showAtOverride" />
    <BravoPopover ref="popover" :target="defaultTarget?.$el">
      <div class="p-3">
        <h5>Target Override Example</h5>
        <p class="m-0">
          Click "Default Target" to anchor here, or "Override Target" to anchor there.
          The override target parameter takes precedence over the target prop.
        </p>
      </div>
    </BravoPopover>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import BravoPopover from '../components/BravoPopover.vue';
import BravoButton from '../components/BravoButton.vue';

const popover = ref();
const defaultTarget = ref();
const overrideTarget = ref();

const showAtDefault = (event) => {
  popover.value.toggle(event);
};

const showAtOverride = (event) => {
  popover.value.toggle(event, overrideTarget.value?.$el);
};
</script>
                `,
            },
        },
    },
};
