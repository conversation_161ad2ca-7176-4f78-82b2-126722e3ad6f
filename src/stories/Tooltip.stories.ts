import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';

// The tooltip is a directive, not a component
// We're creating stories for the v-tooltip directive
const meta = {
    title: 'Overlay/Tooltip',
    tags: ['autodocs'],
    parameters: {
        docs: {
            description: {
                component: 'PrimeVue Tooltip is a directive to display floating information on hover.',
            },
        },
    },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Tooltip
export const Basic: Story = {
    render: (args) => ({
        components: { Button },
        template: `
      <div class="card flex justify-content-center">
        <Button 
          label="Save" 
          icon="pi pi-check"
          v-tooltip="'Save your changes'" 
        />
      </div>
    `,
    }),
};

// Positions
export const Positions: Story = {
    render: (args) => ({
        components: { Button },
        template: `
      <div class="card flex flex-wrap gap-2 justify-content-center">
        <Button v-tooltip.top="'Top'" label="Top" class="m-1" />
        <Button v-tooltip.bottom="'Bottom'" label="Bottom" class="m-1" />
        <Button v-tooltip.left="'Left'" label="Left" class="m-1" />
        <Button v-tooltip.right="'Right'" label="Right" class="m-1" />
      </div>
    `,
    }),
};

// Auto Hide
export const AutoHide: Story = {
    render: (args) => ({
        components: { Button },
        setup() {
            const tooltipOptions = {
                value: 'Will be hidden automatically after 2 seconds',
                autoHide: true,
                hideDelay: 2000,
            };
            return { tooltipOptions };
        },
        template: `
      <div class="card flex justify-content-center">
        <Button 
          label="Auto Hide" 
          v-tooltip="tooltipOptions"
        />
      </div>
    `,
    }),
};

// Delay example
export const Delay: Story = {
    render: (args) => ({
        components: { Button },
        setup() {
            const tooltipOptions = {
                value: 'Confirm to proceed',
                showDelay: 1000,
                hideDelay: 300,
            };
            return { tooltipOptions };
        },
        template: `
      <div class="card flex justify-content-center">
        <Button 
          label="Save"
          v-tooltip="tooltipOptions"
        />
      </div>
    `,
    }),
};
