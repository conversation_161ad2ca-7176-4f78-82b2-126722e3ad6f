import type { Meta, StoryObj } from '@storybook/vue3';
import BravoProgressSpinner from '../components/BravoProgressSpinner.vue';

const meta = {
    title: 'Misc/ProgressSpinner',
    component: BravoProgressSpinner,
    tags: ['autodocs'],
    argTypes: {
        strokeWidth: { control: 'text' },
        fill: { control: 'color' },
        animationDuration: { control: 'text' },
    },
} satisfies Meta<typeof BravoProgressSpinner>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
    render: () => ({
        components: { BravoProgressSpinner },
        template: `
      <div class="card flex justify-content-center">
        <BravoProgressSpinner style="width:50px;height:50px" strokeWidth="4" animationDuration="1s"/>
      </div>
    `,
    }),
};

export const Colors: Story = {
    render: () => ({
        components: { BravoProgressSpinner },
        template: `
      <div class="card flex gap-5">
        <BravoProgressSpinner style="width:50px;height:50px" strokeWidth="4"  />
        <BravoProgressSpinner style="width:50px;height:50px" strokeWidth="4" animationDuration=".5s" />
      </div>
    `,
    }),
};
