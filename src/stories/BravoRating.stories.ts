import type { Meta, StoryObj } from '@storybook/vue3';
import BravoRating from '../components/BravoRating.vue';
import { ref } from 'vue';

interface BravoRatingProps {
    modelValue?: number;
    disabled?: boolean;
    readonly?: boolean;
    stars?: number;
    cancel?: boolean;
}

const meta = {
    title: 'Form/Rating',
    component: BravoRating,
    tags: ['autodocs'],
    argTypes: {
        modelValue: { control: 'number' },
        disabled: { control: 'boolean' },
        readonly: { control: 'boolean' },
        stars: { control: 'number' },
        cancel: { control: 'boolean' },
    },
} as Meta<typeof BravoRating>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoRating v-model="value" />
</template>

<script setup>
import { ref } from 'vue';

const value = ref(0);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoRating },
        setup() {
            const value = ref(0);
            return { value };
        },
        template: `
      <BravoRating v-model="value" />
    `,
    }),
};

// Disabled story
export const Disabled: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoRating v-model="value" :disabled="true" />
</template>

<script setup>
import { ref } from 'vue';

const value = ref(2);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoRating },
        setup() {
            const value = ref(2);
            return { value };
        },
        template: `
      <BravoRating v-model="value" :disabled="true" />
    `,
    }),
};
