import type { Meta, StoryObj } from '@storybook/vue3';
import BravoInputNumber from '../components/BravoInputNumber.vue';
import { ref } from 'vue';

// Define the props interface for InputNumber component
interface BravoInputNumberProps {
    modelValue?: number | null;
    mode?: 'decimal' | 'currency';
    currency?: string;
    locale?: string;
    suffix?: string;
    prefix?: string;
    minFractionDigits?: number;
    maxFractionDigits?: number;
    min?: number;
    max?: number;
    showButtons?: boolean;
    buttonLayout?: 'stacked' | 'horizontal' | 'vertical';
    variant?: 'outlined' | 'filled';
    size?: 'small' | 'large';
    invalid?: boolean;
    disabled?: boolean;
}

// Meta information for the component
const meta = {
    title: 'Form/InputNumber',
    component: BravoInputNumber,
    tags: ['autodocs'],
    argTypes: {
        modelValue: { control: 'number' },
        mode: { control: 'select', options: ['decimal', 'currency'] },
        currency: { control: 'text' },
        locale: { control: 'text' },
        suffix: { control: 'text' },
        prefix: { control: 'text' },
        minFractionDigits: { control: 'number' },
        maxFractionDigits: { control: 'number' },
        min: { control: 'number' },
        max: { control: 'number' },
        showButtons: { control: 'boolean' },
        buttonLayout: { control: 'select', options: ['stacked', 'horizontal', 'vertical'] },
        variant: { control: 'select', options: ['outlined', 'filled'] },
        size: { control: 'select', options: ['small', 'large'] },
        invalid: { control: 'boolean' },
        disabled: { control: 'boolean' },
    },
} satisfies Meta<typeof BravoInputNumber>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    args: {
        modelValue: 42,
    },
    render: (args) => ({
        components: { BravoInputNumber },
        setup() {
            const value = ref(args.modelValue);
            return { value };
        },
        template: `
      <div style="max-width: 300px">
        <BravoInputNumber v-model="value" />
      </div>
    `,
    }),
};

// Currency story
export const Currency: Story = {
    args: {
        modelValue: 100,
        mode: 'currency',
        currency: 'USD',
        locale: 'en-US',
    },
    render: (args) => ({
        components: { BravoInputNumber },
        setup() {
            const value = ref(args.modelValue);
            return { value };
        },
        template: `
      <div style="max-width: 300px">
        <BravoInputNumber
          v-model="value"
          mode="currency"
          currency="USD"
          locale="en-US"
        />
      </div>
    `,
    }),
};

// With Buttons story
export const WithButtons: Story = {
    args: {
        modelValue: 50,
        showButtons: true,
        min: 0,
        max: 100,
    },
    render: (args) => ({
        components: { BravoInputNumber },
        setup() {
            const value = ref(args.modelValue);
            return { value };
        },
        template: `
      <div style="max-width: 300px">
        <BravoInputNumber
          v-model="value"
          showButtons
          :min="0"
          :max="100"
        />
      </div>
    `,
    }),
};

// Prefix and Suffix story
export const PrefixSuffix: Story = {
    args: {
        modelValue: 50,
        prefix: '$',
        suffix: ' USD',
    },
    render: (args) => ({
        components: { BravoInputNumber },
        setup() {
            const value = ref(args.modelValue);
            return { value };
        },
        template: `
      <div style="max-width: 300px">
        <BravoInputNumber
          v-model="value"
          prefix="$"
          suffix=" USD"
        />
      </div>
    `,
    }),
};
