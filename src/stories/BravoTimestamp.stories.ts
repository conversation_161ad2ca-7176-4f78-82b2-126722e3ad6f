import type { Meta, StoryObj } from '@storybook/vue3';
import BravoTimestamp from '../components/BravoTimestamp.vue';

const meta = {
    component: BravoTimestamp,
    title: 'Legacy & Custom/Timestamp',
    parameters: {
        docs: {
            description: {
                component:
                    'Display a formatted timestamp relative to the current day. Adds a (time since) or (time until) suffix if date is within 30 days of the current day. Formats ISO strings and unix timestamps. ',
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        datetime: {
            control: 'date',
            description: 'ISO string or unix timestamp to format',
        },
    },
} satisfies Meta<typeof BravoTimestamp>;

export default meta;
type Story = StoryObj<typeof meta>;

// Current date for examples
const today = new Date();
const yesterday = new Date(today);
yesterday.setDate(yesterday.getDate() - 1);
const nextWeek = new Date(today);
nextWeek.setDate(nextWeek.getDate() + 7);
const lastMonth = new Date(today);
lastMonth.setMonth(lastMonth.getMonth() - 1);

export const Default: Story = {
    args: {
        datetime: '2023-03-29T21:57:55.165Z',
    },
};

export const FiveMinutesAgo: Story = {
    args: {
        datetime: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
    },
};

export const OneDayAgo: Story = {
    args: {
        datetime: yesterday.toISOString(),
    },
};

export const NearFuture: Story = {
    args: {
        datetime: nextWeek.toISOString(),
    },
};

export const DistantPast: Story = {
    args: {
        datetime: lastMonth.toISOString(),
    },
};

export const UnixTimestamp: Story = {
    args: {
        datetime: Date.now() - 86400000, // Yesterday in milliseconds
    },
};
