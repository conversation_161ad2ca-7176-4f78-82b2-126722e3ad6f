import BravoMainNavigation from '../components/BravoMainNavigation.vue';
import { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import { NavItem } from '../types/navigation';
import Tooltip from 'primevue/tooltip';
import BravoMenu from '../components/BravoMenu.vue';

// Import NavItem from the component file or a shared types file
// instead of redefining it here

export default {
    component: BravoMainNavigation,
    title: 'Navigation/MainNavigation',
    parameters: {
        docs: {
            description: {
                component:
                    'A skinny navigation bar component that displays icons for different sections of the application.',
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        items: {
            control: 'object',
            description: 'Array of navigation items with id, icon, label, and optional active state',
        },
        bottomItems: {
            control: 'object',
            description: 'Array of navigation items to display at the bottom of the navigation',
        },
        'onNav-click': {
            description: 'Event emitted when a navigation item is clicked',
            control: { type: 'function' },
        },
        'update:items': {
            description: 'Event emitted when items state changes',
            control: { type: 'function' },
        },
        'update:bottomItems': {
            description: 'Event emitted when bottomItems state changes',
            control: { type: 'function' },
        },
    }
} satisfies Meta<typeof BravoMainNavigation>;

type Story = StoryObj<typeof BravoMainNavigation>;

// Basic navigation with a few items
export const Primary: Story = {
    render: () => ({
        components: { BravoMainNavigation },
        directives: { tooltip: Tooltip },
        setup() {
            const items = ref([
                { id: 'inbox', icon: 'pi pi-inbox', label: 'Inbox', active: true, tooltip: 'Inbox' },
                { id: 'map', icon: 'pi pi-map', label: 'Map', tooltip: 'Journeys' },
                { id: 'book', icon: 'pi pi-book', label: 'Book', tooltip: 'Knowledge' },
                { id: 'chart', icon: 'pi pi-chart-line', label: 'Chart', tooltip: 'Analytics' },
            ]);

            return { items };
        },
        template: `
      <div style="height: 400px; background-color: var(--bravo-surface-ground);">
        <BravoMainNavigation 
          v-model:items="items"
          @nav-click="(item) => console.log('Clicked:', item.label)" 
        />
      </div>
    `,
    }),
};

// Navigation with more items to show scrolling
export const WithManyItems: Story = {
    render: () => ({
        components: { BravoMainNavigation },
        directives: { tooltip: Tooltip },
        setup() {
            const items = ref([
                { id: 'inbox', icon: 'pi pi-inbox', label: 'Inbox', active: true, tooltip: 'Inbox' },
                { id: 'calendar', icon: 'pi pi-calendar', label: 'Calendar', tooltip: 'Calendar' },
                { id: 'tasks', icon: 'pi pi-tasks', label: 'Tasks', tooltip: 'Tasks' },
                { id: 'files', icon: 'pi pi-file', label: 'Files', tooltip: 'Files' },
                { id: 'search', icon: 'pi pi-search', label: 'Search', tooltip: 'Search' },
                { id: 'edit', icon: 'pi pi-pencil', label: 'Edit', tooltip: 'Edit' },
                { id: 'marker', icon: 'pi pi-map-marker', label: 'Settings', tooltip: 'Settings' },
                { id: 'info', icon: 'pi pi-info-circle', label: 'Information', tooltip: 'Information' },
                { id: 'notifications', icon: 'pi pi-bell', label: 'Notifications', tooltip: 'Notifications' },
            ]);

            return { items };
        },
        template: `
      <div style="height: 600px; background-color: var(--bravo-surface-ground);">
        <BravoMainNavigation 
          v-model:items="items"
          @nav-click="(item) => console.log('Clicked:', item.label)" 
        />
      </div>
    `,
    }),
    parameters: {
        docs: {
            description: {
                story: 'Navigation with multiple items to demonstrate scrolling behavior.',
            },
        },
    },
};

// Example with different active states
export const WithActiveStates: Story = {
    render: () => ({
        components: { BravoMainNavigation },
        directives: { tooltip: Tooltip },
        setup() {
            const items = [
                { id: 'inbox', icon: 'pi pi-inbox', label: 'Inbox', tooltip: 'Inbox' },
                { id: 'calendar', icon: 'pi pi-calendar', label: 'Calendar', active: true, tooltip: 'Calendar' },
                { id: 'tasks', icon: 'pi pi-tasks', label: 'Tasks', tooltip: 'Tasks' },
                { id: 'files', icon: 'pi pi-file', label: 'Files', tooltip: 'Files' },
            ];
            return { items };
        },
        template:
            '<div style="height: 400px; background-color: var(--bravo-surface-ground);"><BravoMainNavigation :items="items" @nav-click="handleNavClick" /></div>',
        methods: {
            handleNavClick(item: NavItem) {
                console.log('Navigation clicked:', item);
            },
        },
    }),
};

// Example with hidden items
export const HiddenItems: Story = {
    render: () => ({
        components: { BravoMainNavigation },
        directives: { tooltip: Tooltip },
        setup() {
            const items = ref([
                { id: 'inbox', icon: 'pi pi-inbox', label: 'Inbox', active: true, tooltip: 'Inbox' },
                { id: 'map', icon: 'pi pi-map', label: 'Map', tooltip: 'Map', visible: false },
                { id: 'book', icon: 'pi pi-book', label: 'Book', tooltip: 'Book' },
                { id: 'chart', icon: 'pi pi-chart-line', label: 'Chart', tooltip: 'Chart' },
                { id: 'cog', icon: 'pi pi-cog', label: 'Settings', tooltip: 'Settings', visible: false },
            ]);

            const bottomItems = ref([
                { id: 'user', icon: 'pi pi-user', label: 'Profile', tooltip: 'Profile' },
                { id: 'bell', icon: 'pi pi-bell', label: 'Notifications', tooltip: 'Notifications', visible: false }
            ]);

            return { items, bottomItems };
        },
        template: `
      <div style="height: 400px; background-color: var(--bravo-surface-ground);">
        <BravoMainNavigation 
          v-model:items="items"
          v-model:bottomItems="bottomItems" 
          @nav-click="(item) => console.log('Clicked:', item.label)"
        />
        <div style="margin-top: 1rem; padding: 1rem; background-color: white;">
          <p>The "Map" and "Settings" items in the main navigation and "Notifications" in the bottom navigation have <code>visible: false</code> and are not displayed.</p>
          <p>Complete items array:</p>
          <pre>{{ JSON.stringify(items, null, 2) }}</pre>
          <p>Bottom items array:</p>
          <pre>{{ JSON.stringify(bottomItems, null, 2) }}</pre>
        </div>
      </div>
    `,
    }),
    parameters: {
        docs: {
            description: {
                story: 'Example showing how items with visible: false are not displayed in the navigation.',
            },
        },
    },
};

// Interactive example with state management
export const Interactive: Story = {
    render: () => ({
        components: { BravoMainNavigation },
        directives: { tooltip: Tooltip },
        setup() {
            const items = ref([
                { id: 'inbox', icon: 'pi pi-inbox', label: 'Inbox', active: true, tooltip: 'Inbox' },
                { id: 'map', icon: 'pi pi-map', label: 'Map', tooltip: 'Map' },
                { id: 'book', icon: 'pi pi-book', label: 'Book', tooltip: 'Book' },
                { id: 'chart', icon: 'pi pi-chart-line', label: 'Chart', tooltip: 'Chart' },
            ]);

            const handleNavClick = (item: NavItem) => {
                console.log('Navigation clicked:', item.label);
            };

            return { items, handleNavClick };
        },
        template: `
      <div style="height: 400px; background-color: var(--bravo-surface-ground);">
        <BravoMainNavigation 
          v-model:items="items"
          @nav-click="handleNavClick" 
        />
        <div style="margin-top: 1rem; padding: 1rem;">
          <p>Click on the navigation items to see the active state change.</p>
          <p>Active item: {{ items.find(item => item.active)?.label }}</p>
        </div>
      </div>
    `,
    }),
};

// Example with a notifications icon at the bottom
export const WithNotificationsAtBottom: Story = {
    render: () => ({
        components: { BravoMainNavigation },
        directives: { tooltip: Tooltip },
        setup() {
            const items = ref([
                { id: 'inbox', icon: 'pi pi-inbox', label: 'Inbox', active: true, tooltip: 'Inbox' },
                { id: 'map', icon: 'pi pi-map', label: 'Map', tooltip: 'Map' },
                { id: 'book', icon: 'pi pi-book', label: 'Book', tooltip: 'Book' },
                { id: 'chart', icon: 'pi pi-chart-line', label: 'Chart', tooltip: 'Chart' },
            ]);

            const bottomItems = ref([{ 
                id: 'notifications', 
                icon: 'pi pi-bell', 
                label: 'Notifications', 
                tooltip: 'Notifications' 
            }]);

            const handleNavClick = (item: NavItem) => {
                console.log('Clicked:', item.label);
            };

            return { items, bottomItems, handleNavClick };
        },
        template: `
      <div style="height: 400px; background-color: var(--bravo-surface-ground);">
        <BravoMainNavigation 
          v-model:items="items"
          v-model:bottomItems="bottomItems"
          @nav-click="handleNavClick" 
        />
      </div>
    `,
    }),
    parameters: {
        docs: {
            description: {
                story: 'Navigation with 4 items at the top and a notifications bell at the bottom.',
            },
        },
    },
};

// Example with avatar at the bottom
export const WithAvatarAtBottom: Story = {
    render: () => ({
        components: { BravoMainNavigation, BravoMenu },
        directives: { tooltip: Tooltip },
        setup() {
            const items = ref([
                { id: 'inbox', icon: 'pi pi-inbox', label: 'Inbox', active: true, tooltip: 'Inbox' },
                { id: 'map', icon: 'pi pi-map', label: 'Map', tooltip: 'Map' },
                { id: 'book', icon: 'pi pi-book', label: 'Book', tooltip: 'Book' },
                { id: 'chart', icon: 'pi pi-chart-line', label: 'Chart', tooltip: 'Chart' },
            ]);

            const bottomItems = ref([{ 
                id: 'user-profile', 
                label: 'User Profile', 
                tooltip: 'User Profile', 
                avatar: {
                    image: 'https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png',
                    firstName: 'Amy',
                    lastName: 'Elsner',
                    size: 'normal',
                }
            }]);

            const menu = ref();
            
            const menuItems = ref([
                {
                    label: 'User Actions',
                    items: [
                        { label: 'Profile', icon: 'pi pi-user', command: () => console.log('Profile clicked') },
                        { label: 'Settings', icon: 'pi pi-cog', command: () => console.log('Settings clicked') },
                        { label: 'Sign Out', icon: 'pi pi-sign-out', command: () => console.log('Sign Out clicked') }
                    ]
                }
            ]);

            const handleNavClick = (item: NavItem) => {
                if (item.id === 'user-profile' && menu.value) {
                    const event = window.event;
                    menu.value.toggle(event);
                } else {
                    console.log('Clicked:', item.label);
                }
            };

            return { items, bottomItems, handleNavClick, menuItems, menu };
        },
        template: `
      <div style="height: 400px; background-color: var(--bravo-surface-ground); position: relative;">
        <BravoMainNavigation 
          v-model:items="items"
          v-model:bottomItems="bottomItems"
          @nav-click="handleNavClick" 
        />
        <BravoMenu 
          ref="menu"
          :model="menuItems"
          :popup="true"
          appendTo="body"
          :popupAlignment="'right'"
        />
      </div>
    `,
    }),
    parameters: {
        docs: {
            description: {
                story: 'Navigation with 4 items at the top and a user profile avatar at the bottom that shows a menu to the right when clicked.',
            },
        },
    },
};

// Example with avatar at the bottom - showing initials
export const WithInitialsAvatarAtBottom: Story = {
    render: () => ({
        components: { BravoMainNavigation, BravoMenu },
        directives: { tooltip: Tooltip },
        setup() {
            const items = ref([
                { id: 'inbox', icon: 'pi pi-inbox', label: 'Inbox', active: true, tooltip: 'Inbox' },
                { id: 'map', icon: 'pi pi-map', label: 'Map', tooltip: 'Map' },
                { id: 'book', icon: 'pi pi-book', label: 'Book', tooltip: 'Book' },
                { id: 'chart', icon: 'pi pi-chart-line', label: 'Chart', tooltip: 'Chart' },
            ]);

            const bottomItems = ref([{ 
                id: 'user-profile', 
                label: 'User Profile', 
                tooltip: 'User Profile', 
                avatar: {
                    firstName: 'John',
                    lastName: 'Doe',
                    size: 'normal',
                    backgroundColor: '#2196F3' // Blue background color
                }
            }]);

            const menu = ref();
            
            const menuItems = ref([
                {
                    label: 'User Actions',
                    items: [
                        { label: 'Profile', icon: 'pi pi-user', command: () => console.log('Profile clicked') },
                        { label: 'Settings', icon: 'pi pi-cog', command: () => console.log('Settings clicked') },
                        { label: 'Sign Out', icon: 'pi pi-sign-out', command: () => console.log('Sign Out clicked') }
                    ]
                }
            ]);

            const handleNavClick = (item: NavItem) => {
                if (item.id === 'user-profile' && menu.value) {
                    const event = window.event;
                    menu.value.toggle(event);
                } else {
                    console.log('Clicked:', item.label);
                }
            };

            return { items, bottomItems, handleNavClick, menuItems, menu };
        },
        template: `
      <div style="height: 400px; background-color: var(--bravo-surface-ground); position: relative;">
        <BravoMainNavigation 
          v-model:items="items"
          v-model:bottomItems="bottomItems"
          @nav-click="handleNavClick" 
        />
        <BravoMenu 
          ref="menu"
          :model="menuItems"
          :popup="true"
          appendTo="body"
          :popupAlignment="'right'"
        />
      </div>
    `,
    }),
    parameters: {
        docs: {
            description: {
                story: 'Navigation with 4 items at the top and a user profile avatar at the bottom showing initials (JD) instead of an image.',
            },
        },
    },
};
