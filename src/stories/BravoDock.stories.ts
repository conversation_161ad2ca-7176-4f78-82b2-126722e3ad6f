import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import BravoDock from '../components/BravoDock.vue';

const meta = {
    title: 'Menu/Dock',
    component: BravoDock,
    tags: ['autodocs'],
    argTypes: {
        model: { control: 'object' },
        position: {
            control: 'select',
            options: ['bottom', 'top', 'left', 'right'],
        },
        pt: { control: 'object' },
    },
} satisfies Meta<typeof BravoDock>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    args: {
        model: [
            {
                label: 'Finder',
                icon: 'pi pi-home',
                command: () => console.log('Finder clicked'),
            },
            {
                label: 'App Store',
                icon: 'pi pi-shopping-cart',
                command: () => console.log('App Store clicked'),
            },
            {
                label: 'Photos',
                icon: 'pi pi-image',
                command: () => console.log('Photos clicked'),
            },
            {
                label: 'Trash',
                icon: 'pi pi-trash',
                command: () => console.log('Trash clicked'),
            },
        ],
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <Dock :model="model" />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoDock },
        setup() {
            return { args };
        },
        template: `
      <div style="height: 400px; position: relative;">
        <BravoDock v-bind="args" />
      </div>
    `,
    }),
};

// Different positions
export const Positions: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-col gap-8">
    <BravoDock :model="model" position="top" />
    <BravoDock :model="model" position="bottom" />
    <BravoDock :model="model" position="left" />
    <BravoDock :model="model" position="right" />
  </div>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoDock },
        setup() {
            const items = [
                { label: 'Home', icon: 'pi pi-home' },
                { label: 'Calendar', icon: 'pi pi-calendar' },
                { label: 'Settings', icon: 'pi pi-cog' },
            ];
            return { items };
        },
        template: `
      <div style="height: 500px; position: relative;">
        <BravoDock :model="items" position="top" class="mb-5" />
        <BravoDock :model="items" position="bottom" class="mb-5" />
        <BravoDock :model="items" position="left" />
        <BravoDock :model="items" position="right" />
      </div>
    `,
    }),
};
