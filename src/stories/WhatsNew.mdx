import { Meta } from '@storybook/blocks';

<Meta title="overview/What's New?" />

<style>
    {`
  `}
</style>

# Release Notes

Here is where we post the updates for each Component Library version

---

&nbsp;

#### <strong> 05/2025 </strong>

- 1.0.78 - Updated BravoLoginScreen and BravoLoginScreenSSO to emit the password reset event
- 1.0.77 - Updated Timeline Title to use BravoSubHead instead of Title3. Added new BravoRelativeDateTime component. Updated icon display.
- 1.0.76 - Updated BravoZeroState to allow users to hide the button. Added default icons.
- 1.0.75 - Updated scrollable tabs so they don't show a scrollbar below the tablist
- 1.0.74 - Updated styling of timestamp in timeline
- 1.0.73 - Updated Timeline Component to use the Timestamp component for consistent dates
- 1.0.72 - Added global styling for scrollbars. Added filter styled fields for Select and MultiSelect
- 1.0.70 - Renamed BodyBold to BravoBodyBold
- 1.0.69 - Removed 100% width on all bravo tab components
- 1.0.68 - Imported all tab components as BravoTabs
- 1.0.66 - Updated loading state on BravoSquareImageCropped to accept a prop to know when to finish vs fixed time.
- 1.0.65 - Updated BravoSquareImageCropped to flip the mirrored text and add loading times to reduce flashing
- 1.0.64 - Updated BravoSquareImageCropperd to remove logic and have a crop preview
- 1.0.62 - Added BravoSquareImageCropper that crops uploaded images to resizable square shapes
- 1.0.61 - Updated BravoMainNavigation to use BravoAvatar instead of a default avatar component to all initial fallback
- 1.0.60 - Updated BravoAvatar to have logic to show image and back to first or last name if not provided and fall back to icon if not provided
- 1.0.58 - Updated BravoTimeline to allow rendering html in timeline body. Also made some styling updates
- 1.0.57 - Updated BravoComments to allow the title/url to be hidden and also put the title/url above the comment body
- 1.0.56 - Added a visible toggle to the BravoMainNav component to hide menu items in the list
- 1.0.55 - Added a trim to the email value in the Login and password reset components to remove excess spaces on email check and submit handlers. Also added an extra error handler and message when the email check gets a network or cors error.
- 1.0.54 - Added a PasswordReset and PasswordResetConfirmation components
- 1.0.53 - Updated Login SSO screen to be more responsive. Added Sample view state for InputText component.
- 1.0.52 - Update Label default color to secondary and bottom padding to 6px

#### <strong> 04/2025 </strong>

- 1.0.51 - Updated BravoLabel to only show the info tooltip on hover
- 1.0.50 - Updated the BravoMenu to have better slot passthrough and can now show a template above the menu items. Added SB example for that.
- 1.0.49 - Added a BravoLoginScreenSSO that has an additional handler for checking email before showing password
- 1.0.48 - Added BravoComment component, updated size of small button, added badget over button story example, updated drawer close button border radius
- 1.0.46 - Updated size of LargeBody and added Unpublished and Previously Published to Tag styles
- 1.0.42 - Added a component for zero state screens with slots for image, title and body
- 1.0.41 - Added prop for Avatars on the MainNavigation Components
- 1.0.40 - One more small update to select field height
- 1.0.38 - Updated placeholder text color and icon color on select fields
- 1.0.37 - Small update to select fields y padding
- 1.0.36 - Updated y padding and border radius for all input fields and buttons
- 1.0.34 - Removed Border from top of table, removed rounded from dialog close, updated select heights, updated spinner speed
- 1.0.33 - Updated tooltip styling, added tooltips to MainNav component, and updated styling of Block headers
- 1.0.32 - Updated SelectField to allow for better options with avatars for a user select component, updated adnimation duration of spinner
- 1.0.31 - Updated styles for Tabs, Blocks, Accodions
- 1.0.28 - Added a BravoTitlePage typography component for screen titles
- 1.0.27 - Added the BravoInputIcon component so we can use it in the BravoIconField component
- 1.0.26 - Removed the old CxmLabel component from the checkbox and updated with the new BravoLabel
- 1.0.26 - Updated the BravoDataTable component by adding the necessary component and props
- 1.0.23 - Updated pipeline to automatically increment patch versions on publish
- 1.0.22 - Added new BravoLabel Component and also includes eslint and prettier with all files updated for formatting

#### <strong> 03/2025 </strong>

- 1.0.19 - Updated the text styling in Block Headers
- 1.0.18 - Added PanelMenuNav component to be used as the left nav list on Inbox, Customers, Settings and other screens
- 1.0.17 - Added NavTree component for the left nav layout for most screens
- 1.0.16 - Added Block component based on the Accordion component
- 1.0.13 - Updated Timeline component to match our Events timeline. Added prop for dots or icons
- 1.0.12 - Added ability to tap enter on login screen form to submit the email and password
- 1.0.11 - Added all Accordion components as Bravo Components
- 1.0.9 - Added login screen component and fixed tabindex styling on password field
- 1.0.8 - fixed ts errors
- 1.0.7 - Update focus state for buttons to be ada compliant
- 1.0.6 - Upgraded styles of Tags and added styles for Case Statuses (New, Ready, Waiting, Resolved, Closed)

&nbsp;

#### <strong> 02/2025 - 1.0.5 </strong>

- Upgraded to latest version of Chromatic - 11.27.0

&nbsp;

#### <strong> 02/2025 - 1.0.4 </strong>

- Lots of CSS styling updates
- Added View States for single edit fields
- Added Main Navigation Component
- Added a panel of fields for Block mockups
- Added a Login Component
- Updated Tailwind configuration and dependencies so it can be used easily in other vue projects

&nbsp;

#### <strong> 02/2025 - 1.0.0 </strong>

- Remove all DevExtreme components and replace with PrimeVue components
- Upgraded to PrimeVue 4.2.5
- Upgraded to Storybook 8.6
- Added new typography file
- Added wrappers for almost all components from Primevue
- Added the Primeicons library for quick access to lots of actions
- Added a global theme to style all components

&nbsp;

#### <strong> 9/29/2023 - 0.0.62 </strong>

- Added a new skeleton loader component for the grid component

&nbsp;

#### <strong> 9/29/2023 - 0.0.61 </strong>

- Added color variables and documentation
- Updated the order of the categories
- Added documentation to Getting Started

&nbsp;

#### <strong> 9/20/2023 - 0.0.58 </strong>

- We updated the organization of the component library
