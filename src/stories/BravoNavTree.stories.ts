import type { Meta, StoryObj } from '@storybook/vue3';
import BravoNavTree from '../components/BravoNavTree.vue';
import { ref } from 'vue';

const meta = {
    title: 'Data/NavTree', // Changed from 'Data/Tree' to 'Data/NavTree'
    component: BravoNavTree,
    tags: ['autodocs'],
    argTypes: {
        value: { control: 'object' },
        expandedKeys: { control: 'object' },
        selectionKeys: { control: 'object' },
        selectionMode: { control: 'select', options: ['single', 'multiple', 'checkbox'] },
        metaKeySelection: { control: 'boolean' },
        loading: { control: 'boolean' },
        loadingIcon: { control: 'text' },
        filter: { control: 'boolean' },
        filterMode: { control: 'select', options: ['lenient', 'strict'] },
    },
} as Meta<typeof BravoNavTree>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoNavTree 
    :value="nodes" 
    selectionMode="single"
    v-model:selectionKeys="selectedKeys"
  />
  <div class="mt-3">Selected: {{ selectedKeys ? Object.keys(selectedKeys)[0] : 'none' }}</div>
</template>

<script setup>
import { ref } from 'vue';

const nodes = ref([
  {
    key: '0',
    label: 'Cases',
    selectable: false,
    children: [
      { key: '0-0', label: 'My Inbox' },
      { key: '0-1', label: 'My Team's Unassigned' },
      { key: '0-2', label: 'All' }
    ]
  },
  {
    key: '1',
    label: 'Tasks',
    selectable: false,
    children: [
      { key: '1-0', label: 'My Tasks' },
      { key: '1-1', label: 'My Team's Unassigned' },
      { key: '1-2', label: 'All' }
    ]
  }
]);

const selectedKeys = ref({});
</script>
`,
            },
        },
    },
    render: () => ({
        components: { BravoNavTree },
        setup() {
            const nodes = ref([
                {
                    key: '0',
                    label: 'Cases',
                    selectable: false,
                    children: [
                        { key: '0-0', label: 'My Open Cases' },
                        { key: '0-1', label: "My Team's Unassigned" },
                        { key: '0-2', label: 'All' },
                    ],
                },
                {
                    key: '1',
                    label: 'Tasks',
                    selectable: false,
                    children: [
                        { key: '1-0', label: 'My Tasks' },
                        { key: '1-1', label: "My Team's unassigned" },
                        { key: '1-2', label: 'All' },
                    ],
                },
            ]);

            const selectedKeys = ref({});

            return { nodes, selectedKeys };
        },
        template: `
      <BravoNavTree 
        :value="nodes" 
        selectionMode="single"
        v-model:selectionKeys="selectedKeys"
      />
      <div class="mt-3">Selected: {{ selectedKeys ? Object.keys(selectedKeys)[0] : 'none' }}</div>
    `,
    }),
};

// Selection story
export const Selection: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoNavTree
    :value="nodes"
    selectionMode="checkbox"
    v-model:selectionKeys="selectedKeys"
    :filter="true"
  />
</template>

<script setup>
import { ref } from 'vue';

const nodes = ref([
  {
    key: '0',
    label: 'Documents',
    children: [
      {
        key: '0-0',
        label: 'Work',
        children: [
          { key: '0-0-0', label: 'Expenses.doc' },
          { key: '0-0-1', label: 'Resume.doc' }
        ]
      },
      {
        key: '0-1',
        label: 'Home',
        children: [
          { key: '0-1-0', label: 'Invoices.txt' }
        ]
      }
    ]
  },
  {
    key: '1',
    label: 'Pictures',
    children: [
      { key: '1-0', label: 'barcelona.jpg' },
      { key: '1-1', label: 'logo.jpg' },
      { key: '1-2', label: 'primeui.png' }
    ]
  }
]);

const selectedKeys = ref({});
</script>
`,
            },
        },
    },
    render: () => ({
        components: { BravoNavTree },
        setup() {
            const nodes = ref([
                {
                    key: '0',
                    label: 'Documents',
                    children: [
                        {
                            key: '0-0',
                            label: 'Work',
                            children: [
                                { key: '0-0-0', label: 'Expenses.doc' },
                                { key: '0-0-1', label: 'Resume.doc' },
                            ],
                        },
                        {
                            key: '0-1',
                            label: 'Home',
                            children: [{ key: '0-1-0', label: 'Invoices.txt' }],
                        },
                    ],
                },
                {
                    key: '1',
                    label: 'Pictures',
                    children: [
                        { key: '1-0', label: 'barcelona.jpg' },
                        { key: '1-1', label: 'logo.jpg' },
                        { key: '1-2', label: 'primeui.png' },
                    ],
                },
            ]);

            const selectedKeys = ref({});

            return { nodes, selectedKeys };
        },
        template: `
      <BravoNavTree
        :value="nodes"
        selectionMode="checkbox"
        v-model:selectionKeys="selectedKeys"
        :filter="true"
      />
    `,
    }),
};
