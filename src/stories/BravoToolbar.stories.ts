import type { Meta, StoryObj } from '@storybook/vue3';
import BravoToolbar from '../components/BravoToolbar.vue';
import BravoButton from '../components/BravoButton.vue';
import BravoInputText from '../components/BravoInputText.vue';
import BravoSplitButton from '../components/BravoSplitButton.vue';

// Define the props interface for Toolbar component
// Toolbar doesn't have specific props, it uses slots for content

const meta = {
    title: 'Panel/Toolbar',
    component: BravoToolbar,
    tags: ['autodocs'],
    argTypes: {
        // No specific props for Toolbar
    },
} satisfies Meta<typeof BravoToolbar>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoToolbar>
    <template #start>
      <BravoButton icon="pi pi-plus" class="mr-2" severity="secondary" text />
      <BravoButton icon="pi pi-print" class="mr-2" severity="secondary" text />
      <BravoButton icon="pi pi-upload" severity="secondary" text />
    </template>
    <template #end>
      <BravoButton label="Save" icon="pi pi-check" />
    </template>
  </BravoToolbar>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoToolbar, BravoButton },
        setup() {
            return { args };
        },
        template: `
      <BravoToolbar v-bind="args">
        <template #start>
          <BravoButton icon="pi pi-plus" class="mr-2" severity="secondary" text />
          <BravoButton icon="pi pi-print" class="mr-2" severity="secondary" text />
          <BravoButton icon="pi pi-upload" severity="secondary" text />
        </template>
        <template #end>
          <BravoButton label="Save" icon="pi pi-check" />
        </template>
      </BravoToolbar>
    `,
    }),
};

// With all slots
export const WithAllSlots: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoToolbar>
    <template #start>
      <BravoButton icon="pi pi-plus" class="mr-2" severity="primary" text />
      <BravoButton icon="pi pi-print" class="mr-2" severity="secondary" text />
      <BravoButton icon="pi pi-upload" severity="secondary" text />
    </template>
    <template #end>
      <BravoButton icon="pi pi-user" severity="secondary" />  
      <BravoInputText placeholder="Search" class="mr-2" />
      <BravoButton label="Save" icon="pi pi-check" />
    </template>
  </BravoToolbar>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoToolbar, BravoButton, BravoInputText },
        setup() {
            return { args };
        },
        template: `
      <BravoToolbar v-bind="args">
        <template #start>
          <BravoButton icon="pi pi-plus" class="mr-2" severity="secondary" text />
          <BravoButton icon="pi pi-print" class="mr-2" severity="secondary" text />
          <BravoButton icon="pi pi-upload" severity="secondary" text />
        </template>
        <template #end>
          <BravoButton icon="pi pi-user" severity="secondary" />  
          <BravoInputText placeholder="Search"  />
          <BravoButton label="Save" icon="pi pi-check" />
        </template>
      </BravoToolbar>
    `,
    }),
};

// With SplitButton
export const WithSplitButton: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoToolbar>
    <template #start>
      <BravoButton icon="pi pi-plus" class="mr-2" severity="secondary" text />
      <BravoButton icon="pi pi-print" class="mr-2" severity="secondary" text />
      <BravoButton icon="pi pi-upload" severity="secondary" text />
    </template>
    <template #end>
      <BravoSplitButton label="Save" icon="pi pi-check" :model="items" />
    </template>
  </BravoToolbar>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoToolbar, BravoButton, BravoSplitButton },
        setup() {
            const items = [
                {
                    label: 'Save',
                    icon: 'pi pi-save',
                },
                {
                    label: 'Save As',
                    icon: 'pi pi-file',
                },
                {
                    label: 'Export',
                    icon: 'pi pi-external-link',
                },
            ];

            return { args, items };
        },
        template: `
      <BravoToolbar v-bind="args">
        <template #start>
          <BravoButton icon="pi pi-plus" class="mr-2" severity="secondary" text />
          <BravoButton icon="pi pi-print" class="mr-2" severity="secondary" text />
          <BravoButton icon="pi pi-upload" severity="secondary" text />
        </template>
        <template #end>
          <BravoSplitButton label="Save" icon="pi pi-check" :model="items" />
        </template>
      </BravoToolbar>
    `,
    }),
};

// Custom styling
export const CustomStyling: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoToolbar style="border-radius: 3rem; padding: 1rem 1rem 1rem 1.5rem">
    <template #start>
      <div class="flex items-center gap-2">
        <BravoButton label="Files" text plain />
        <BravoButton label="Edit" text plain />
        <BravoButton label="View" text plain />
      </div>
    </template>
    <template #end>
      <div class="flex items-center gap-2">
        <BravoButton label="Share" severity="contrast" size="small" />
      </div>
    </template>
  </BravoToolbar>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoToolbar, BravoButton },
        setup() {
            return { args };
        },
        template: `
      <BravoToolbar style="border-radius: 3rem; padding: 1rem 1rem 1rem 1.5rem" v-bind="args">
        <template #start>
          <div class="flex items-center gap-2">
            <BravoButton label="Files" text plain />
            <BravoButton label="Edit" text plain />
            <BravoButton label="View" text plain />
          </div>
        </template>
        <template #end>
          <div class="flex items-center gap-2">
            <BravoButton label="Share" severity="contrast" size="small" />
          </div>
        </template>
      </BravoToolbar>
    `,
    }),
};

// multiple buttons
export const Default: Story = {
    args: {},
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <Toolbar>
    <template #start>
      <BravoButton style="margin: 0px 4px;" icon="pi pi-plus" label="Primary Page Action" />
      <BravoButton style="margin: 0px 4px;" icon="pi pi-minus" :outlined="true" label="Secondary Page Action" />
      <BravoButton style="margin: 0px 4px;" icon="pi pi-pencil" :outlined="true" label="edit" />
    </template>
  </Toolbar>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoToolbar, BravoButton },
        setup() {
            return { args };
        },
        template: `
      <BravoToolbar v-bind="args">
        <template #start>
          <BravoButton style="margin: 0px 4px;" icon="pi pi-plus" label="Primary Page Action" />
          <BravoButton style="margin: 0px 4px;" icon="pi pi-minus" :outlined="true" label="Secondary Page Action" />
          <BravoButton style="margin: 0px 4px;" icon="pi pi-pencil" :outlined="true" label="edit" />
        </template>
      </BravoToolbar>
    `,
    }),
};
