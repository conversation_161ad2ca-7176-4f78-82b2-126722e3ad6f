import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import BravoTextarea from '../components/BravoTextarea.vue';
import { ref } from 'vue';

const meta = {
    title: 'Form/Textarea',
    component: BravoTextarea,
    tags: ['autodocs'],
    argTypes: {
        modelValue: { control: 'text' },
        autoResize: { control: 'boolean' },
        rows: { control: 'number' },
        cols: { control: 'number' },
    },
} satisfies Meta<typeof BravoTextarea>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoTextarea v-model="value" rows="5" cols="30" placeholder="Enter your text" />
</template>

<script setup>
import { ref } from 'vue';

const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoTextarea },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoTextarea
        v-model="value"
        rows="5"
        cols="30"
        placeholder="Enter your text"
      />
    `,
    }),
};

export const AutoResize: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoTextarea
    v-model="value"
    autoResize
    rows="5"
    cols="30"
    placeholder="This textarea will auto-resize"
  />
</template>

<script setup>
import { ref } from 'vue';

const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoTextarea },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoTextarea
        v-model="value"
        autoResize
        rows="5"
        cols="30"
        placeholder="This textarea will auto-resize"
      />
    `,
    }),
};
