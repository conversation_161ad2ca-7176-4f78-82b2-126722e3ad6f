import type { Meta, StoryObj } from '@storybook/vue3';
import BravoLabel from '../components/BravoLabel.vue';
import BravoInputText from '../components/BravoInputText.vue';

// Meta information for the component
const meta = {
  title: 'Form/Label',
  component: BravoLabel,
  tags: ['autodocs'],
  argTypes: {
    text: { control: 'text' },
    id: { control: 'text' },
    dataTestId: { control: 'text' },
    forElement: { control: 'text' },
    isRequired: { control: 'boolean' },
    iconName: { control: 'text' },
    toolTipText: { control: 'text' },
    toolTipPosition: { control: 'text' },
    mode: { control: 'select', options: ['primary', 'secondary', 'dark'] },
    className: { control: 'text' },
  },
} satisfies Meta<typeof BravoLabel>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default story with no mode specified
export const Default: Story = {
  args: {
    text: 'Default Label',
    id: 'default-label',
  },
  parameters: {
    docs: {
      source: {
        code: `
<template>
  <BravoLabel
    text="Default Label"
    id="default-label"
  />
</template>
`,
      },
    },
  },
  render: (args) => ({
    components: { BravoLabel },
    setup() {
      return { args };
    },
    template: `
      <BravoLabel v-bind="args" />
    `,
  }),
};

// Basic story with primary label
export const Primary: Story = {
  args: {
    text: 'Primary Label',
    mode: 'primary',
    id: 'primary-label',
  },
  parameters: {
    docs: {
      source: {
        code: `
<template>
  <BravoLabel
    text="Primary Label"
    mode="primary"
    id="primary-label"
  />
</template>
`,
      },
    },
  },
  render: (args) => ({
    components: { BravoLabel },
    setup() {
      return { args };
    },
    template: `
      <BravoLabel v-bind="args" />
    `,
  }),
};

// Secondary label story
export const Secondary: Story = {
  args: {
    text: 'Secondary Label',
    mode: 'secondary',
    id: 'secondary-label',
  },
  parameters: {
    docs: {
      source: {
        code: `
<template>
  <BravoLabel
    text="Secondary Label"
    mode="secondary"
    id="secondary-label"
  />
</template>
`,
      },
    },
  },
  render: (args) => ({
    components: { BravoLabel },
    setup() {
      return { args };
    },
    template: `
      <BravoLabel v-bind="args" />
    `,
  }),
};


// Required label story
export const Required: Story = {
  args: {
    text: 'Required Field',
    isRequired: true,
    id: 'required-label',
  },
  parameters: {
    docs: {
      source: {
        code: `
<template>
  <BravoLabel
    text="Required Field"
    isRequired
    id="required-label"
  />
</template>
`,
      },
    },
  },
  render: (args) => ({
    components: { BravoLabel },
    setup() {
      return { args };
    },
    template: `
      <BravoLabel v-bind="args" />
    `,
  }),
};

// Label with tooltip
export const WithTooltip: Story = {
  args: {
    text: 'Label with Tooltip',
    iconName: 'info',
    toolTipText: 'This is a helpful tooltip',
    id: 'tooltip-label',
  },
  parameters: {
    docs: {
      source: {
        code: `
<template>
  <BravoLabel
    text="Label with Tooltip"
    iconName="info"
    toolTipText="This is a helpful tooltip"
    id="tooltip-label"
  />
</template>
`,
      },
    },
  },
  render: (args) => ({
    components: { BravoLabel },
    setup() {
      return { args };
    },
    template: `
      <BravoLabel v-bind="args" />
    `,
  }),
};

// Label with input field
export const WithInputField: Story = {
  args: {
    text: 'Email Address',
    id: 'email-label',
    forElement: 'email-input',
  },
  parameters: {
    docs: {
      source: {
        code: `
<template>
  <div>
    <BravoLabel
      text="Email Address"
      id="email-label"
      forElement="email-input"
    />
    <BravoInputText
      id="email-input"
      placeholder="Enter your email"
    />
  </div>
</template>
`,
      },
    },
  },
  render: (args) => ({
    components: { BravoLabel, BravoInputText },
    setup() {
      return { args };
    },
    template: `
      <div>
        <BravoLabel v-bind="args" />
        <BravoInputText
          :id="args.forElement"
          placeholder="Enter your email"
        />
      </div>
    `,
  }),
}; 