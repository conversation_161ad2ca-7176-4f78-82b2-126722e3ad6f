import { Meta, StoryObj } from '@storybook/vue3';
import { CxmInlineLoader } from '../components';

type BaseInlineLoaderProps = {
    isLoading?: boolean;
};

const defaultInlineLoader: BaseInlineLoaderProps = {
    isLoading: true,
};

export default {
    component: CxmInlineLoader,
    title: 'Legacy & Custom/Loaders/InlineLoader',
    tags: ['autodocs'],
    parameters: {
        docs: {
            description: {
                component: `InlineLoader can be sized with CSS height and width properties.`,
            },
        },
        layout: 'fullscreen',
    },
} as Meta<typeof CxmInlineLoader>;

type Story = StoryObj<typeof CxmInlineLoader>;

export const Default: Story = {
    args: { isLoading: defaultInlineLoader.isLoading },
};
