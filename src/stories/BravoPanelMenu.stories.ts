import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import BravoPanelMenu from '../components/BravoPanelMenu.vue';

interface PanelMenuItem {
    label: string;
    icon?: string;
    items?: PanelMenuItem[];
    command?: () => void;
    url?: string;
    expanded?: boolean;
    disabled?: boolean;
}

const meta = {
    title: 'Menu/PanelMenu',
    component: BravoPanelMenu,
    tags: ['autodocs'],
    argTypes: {
        model: { control: 'object' },
        multiple: { control: 'boolean' },
        pt: { control: 'object' },
    },
} satisfies Meta<typeof BravoPanelMenu>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    args: {
        model: [
            {
                label: 'File',
                icon: 'pi pi-fw pi-file',
                items: [
                    {
                        label: 'New',
                        icon: 'pi pi-fw pi-plus',
                        items: [
                            { label: 'Document', icon: 'pi pi-fw pi-file' },
                            { label: 'Spreadsheet', icon: 'pi pi-fw pi-file-excel' },
                        ],
                    },
                    { label: 'Open', icon: 'pi pi-fw pi-folder-open' },
                    { label: 'Save', icon: 'pi pi-fw pi-save' },
                ],
            },
            {
                label: 'Edit',
                icon: 'pi pi-fw pi-pencil',
                expanded: true,
                items: [
                    { label: 'Cut', icon: 'pi pi-fw pi-cut' },
                    { label: 'Copy', icon: 'pi pi-fw pi-copy' },
                    { label: 'Paste', icon: 'pi pi-fw pi-paste' },
                ],
            },
        ],
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <PanelMenu :model="model" />
</template>
`,
            },
        },
        layout: 'padded',
        viewport: {
            defaultViewport: 'desktop',
        },
    },
    render: (args) => ({
        components: { BravoPanelMenu },
        setup() {
            return { args };
        },
        template: '<div style="padding: 20px; width: 300px;"><BravoPanelMenu v-bind="args" /></div>',
    }),
};
