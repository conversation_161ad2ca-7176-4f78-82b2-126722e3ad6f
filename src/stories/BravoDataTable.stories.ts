import type { Meta, StoryObj } from '@storybook/vue3';
import BravoDataTable from '../components/BravoDataTable.vue';
import Column from 'primevue/column';
import { ref } from 'vue';

interface BravoDataTableProps {
    value?: any[];
    paginator?: boolean;
    rows?: number;
    sortField?: string;
    sortOrder?: number;
    selection?: any[];
    dataKey?: string;
}

const meta = {
    title: 'Data/DataTable',
    component: BravoDataTable,
    tags: ['autodocs'],
    argTypes: {
        value: { control: 'object' },
        columns: { control: 'object' },
        paginator: { control: 'boolean' },
        rows: { control: 'number' },
        sortField: { control: 'text' },
        sortOrder: { control: 'number' },
        selection: { control: 'object' },
        dataKey: { control: 'text' },
    },
} as Meta<typeof BravoDataTable>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    args: {
        value: [
            { code: '1', name: 'Product 1', price: 100 },
            { code: '2', name: 'Product 2', price: 200 },
            { code: '3', name: 'Product 3', price: 300 },
            { code: '4', name: 'Product 4', price: 400 },
            { code: '5', name: 'Product 5', price: 500 },
            { code: '6', name: 'Product 6', price: 500 },
        ],
        paginator: true,
        rows: 3,
        rowHover: true,
        columns: [
            { field: 'code', header: 'Code' },
            { field: 'name', header: 'Name' },
            { field: 'price', header: 'Price' },
        ],
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <DataTable :rowHover="true" :value="products" :paginator="true" :rows="5">
    <Column field="code" header="Code"></Column>
    <Column field="name" header="Name"></Column>
    <Column field="price" header="Price"></Column>
  </DataTable>
</template>

<script setup>
import { ref } from 'vue';

const products = ref([
  { code: '1', name: 'Product 1', price: 100 },
  { code: '2', name: 'Product 2', price: 200 },
  { code: '3', name: 'Product 3', price: 300 },
  { code: '4', name: 'Product 4', price: 400 },
  { code: '5', name: 'Product 5', price: 500 },
]);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoDataTable },
        setup() {
            return { args };
        },
        template: `
      <BravoDataTable  v-bind="args">
        
      </BravoDataTable>
    `,
    }),
};

// Advanced story with sorting and filtering
export const Advanced: Story = {
    args: {
        value: [
            {
                code: '1',
                name: 'Product 1',
                category: 'Electronics',
                price: 100,
                rating: 4.5,
                inventoryStatus: 'In Stock',
            },
            {
                code: '2',
                name: 'Product 2',
                category: 'Fashion',
                price: 200,
                rating: 4.0,
                inventoryStatus: 'Low Stock',
            },
            {
                code: '3',
                name: 'Product 3',
                category: 'Home',
                price: 300,
                rating: 3.5,
                inventoryStatus: 'Out of Stock',
            },
            {
                code: '4',
                name: 'Product 4',
                category: 'Electronics',
                price: 400,
                rating: 5.0,
                inventoryStatus: 'In Stock',
            },
            {
                code: '5',
                name: 'Product 5',
                category: 'Fashion',
                price: 500,
                rating: 4.2,
                inventoryStatus: 'Low Stock',
            },
        ],
        paginator: true,
        rows: 5,
        // sortField: "code",
        // sortOrder: 1,
        showGridlines: true,
        stripedRows: true,
        columns: [
            { field: 'code', header: 'Code', sortable: true },
            {
                field: 'name',
                header: 'Name',
                sortable: true,
                filterMatchMode: 'contains',
            },
            {
                field: 'category',
                header: 'Category',
                sortable: true,
                filterMatchMode: 'contains',
            },
            {
                field: 'price',
                header: 'Price',
                sortable: true,
                bodyTemplate: (data: any) => `<span>$${data.price}</span>`,
            },
            {
                field: 'rating',
                header: 'Rating',
                sortable: true,
                bodyTemplate: (data) => `<span>${data.rating}/5</span>`,
            },
            {
                field: 'inventoryStatus',
                header: 'Status',
                sortable: true,
                bodyTemplate: (data) => `
          <span :class="'inventory-badge status-' + slotProps.data.inventoryStatus.toLowerCase().replace(' ', '')">
            ${data.inventoryStatus}
          </span>
        `,
            },
        ],
        // filters: {
        //   name: { value: "", matchMode: "contains" },
        //   category: { value: "", matchMode: "contains" },
        // },
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <
    :value="products"
    :paginator="true"
    :rows="5"
    :sortField="sortField"
    :sortOrder="sortOrder"
    :filters="filters"
    stripedRows
    showGridlines
  >
    <Column field="code" header="Code" sortable></Column>
    <Column field="name" header="Name" sortable filterMatchMode="contains"></Column>
    <Column field="category" header="Category" sortable filterMatchMode="contains"></Column>
    <Column field="price" header="Price" sortable>
      <template #body="slotProps">
        \${{slotProps.data.price}}
      </template>
    </Column>
    <Column field="rating" header="Rating" sortable>
      <template #body="slotProps">
        {{slotProps.data.rating}}/5
      </template>
    </Column>
    <Column field="inventoryStatus" header="Status" sortable>
      <template #body="slotProps">
        <span :class="'inventory-badge status-' + slotProps.data.inventoryStatus.toLowerCase().replace(' ', '')">
          {{slotProps.data.inventoryStatus}}
        </span>
      </template>
    </Column>
  </>
</template>

<script setup>
import { ref } from 'vue';

const products = ref([
  { code: '1', name: 'Product 1', category: 'Electronics', price: 100, rating: 4.5, inventoryStatus: 'In Stock' },
  { code: '2', name: 'Product 2', category: 'Fashion', price: 200, rating: 4.0, inventoryStatus: 'Low Stock' },
  { code: '3', name: 'Product 3', category: 'Home', price: 300, rating: 3.5, inventoryStatus: 'Out of Stock' },
  { code: '4', name: 'Product 4', category: 'Electronics', price: 400, rating: 5.0, inventoryStatus: 'In Stock' },
  { code: '5', name: 'Product 5', category: 'Fashion', price: 500, rating: 4.2, inventoryStatus: 'Low Stock' },
]);

const sortField = ref('code');
const sortOrder = ref(1);
const filters = ref({
  name: { value: '', matchMode: 'contains' },
  category: { value: '', matchMode: 'contains' }
});
</script>

<style>
.inventory-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 700;
}
.status-instock {
  background: #C8E6C9;
  color: #256029;
}
.status-lowstock {
  background: #FEEDAF;
  color: #8A5340;
}
.status-outofstock {
  background: #FFCDD2;
  color: #C63737;
}
</style>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoDataTable, Column },
        setup() {
            return { args };
        },
        template: `
      <BravoDataTable v-bind="args" >

        
      </BravoDataTable>
    `,
    }),
};

// Checkbox Row Selection story
export const CheckboxRowSelection: Story = {
    args: {
        value: [
            {
                id: '1001',
                code: 'f230fh0g3',
                name: 'Bamboo Watch',
                description: 'Product Description',
                image: 'bamboo-watch.jpg',
                price: 65,
                category: 'Accessories',
                quantity: 24,
                inventoryStatus: 'INSTOCK',
                rating: 5,
            },
            {
                id: '1002',
                code: 'nvklal433',
                name: 'Black Watch',
                description: 'Product Description',
                image: 'black-watch.jpg',
                price: 72,
                category: 'Accessories',
                quantity: 61,
                inventoryStatus: 'INSTOCK',
                rating: 4,
            },
            {
                id: '1003',
                code: 'zz21cz3c1',
                name: 'Blue Band',
                description: 'Product Description',
                image: 'blue-band.jpg',
                price: 79,
                category: 'Fitness',
                quantity: 2,
                inventoryStatus: 'LOWSTOCK',
                rating: 3,
            },
            {
                id: '1004',
                code: '244wgerg2',
                name: 'Blue T-Shirt',
                description: 'Product Description',
                image: 'blue-t-shirt.jpg',
                price: 29,
                category: 'Clothing',
                quantity: 25,
                inventoryStatus: 'INSTOCK',
                rating: 5,
            },
            {
                id: '1005',
                code: 'h456wer53',
                name: 'Bracelet',
                description: 'Product Description',
                image: 'bracelet.jpg',
                price: 15,
                category: 'Accessories',
                quantity: 73,
                inventoryStatus: 'INSTOCK',
                rating: 4,
            },
            {
                id: '1006',
                code: 'av2231fwg',
                name: 'Brown Purse',
                description: 'Product Description',
                image: 'brown-purse.jpg',
                price: 120,
                category: 'Accessories',
                quantity: 0,
                inventoryStatus: 'OUTOFSTOCK',
                rating: 4,
            },
            {
                id: '1007',
                code: 'bib36pfvm',
                name: 'Chakra Bracelet',
                description: 'Product Description',
                image: 'chakra-bracelet.jpg',
                price: 32,
                category: 'Accessories',
                quantity: 5,
                inventoryStatus: 'LOWSTOCK',
                rating: 3,
            },
            {
                id: '1008',
                code: 'mbvjkgip5',
                name: 'Galaxy Earrings',
                description: 'Product Description',
                image: 'galaxy-earrings.jpg',
                price: 34,
                category: 'Accessories',
                quantity: 23,
                inventoryStatus: 'INSTOCK',
                rating: 5,
            },
        ],
        paginator: true,
        rows: 5,
        // dataKey: "id",
        // selection: [],
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <DataTable v-model:selection="selectedProducts" :value="products" dataKey="id" :paginator="true" :rows="5">
    <!-- The header checkbox will automatically select/unselect all rows on the current page -->
    <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>
    <Column field="code" header="Code"></Column>
    <Column field="name" header="Name"></Column>
    <Column field="category" header="Category"></Column>
    <Column field="price" header="Price"></Column>
    <Column field="inventoryStatus" header="Status"></Column>
  </DataTable>
</template>

<script setup>
import { ref } from 'vue';

const products = ref([
  { id: '1001', code: 'f230fh0g3', name: 'Bamboo Watch', price: 65, category: 'Accessories', inventoryStatus: 'INSTOCK' },
  { id: '1002', code: 'nvklal433', name: 'Black Watch', price: 72, category: 'Accessories', inventoryStatus: 'INSTOCK' },
  { id: '1003', code: 'zz21cz3c1', name: 'Blue Band', price: 79, category: 'Fitness', inventoryStatus: 'LOWSTOCK' },
  { id: '1004', code: '244wgerg2', name: 'Blue T-Shirt', price: 29, category: 'Clothing', inventoryStatus: 'INSTOCK' }
]);

const selectedProducts = ref([]);

// Event handlers for row selection
const onRowSelect = (event) => {
  console.log('Row Selected:', event.data);
};

const onRowUnselect = (event) => {
  console.log('Row Unselected:', event.data);
};

// Event handler for header checkbox (select all)
const onHeaderCheckboxToggle = (event) => {
  console.log('Header Checkbox Toggled:', event.checked);
};
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoDataTable, Column },
        setup() {
            const selectedProducts = ref([]);

            const onRowSelect = (event: any) => {
                console.log('Row Selected:', event.data);
            };

            const onRowUnselect = (event: any) => {
                console.log('Row Unselected:', event.data);
            };

            const onHeaderCheckboxToggle = (event: any) => {
                console.log('Header Checkbox Toggled:', event.checked);
            };

            const formatPrice = (price: number) => {
                return `$${price}`;
            };

            const getStatusClass = (status: string) => {
                return `inventory-badge status-${status.toLowerCase()}`;
            };

            return {
                args,
                selectedProducts,
                onRowSelect,
                onRowUnselect,
                onHeaderCheckboxToggle,
                formatPrice,
                getStatusClass,
            };
        },
        template: `
      <div>
        <div v-if="selectedProducts.length > 0" class="mb-3">
          <p>Selected Products: {{ selectedProducts.length }}</p>
        </div>
        <BravoDataTable 
          v-model:selection="selectedProducts" 
          v-bind="args"
          @row-select="onRowSelect"
          @row-unselect="onRowUnselect"
          :columns="args.columns"
          @header-checkbox-toggle="onHeaderCheckboxToggle"
        >
          <!-- The header checkbox will automatically select/unselect all rows on the current page -->
          <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>
          <Column field="code" header="Code"></Column>
          <Column field="name" header="Name"></Column>
          <Column field="category" header="Category"></Column>
          <Column field="price" header="Price">
            <template #body="{ data }">
              {{ formatPrice(data.price) }}
            </template>
          </Column>
          <Column field="inventoryStatus" header="Status">
            <template #body="{ data }">
              <span :class="getStatusClass(data.inventoryStatus)">
                {{ data.inventoryStatus }}
              </span>
            </template>
          </Column>
        </BravoDataTable>
        
        <div v-if="selectedProducts.length > 0" class="mt-3">
          <h3>Selected Products</h3>
          <ul>
            <li v-for="product in selectedProducts" :key="product.id">
              {{ product.name }} - {{ formatPrice(product.price) }}
            </li>
          </ul>
        </div>
        
        <style>
        .inventory-badge {
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          font-weight: 700;
        }
        .status-instock {
          background: #C8E6C9;
          color: #256029;
        }
        .status-lowstock {
          background: #FEEDAF;
          color: #8A5340;
        }
        .status-outofstock {
          background: #FFCDD2;
          color: #C63737;
        }
        </style>
      </div>
    `,
    }),
};
