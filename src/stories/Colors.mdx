import { Meta } from '@storybook/blocks';

<Meta title="styles/Colors" />

export const sections = [
    {
        title: 'Monochrome',
        colors: [
            '--color-monochrome-white',
            '--color-monochrome-50',
            '--color-monochrome-100',
            '--color-monochrome-150',
            '--color-monochrome-200',
            '--color-monochrome-400',
            '--color-monochrome-300',
            '--color-monochrome-500',
            '--color-monochrome-600',
            '--color-monochrome-700',
            '--color-monochrome-800',
            '--color-monochrome-850',
            '--color-monochrome-900',
        ],
    },
    {
        title: 'Blue',
        colors: [
            '--color-blue-50',
            '--color-blue-100',
            '--color-blue-200',
            '--color-blue-300',
            '--color-blue-400',
            '--color-blue-500',
            '--color-blue-550',
            '--color-blue-600',
            '--color-blue-650',
            '--color-blue-700',
            '--color-blue-800',
            '--color-blue-900',
        ],
    },
    {
        title: 'Bluegray',
        colors: [
            '--color-bluegray-50',
            '--color-bluegray-100',
            '--color-bluegray-200',
            '--color-bluegray-300',
            '--color-bluegray-400',
            '--color-bluegray-500',
            '--color-bluegray-600',
            '--color-bluegray-700',
            '--color-bluegray-800',
            '--color-bluegray-900',
        ],
    },
    {
        title: 'Green',
        colors: [
            '--color-green-50',
            '--color-green-100',
            '--color-green-200',
            '--color-green-300',
            '--color-green-400',
            '--color-green-500',
            '--color-green-600',
            '--color-green-700',
            '--color-green-800',
            '--color-green-900',
        ],
    },
    {
        title: 'Orange',
        colors: [
            '--color-orange-50',
            '--color-orange-100',
            '--color-orange-200',
            '--color-orange-300',
            '--color-orange-400',
            '--color-orange-500',
            '--color-orange-600',
            '--color-orange-700',
            '--color-orange-800',
            '--color-orange-900',
        ],
    },
    {
        title: 'Yellow',
        colors: [
            '--color-yellow-50',
            '--color-yellow-100',
            '--color-yellow-200',
            '--color-yellow-300',
            '--color-yellow-400',
            '--color-yellow-500',
            '--color-yellow-600',
            '--color-yellow-700',
            '--color-yellow-800',
            '--color-yellow-900',
        ],
    },
    {
        title: 'Red',
        colors: [
            '--color-red-50',
            '--color-red-100',
            '--color-red-200',
            '--color-red-300',
            '--color-red-400',
            '--color-red-500',
            '--color-red-600',
            '--color-red-700',
            '--color-red-800',
            '--color-red-900',
        ],
    },
    {
        title: 'Purple',
        colors: [
            '--color-purple-50',
            '--color-purple-100',
            '--color-purple-200',
            '--color-purple-300',
            '--color-purple-400',
            '--color-purple-500',
            '--color-purple-600',
            '--color-purple-700',
            '--color-purple-800',
            '--color-purple-900',
        ],
    },

    {
        title: 'Miscellaneous',
        colors: ['--color-monochrome-black', '--color-other-magenta-100', '--color-other-teal-100'],
    },
];

<style>
    {`
  .color-label {
    font-size: 14px;
    text-align: center;
    margin-top: 4px;
  }

  .color-title {
    font-size: 18px;
    font-weight: 600;
    margin: 25px 0 6px 0;
  }
  `}
</style>

# Bravo Colors

Here are details about the colors that we use in the Bravo Design System and how to use them. For reference, <b><a href="https://www.figma.com/file/W1ImPeN8a4kl4ffpUit96M/Bravo-Framework?type=design&node-id=19%3A3418&mode=design&t=74VtQvJVlSVkl0Ir-1">here is a link to Figma color file</a></b>.

<br />

## How to Use Colors

We have created our colors as css variables in our component library.

To use the colors, you can simply set the color variable to the element that you are applying the color to.

<b>Example</b>: var(--gray-1600)

Here's an example of a color variable being used to style a square:

```
<div
  className="color-square"
  style={{
    background: `var(--gray-1600)`,
    height: "100px",
    width: "100px",
    borderRadius: "12px",
  }}
></div>
```

## Color Overview

<div>
    {sections.map((section) => (
        <div key={section.title}>
            <div className="color-title">{section.title}</div>
            <div>
                {section.colors.map((color) => (
                    <div
                        className="color-definition"
                        style={{ display: 'inline-block', margin: '0px 12px 0px 0px' }}
                        key={color}
                    >
                        <div
                            className="color-square"
                            style={{
                                background: `var(${color})`,
                                height: '55px',
                                width: '75px',
                                borderRadius: '12px',
                            }}
                        ></div>
                        <div className="color-label">{color}</div>
                    </div>
                ))}
            </div>
        </div>
    ))}
</div>
