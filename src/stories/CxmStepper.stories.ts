import type { Meta } from '@storybook/vue3';
import { CxmStepper } from '../components/CxmStepper';

type BaseStepperProps = {
    activeStep: number;
    steps: Array<any>;
    preRequired: boolean;
    dataTestId: string;
    id: string;
};
const defaultStepper: BaseStepperProps = {
    activeStep: 1,
    steps: [
        { id: 1, title: 'Step 1' },
        { id: 2, title: 'Step 2', dataTestId: 'spStep2' },
        { id: 3, title: 'Step 3', dataTestId: 'spStep3' },
        { id: 4, title: 'Step 4', dataTestId: 'spStep4' },
        { id: 5, title: 'Step 5', dataTestId: 'spStep5' },
    ],
    preRequired: true,
    dataTestId: 'jira-stepper',
    id: 'jira-stepper',
};

const meta = {
    title: 'Legacy & Custom/Stepper-Old',
    component: CxmStepper,
    tags: ['autodocs'],
    parameters: {
        docs: {
            description: {
                component: 'Display a Stepper relative to the current steps',
            },
        },
        layout: 'fullscreen',
    },
    argTypes: {
        activeStep: { control: 'number' },
        steps: {
            control: { type: 'object' },
        },
        preRequired: { control: 'boolean' },
    },
    args: defaultStepper,
} satisfies Meta<typeof CxmStepper>;

export default meta;

export const Default = { args: {} };
