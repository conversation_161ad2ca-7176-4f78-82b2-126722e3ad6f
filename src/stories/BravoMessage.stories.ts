import type { Meta, StoryObj } from '@storybook/vue3';
import BravoMessage from '../components/BravoMessage.vue';
import Message from 'primevue/message';

// Define the props interface for Message component
interface BravoMessageProps {
    severity?: 'success' | 'info' | 'warn' | 'error' | 'secondary' | 'contrast';
    closable?: boolean;
    life?: number;
    icon?: string;
    variant?: 'outlined' | 'simple';
    size?: 'small' | 'large';
}

const meta = {
    title: 'Messages/Message',
    component: BravoMessage,
    tags: ['autodocs'],
    argTypes: {
        severity: {
            control: 'select',
            options: ['success', 'info', 'warn', 'error'],
        },
        closable: { control: 'boolean' },
        life: { control: 'number' },
        icon: { control: 'text' },
        variant: {
            control: 'select',
            options: ['outlined', 'filled'],
        },
        pt: { control: 'object' },
    },
} satisfies Meta<typeof BravoMessage>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    args: {
        severity: 'info',
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <Message severity="info">This is a basic info message.</Message>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoMessage },
        template: `<BravoMessage v-bind="args">This is a basic info message.</BravoMessage>`,
    }),
};

// Severity examples
export const Severities: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-col">
    <Message severity="success" style="margin-bottom: 1rem;">This is a success message.</Message>
    <Message severity="info" style="margin-bottom: 1rem;">This is an info message.</Message>
    <Message severity="warn" style="margin-bottom: 1rem;">This is a warning message.</Message>
    <Message severity="error">This is an error message.</Message>
  </div>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoMessage },
        template: `
      <div class="flex flex-col">
        <BravoMessage severity="success" style="margin-bottom: 1rem;">This is a success message.</BravoMessage>
        <BravoMessage severity="info" style="margin-bottom: 1rem;">This is an info message.</BravoMessage>
        <BravoMessage severity="warn" style="margin-bottom: 1rem;">This is a warning message.</BravoMessage>
        <BravoMessage severity="error">This is an error message.</BravoMessage>
      </div>
    `,
    }),
};

// Closable message
export const Closable: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoMessage severity="info" closable>This is a closable message.</BravoMessage>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoMessage },
        template: '<BravoMessage severity="info" closable>This is a closable message.</BravoMessage>',
    }),
};

// Variants
export const Variants: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-col">
    <BravoMessage severity="info" style="margin-bottom: 1rem;">Default variant message</BravoMessage>
    <BravoMessage severity="info" variant="outlined" style="margin-bottom: 1rem;">Outlined variant message</BravoMessage>
    <BravoMessage severity="info" variant="filled">Filled variant message</BravoMessage>
  </div>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoMessage },
        template: `
      <div class="flex flex-col">
        <BravoMessage severity="info" style="margin-bottom: 1rem;">Default variant message</BravoMessage>
        <BravoMessage severity="info" variant="outlined" style="margin-bottom: 1rem;">Outlined variant message</BravoMessage>
        <BravoMessage severity="info" variant="filled">Filled variant message</BravoMessage>
      </div>
    `,
    }),
};
