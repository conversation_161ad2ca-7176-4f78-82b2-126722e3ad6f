import type { Meta, StoryObj } from '@storybook/vue3';
import BravoDivider from '../components/BravoDivider.vue';
import Divider from 'primevue/divider';

const meta = {
    title: 'Panel/Divider',
    component: BravoDivider,
    tags: ['autodocs'],
    argTypes: {
        align: {
            control: 'select',
            options: ['left', 'center', 'right', 'top', 'bottom'],
        },
        layout: {
            control: 'select',
            options: ['horizontal', 'vertical'],
        },
        type: {
            control: 'select',
            options: ['solid', 'dashed', 'dotted'],
        },
        pt: { control: 'object' },
    },
} satisfies Meta<typeof BravoDivider>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-col gap-4">
    <p>
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
    </p>
    <BravoDivider />
    <p>
      Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
    </p>
  </div>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoDivider },
        template: `
      <div class="flex flex-col gap-4">
        <p class="m-0">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        </p>
        <BravoDivider v-bind="args" />
        <p class="m-0">
          Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
        </p>
      </div>
    `,
    }),
};

// Types story
export const Types: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-col gap-4">
    <BravoDivider type="solid" />
    <BravoDivider type="dashed" />
    <BravoDivider type="dotted" />
  </div>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoDivider },
        template: `
      <div class="flex flex-col gap-4">
        <BravoDivider type="solid" />
        <BravoDivider type="dashed" />
        <BravoDivider type="dotted" />
      </div>
    `,
    }),
};

// Content story
export const Content: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-col gap-4">
    <BravoDivider align="left">
      <b>Left</b>
    </BravoDivider>
    <BravoDivider align="center">
      <b>Center</b>
    </BravoDivider>
    <BravoDivider align="right">
      <b>Right</b>
    </BravoDivider>
  </div>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoDivider },
        template: `
      <div class="flex flex-col gap-4">
        <BravoDivider align="left">
          <b>Left</b>
        </BravoDivider>
        <BravoDivider align="center">
          <b>Center</b>
        </BravoDivider>
        <BravoDivider align="right">
          <b>Right</b>
        </BravoDivider>
      </div>
    `,
    }),
};
