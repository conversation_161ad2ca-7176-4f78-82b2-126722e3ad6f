import type { Meta, StoryObj } from '@storybook/vue3';
import BravoInputText from '../components/BravoInputText.vue';
import BravoLabel from '../components/BravoLabel.vue';
import { ref } from 'vue';
// Define the props interface for InputText component
interface BravoInputTextProps {
    modelValue?: string;
    placeholder?: string;
    type?: string;
    size?: 'small' | 'large' | undefined;
    variant?: 'outlined' | 'filled';
    invalid?: boolean;
    disabled?: boolean;
    displayMode?: 'edit' | 'view';
}

// Meta information for the component
const meta = {
    title: 'Form/InputText',
    component: BravoInputText,
    tags: ['autodocs'],
    argTypes: {
        modelValue: { control: 'text' },
        placeholder: { control: 'text' },
        type: { control: 'select', options: ['text', 'password', 'email', 'number'] },
        size: { control: 'select', options: ['small', 'large', undefined] },
        variant: { control: 'select', options: ['outlined', 'filled'] },
        invalid: { control: 'boolean' },
        disabled: { control: 'boolean' },
        displayMode: { control: 'select', options: ['edit', 'view'] },
    },
} satisfies Meta<typeof BravoInputText>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoInputText v-model="value" placeholder="Enter text..." />
</template>

<script setup>
import { ref } from 'vue';

const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoInputText },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoInputText
        v-model="value"
        placeholder="Enter text..."
      />
    `,
    }),
};

// Filled variant story
export const Filled: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoInputText v-model="value" variant="filled" placeholder="Filled input..." />
</template>

<script setup>
import { ref } from 'vue';

const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoInputText },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoInputText
        v-model="value"
        variant="filled"
        placeholder="Filled input..."
      />
    `,
    }),
};

// Sizes story
export const Sizes: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-col">
    <BravoInputText v-model="value1" size="small" placeholder="Small" style="margin-bottom: 1rem;" />
    <BravoInputText v-model="value2" placeholder="Normal" style="margin-bottom: 1rem;" />
    <BravoInputText v-model="value3" size="large" placeholder="Large" />
  </div>
</template>

<script setup>
import { ref } from 'vue';

const value1 = ref('');
const value2 = ref('');
const value3 = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoInputText },
        setup() {
            const value1 = ref('');
            const value2 = ref('');
            const value3 = ref('');
            return { value1, value2, value3 };
        },
        template: `
      <div class="flex flex-col">
        <BravoInputText v-model="value1" size="small" placeholder="Small" style="margin-bottom: 1rem;" />
        <BravoInputText v-model="value2" placeholder="Normal" style="margin-bottom: 1rem;" />
        <BravoInputText v-model="value3" size="large" placeholder="Large" />
      </div>
    `,
    }),
};

// Invalid state story
export const Invalid: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-col gap-2">
    <BravoInputText v-model="value" :invalid="!value" placeholder="Required field" />
  </div>
</template>

<script setup>
import { ref } from 'vue';

const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoInputText },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoInputText
        v-model="value"
        :invalid="!value"
        placeholder="Required field"
      />
    `,
    }),
};

// Disabled story
export const Disabled: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoInputText v-model="value" disabled placeholder="Disabled input" />
</template>

<script setup>
import { ref } from 'vue';

const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoInputText },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoInputText
        v-model="value"
        disabled
        placeholder="Disabled input"
      />
    `,
    }),
};

// View Mode story
export const ViewModeWithLabel: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-col gap-3">
    <div>
      <BravoLabel 
        text="Edit Mode (default)" 
        forElement="edit-mode-input" 
        mode="primary"
      />
      <BravoInputText 
        id="edit-mode-input"
        v-model="value" 
        placeholder="Edit mode" 
      />
    </div>
    
    <div>
      <BravoLabel 
        text="View Mode" 
        forElement="view-mode-input" 
        mode="primary"
      />
      <BravoInputText 
        id="view-mode-input"
        v-model="value" 
        displayMode="view" 
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import BravoLabel from '../components/BravoLabel.vue';

const value = ref('This is read-only text in view mode');
</script>
`,
            },
        },
    },
    render: () => ({
        components: { BravoInputText, BravoLabel },
        setup() {
            const value = ref('This is read-only text in view mode');
            return { value };
        },
        template: `
      <div class="flex flex-col gap-3">
        <div>
          <BravoLabel 
            text="Edit Mode (default)" 
            forElement="edit-mode-input" 
            mode="primary"
          />
          <BravoInputText 
            id="edit-mode-input"
            v-model="value" 
            placeholder="Edit mode" 
          />
        </div>
        
        <div>
          <BravoLabel 
            text="View Mode" 
            forElement="view-mode-input" 
            mode="primary"
          />
          <BravoInputText 
            id="view-mode-input"
            v-model="value" 
            displayMode="view" 
          />
        </div>
      </div>
    `,
    }),
};

// Horizontal layout with labels to the left
export const HorizontalLabels: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-col gap-4">
    <!-- Edit mode with horizontal label -->
    <div class="flex items-center">
      <div class="w-1/3">
        <BravoLabel 
          text="Username:" 
          forElement="username-edit" 
          mode="primary"
        />
      </div>
      <div class="w-2/3">
        <BravoInputText 
          id="username-edit"
          v-model="username" 
          placeholder="Enter username" 
        />
      </div>
    </div>
    
    <!-- View mode with horizontal label -->
    <div class="flex items-center">
      <div class="w-1/3">
        <BravoLabel 
          text="Email:" 
          forElement="email-view" 
          mode="primary"
        />
      </div>
      <div class="w-2/3">
        <BravoInputText 
          id="email-view"
          v-model="email" 
          displayMode="view" 
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import BravoLabel from '../components/BravoLabel.vue';

const username = ref('');
const email = ref('<EMAIL>');
</script>
`,
            },
        },
    },
    render: () => ({
        components: { BravoInputText, BravoLabel },
        setup() {
            const username = ref('');
            const email = ref('<EMAIL>');
            return { username, email };
        },
        template: `
      <div class="flex flex-col gap-4">
        <!-- Edit mode with horizontal label -->
        <div style="display: flex; align-items: center;">
          <div style="width: 100px;">
            <BravoLabel 
              text="Username:" 
              forElement="username-edit" 
              mode="primary"
            />
          </div>
          <div style="flex: 1;">
            <BravoInputText 
              id="username-edit"
              v-model="username" 
              placeholder="Enter username" 
            />
          </div>
        </div>
        
        <!-- View mode with horizontal label -->
        <div style="display: flex; align-items: center;">
          <div style="width: 100px;">
            <BravoLabel 
              text="Email:" 
              forElement="email-view" 
              mode="primary"
            />
          </div>
          <div style="flex: 1;">
            <BravoInputText 
              id="email-view"
              v-model="email" 
              displayMode="view" 
            />
          </div>
        </div>
      </div>
    `,
    }),
};
