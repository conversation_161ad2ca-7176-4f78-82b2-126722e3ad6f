import type { Meta, StoryObj } from '@storybook/vue3';
import BravoStepper from '../components/BravoStepper.vue';
import StepList from 'primevue/steplist';
import StepPanels from 'primevue/steppanels';
import Step from 'primevue/step';
import StepPanel from 'primevue/steppanel';
import StepItem from 'primevue/stepitem';
import BravoButton from '../components/BravoButton.vue';

const meta = {
    title: 'Panel/Stepper',
    component: BravoStepper,
    tags: ['autodocs'],
    argTypes: {
        linear: { control: 'boolean' },
        orientation: {
            control: 'select',
            options: ['horizontal', 'vertical'],
        },
        pt: { control: 'object' },
    },
} satisfies Meta<typeof BravoStepper>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoStepper value="1">
    <StepList>
      <Step value="1">Personal</Step>
      <Step value="2">Seat</Step>
      <Step value="3">Payment</Step>
    </StepList>
    <StepPanels>
      <StepPanel value="1">
        <h3>Personal Information</h3>
        <p>Enter your personal information</p>
      </StepPanel>
      <StepPanel value="2">
        <h3>Seat Selection</h3>
        <p>Choose your seat</p>
      </StepPanel>
      <StepPanel value="3">
        <h3>Payment Information</h3>
        <p>Enter your payment details</p>
      </StepPanel>
    </StepPanels>
  </BravoStepper>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoStepper, StepList, StepPanels, Step, StepPanel },
        template: `
      <BravoStepper value="1" v-bind="args">
        <StepList>
          <Step value="1">Personal</Step>
          <Step value="2">Seat</Step>
          <Step value="3">Payment</Step>
        </StepList>
        <StepPanels>
          <StepPanel value="1">
            <h3>Personal Information</h3>
            <p>Enter your personal information</p>
          </StepPanel>
          <StepPanel value="2">
            <h3>Seat Selection</h3>
            <p>Choose your seat</p>
          </StepPanel>
          <StepPanel value="3">
            <h3>Payment Information</h3>
            <p>Enter your payment details</p>
          </StepPanel>
        </StepPanels>
      </BravoStepper>
    `,
    }),
};

// Vertical story
export const Vertical: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoStepper value="1" orientation="vertical">
    <StepItem value="1">
      <Step>Header I</Step>
      <StepPanel v-slot="{ activateCallback }">
        <div>Content I</div>
        <div>
          <BravoButton label="Next" @click="activateCallback('2')" />
        </div>
      </StepPanel>
    </StepItem>
    <StepItem value="2">
      <Step>Header II</Step>
      <StepPanel v-slot="{ activateCallback }">
        <div>Content II</div>
        <div>
          <BravoButton label="Back" severity="secondary" @click="activateCallback('1')" />
          <BravoButton label="Next" @click="activateCallback('3')" />
        </div>
      </StepPanel>
    </StepItem>
    <StepItem value="3">
      <Step>Header III</Step>
      <StepPanel v-slot="{ activateCallback }">
        <div>Content III</div>
        <div>
          <BravoButton label="Back" severity="secondary" @click="activateCallback('2')" />
        </div>
      </StepPanel>
    </StepItem>
  </BravoStepper>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoStepper, Step, StepPanel, StepItem, BravoButton },
        template: `
      <BravoStepper value="1" orientation="vertical" v-bind="args">
        <StepItem value="1">
          <Step>Header I</Step>
          <StepPanel v-slot="{ activateCallback }">
            <div>Content I</div>
            <div>
              <BravoButton label="Next" @click="activateCallback('2')" />
            </div>
          </StepPanel>
        </StepItem>
        <StepItem value="2">
          <Step>Header II</Step>
          <StepPanel v-slot="{ activateCallback }">
            <div>Content II</div>
            <div>
              <BravoButton label="Back" severity="secondary" @click="activateCallback('1')" />
              <BravoButton label="Next" @click="activateCallback('3')" />
            </div>
          </StepPanel>
        </StepItem>
        <StepItem value="3">
          <Step>Header III</Step>
          <StepPanel v-slot="{ activateCallback }">
            <div>Content III</div>
            <div>
              <BravoButton label="Back" severity="secondary" @click="activateCallback('2')" />
            </div>
          </StepPanel>
        </StepItem>
      </BravoStepper>
    `,
    }),
};
