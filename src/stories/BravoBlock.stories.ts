import type { Meta, StoryObj } from '@storybook/vue3';
import BravoBlock from '../components/BravoBlock.vue';
import BravoAccordionPanel from '../components/BravoAccordionPanel.vue';
import BravoAccordionHeader from '../components/BravoAccordionHeader.vue';
import BravoAccordionContent from '../components/BravoAccordionContent.vue';
import BravoAvatar from '../components/BravoAvatar.vue';
import BravoBadge from '../components/BravoBadge.vue';
import BravoFieldPanel from '../components/BravoFieldPanel.vue';
import BravoInputText from '../components/BravoInputText.vue';
import BravoSelectField from '../components/BravoSelectField.vue';
import BravoMultiSelect from '../components/BravoMultiSelect.vue';
import { ref } from 'vue';

const meta = {
    title: 'Panel/Block',
    component: BravoBlock,
    tags: ['autodocs'],
    argTypes: {
        multiple: { control: 'boolean' },
        expandIcon: { control: 'text' },
        collapseIcon: { control: 'text' },
        pt: { control: 'object' },
    },
} satisfies Meta<typeof BravoBlock>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoBlock :value="['0']" :multiple="true">
    <BravoAccordionPanel value="0">
      <BravoAccordionHeader>Header I</BravoAccordionHeader>
      <BravoAccordionContent>Content I</BravoAccordionContent>
    </BravoAccordionPanel>
    <BravoAccordionPanel value="1">
      <BravoAccordionHeader>Header II</BravoAccordionHeader>
      <BravoAccordionContent>Content II</BravoAccordionContent>
    </BravoAccordionPanel>
  </BravoBlock>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoBlock, BravoAccordionPanel, BravoAccordionHeader, BravoAccordionContent },
        template: `
      <BravoBlock :value="['0']" :multiple="true">
        <BravoAccordionPanel value="0">
          <BravoAccordionHeader>Header I</BravoAccordionHeader>
          <BravoAccordionContent>
            <p class="m-0">Content I</p>
          </BravoAccordionContent>
        </BravoAccordionPanel>
        <BravoAccordionPanel value="1">
          <BravoAccordionHeader>Header II</BravoAccordionHeader>
          <BravoAccordionContent>
            <p class="m-0">Content II</p>
          </BravoAccordionContent>
        </BravoAccordionPanel>
      </BravoBlock>
    `,
    }),
};

// Embedded Apps
export const Apps: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoBlock :value="['0']" :multiple="true">
    <BravoAccordionPanel value="0">
      <BravoAccordionHeader>
        <template #default>
          <div class="flex items-center gap-2">
            <BravoAvatar image="https://cdn.prod.website-files.com/5d4b12a4c03a64ab754f23bd/67e6c86d7cca6933dfe07dbd_jiralogo.png" />
            <span class="font-bold">Jira</span>
          </div>
        </template>
      </BravoAccordionHeader>
      <BravoAccordionContent>Content for Jira</BravoAccordionContent>
    </BravoAccordionPanel>
    <BravoAccordionPanel value="1">
      <BravoAccordionHeader>
        <template #default>
          <div class="flex items-center gap-2">
            <BravoAvatar image="https://cdn.prod.website-files.com/5d4b12a4c03a64ab754f23bd/67e6c8ec8fe6b34b8aa3404f_seismiclogo.png" />
            <span class="font-bold">Seismic</span>
          </div>
        </template>
      </BravoAccordionHeader>
      <BravoAccordionContent>Content for Seismic</BravoAccordionContent>
    </BravoAccordionPanel>
  </BravoBlock>
</template>
`,
            },
        },
    },
    render: () => ({
        components: {
            BravoBlock,
            BravoAccordionPanel,
            BravoAccordionHeader,
            BravoAccordionContent,
            BravoAvatar,
            BravoBadge,
        },
        template: `
      <BravoBlock :value="['0']" :multiple="true">
        <BravoAccordionPanel value="0">
          <BravoAccordionHeader>
            <template #default>
              <div class="flex items-center gap-2">
                <BravoAvatar image="https://cdn.prod.website-files.com/5d4b12a4c03a64ab754f23bd/67e6c86d7cca6933dfe07dbd_jiralogo.png" />
                <span class="font-bold">Jira</span>
              </div>
            </template>
          </BravoAccordionHeader>
          <BravoAccordionContent>
            <p class="m-0">Content for Jira</p>
          </BravoAccordionContent>
        </BravoAccordionPanel>
         <BravoAccordionPanel value="1">
          <BravoAccordionHeader>
            <template #default>
              <div class="flex items-center gap-2">
                <BravoAvatar image="https://cdn.prod.website-files.com/5d4b12a4c03a64ab754f23bd/67e6c8ec8fe6b34b8aa3404f_seismiclogo.png" />
                <span class="font-bold">Seismic</span>
              </div>
            </template>
          </BravoAccordionHeader>
          <BravoAccordionContent>
            <p class="m-0">Content for Seismic</p>
          </BravoAccordionContent>
        </BravoAccordionPanel>
      </BravoBlock>
    `,
    }),
};

// Field Panel Story
export const WithFieldPanel: Story = {
    parameters: {
        docs: {
            source: {
                code: `
  <template>
    <BravoBlock :value="['0']" :multiple="true">
      <BravoAccordionPanel value="0">
        <BravoAccordionHeader>User Information</BravoAccordionHeader>
        <BravoAccordionContent>
          <BravoFieldPanel :fields="fields" />
        </BravoAccordionContent>
      </BravoAccordionPanel>
    </BravoBlock>
  </template>
  `,
            },
        },
    },
    render: () => ({
        components: {
            BravoBlock,
            BravoAccordionPanel,
            BravoAccordionHeader,
            BravoAccordionContent,
            BravoFieldPanel,
            BravoInputText,
            BravoSelectField,
            BravoMultiSelect,
        },
        setup() {
            const name = ref('');
            const selectedCity = ref<string | null>(null);
            const selectedCities = ref<string[]>([]);

            const fields = [
                {
                    label: 'Name',
                    component: BravoInputText,
                    props: {
                        value: name.value,
                        placeholder: 'Enter name',
                        onInput: (val: string) => (name.value = val),
                    },
                },
                {
                    label: 'City',
                    component: BravoSelectField,
                    props: {
                        selected: selectedCity.value,
                        options: [
                            { label: 'New York', value: 'NY' },
                            { label: 'London', value: 'LDN' },
                        ],
                        placeholder: 'Select city',
                        onChange: (val: string | null) => (selectedCity.value = val),
                    },
                },
                {
                    label: 'Preferences',
                    component: BravoMultiSelect,
                    props: {
                        selectedItems: selectedCities.value,
                        options: [
                            { label: 'Sports', value: 'sports' },
                            { label: 'Music', value: 'music' },
                        ],
                        placeholder: 'Select preferences',
                        onUpdate: (val: string[]) => (selectedCities.value = val),
                    },
                },
            ];

            return { fields, name, selectedCity, selectedCities };
        },
        template: `
        <BravoBlock :value="['0']" :multiple="true">
          <BravoAccordionPanel value="0">
            <BravoAccordionHeader>User Information</BravoAccordionHeader>
            <BravoAccordionContent>
              <BravoFieldPanel :fields="fields" />
       
            </BravoAccordionContent>
          </BravoAccordionPanel>
        </BravoBlock>
      `,
    }),
};
