import type { Meta, StoryObj } from '@storybook/vue3';
import BravoTimeline from '../components/BravoTimeline.vue';

// Add type for the render args
interface TimelineItem {
    title: string;
    user: string;
    datetime: string | number;
    content: string;
    icon?: string;
}

interface RenderArgs {
    value: TimelineItem[];
    align?: 'left' | 'right' | 'alternate' | 'top' | 'bottom';
    layout?: 'vertical' | 'horizontal';
    useIcons?: boolean;
}

const meta = {
    title: 'Data/Timeline',
    component: BravoTimeline,
    tags: ['autodocs'],
    argTypes: {
        value: {
            description: 'Array of timeline items',
            control: 'object',
        },
        align: {
            description: 'Alignment of the timeline items',
            control: 'select',
            options: ['left', 'right', 'alternate', 'top', 'bottom'],
            defaultValue: 'left',
        },
        layout: {
            description: 'Layout orientation of the timeline',
            control: 'select',
            options: ['vertical', 'horizontal'],
            defaultValue: 'vertical',
        },
        useIcons: {
            description: 'Whether to use icons instead of dots',
            control: 'boolean',
            defaultValue: false,
        },
    },
} satisfies Meta<typeof BravoTimeline>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    args: {
        value: [
            {
                title: 'Reassigned',
                user: 'Alan Finlay',
                datetime: new Date('2025-05-20T09:20:00').toISOString(),
                content: 'Case assigned to Alan Finlay on SmartTech Technical Support',
            },
            {
                title: 'Fields Updated',
                user: 'Alan Finlay',
                datetime: new Date('2024-12-19T14:30:00').toISOString(),
                content: 'Watchers: Christian Mitchell → Alan Finlay, Christian Mitchell',
            },
            {
                title: 'Fields Updated',
                user: 'John Status',
                datetime: new Date('2024-12-18T07:55:00').toISOString(),
                content: 'Contact: → New Contact',
            },
            {
                title: 'Case Created',
                user: 'Christian Mitchell',
                datetime: new Date('2024-12-15T10:16:00').toISOString(),
                content: 'Created from Boomtown',
            },
        ],
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoTimeline :value="events">
    <!-- The component now handles the layout internally -->
  </BravoTimeline>
</template>

<script setup>
import { ref } from 'vue';

const events = ref([
  {
    title: 'Reassigned',
    user: 'Alan Finlay',
    datetime: new Date('2024-12-20T09:20:00').toISOString(),
    content: 'Case assigned to Alan Finlay on SmartTech Technical Support'
  },
  // ... more events
]);
</script>
`,
            },
        },
    },
    render: (args: RenderArgs) => ({
        components: { BravoTimeline },
        setup() {
            return { args };
        },
        template: `<BravoTimeline v-bind="args" />`,
    }),
};

// Timeline with icons
export const WithIcons: Story = {
    args: {
        value: [
            {
                title: 'Reassigned',
                user: 'Alan Finlay',
                datetime: new Date('2024-12-20T09:20:00').toISOString(),
                content: 'Case assigned to Alan Finlay on SmartTech Technical Support',
                icon: 'pi pi-user',
            },
            {
                title: 'Fields Updated',
                user: 'Alan Finlay',
                datetime: new Date('2024-12-19T14:30:00').toISOString(),
                content: 'Watchers: Christian Mitchell → Alan Finlay, Christian Mitchell',
                icon: 'pi pi-pencil',
            },
            {
                title: 'Fields Updated',
                user: 'John Status',
                datetime: new Date('2024-12-18T07:55:00').toISOString(),
                content: 'Contact: → New Contact',
                icon: 'pi pi-pencil',
            },
            {
                title: 'Case Created',
                user: 'Christian Mitchell',
                datetime: new Date('2024-12-15T10:16:00').toISOString(),
                content: 'Created from Boomtown',
                icon: 'pi pi-plus',
            },
        ],
        useIcons: true,
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoTimeline :value="events" :useIcons="true">
    <!-- The component handles icons automatically -->
  </BravoTimeline>
</template>

<script setup>
import { ref } from 'vue';

const events = ref([
  {
    title: 'Reassigned',
    user: 'Alan Finlay',
    datetime: new Date('2024-12-20T09:20:00').toISOString(),
    content: 'Case assigned to Alan Finlay on SmartTech Technical Support',
    icon: 'pi pi-user'
  },
  // ... more events with icons
]);
</script>
`,
            },
        },
    },
};
