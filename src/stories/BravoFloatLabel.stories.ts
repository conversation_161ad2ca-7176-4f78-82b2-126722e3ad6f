import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import BravoFloatLabel from '../components/BravoFloatLabel.vue';
import BravoInputText from '../components/BravoInputText.vue';
import { ref } from 'vue';

const meta = {
    title: 'Form/FloatLabel',
    component: BravoFloatLabel,
    tags: ['autodocs'],
    argTypes: {
        // Remove variant from argTypes since it's not defined in component props
    },
} satisfies Meta<typeof BravoFloatLabel>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFloatLabel>
    <InputText id="username" v-model="value" />
    <label for="username">Username</label>
  </BravoFloatLabel>
</template>

<script setup>
import { ref } from 'vue';

const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { <PERSON><PERSON><PERSON>Label, BravoInputText },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoFloatLabel>
        <BravoInputText id="username" v-model="value" />
        <label for="username">Username</label>
      </BravoFloatLabel>
    `,
    }),
};

// Variants story
export const Variants: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-column gap-3">
    <FloatLabel variant="in">
      <InputText id="in_label" v-model="value1" autocomplete="off" />
      <label for="in_label">In Label</label>
    </BravoFloatLabel>

    <FloatLabel variant="on">
      <InputText id="on_label" v-model="value2" autocomplete="off" />
      <label for="on_label">On Label</label>
    </BravoFloatLabel>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const value1 = ref('');
const value2 = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoFloatLabel, BravoInputText },
        setup() {
            const value1 = ref('');
            const value2 = ref('');
            return { value1, value2 };
        },
        template: `
      <div class="flex flex-column gap-3">
        <BravoFloatLabel variant="in">
          <BravoInputText id="in_label" v-model="value1" autocomplete="off" />
          <label for="in_label">In Label</label>
        </BravoFloatLabel>

        <BravoFloatLabel variant="on">
          <BravoInputText id="on_label" v-model="value2" autocomplete="off" />
          <label for="on_label">On Label</label>
        </BravoFloatLabel>
      </div>
    `,
    }),
};

// Invalid state story
export const Invalid: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-column gap-3">
    <BravoFloatLabel>
      <InputText id="value1" v-model="value1" :invalid="!value1" />
      <label for="value1">Username</label>
    </BravoFloatLabel>

    <BravoFloatLabel variant="in">
      <InputText id="value2" v-model="value2" :invalid="!value2" />
      <label for="value2">Username</label>
    </BravoFloatLabel>

    <BravoFloatLabel variant="on">
      <InputText id="value3" v-model="value3" :invalid="!value3" />
      <label for="value3">Username</label>
    </BravoFloatLabel>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const value1 = ref('');
const value2 = ref('');
const value3 = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoFloatLabel, BravoInputText },
        setup() {
            const value1 = ref('');
            const value2 = ref('');
            const value3 = ref('');
            return { value1, value2, value3 };
        },
        template: `
      <div class="flex flex-column gap-3">
        <BravoFloatLabel>
          <BravoInputText id="value1" v-model="value1" :invalid="!value1" />
          <label for="value1">Username</label>
        </BravoFloatLabel>

        <BravoFloatLabel variant="in">
          <BravoInputText id="value2" v-model="value2" :invalid="!value2" />
          <label for="value2">Username</label>
        </BravoFloatLabel>

        <BravoFloatLabel variant="on">
          <BravoInputText id="value3" v-model="value3" :invalid="!value3" />
          <label for="value3">Username</label>
        </BravoFloatLabel>
      </div>
    `,
    }),
};
