import type { Meta, StoryObj } from '@storybook/vue3';
import BravoPasswordReset from '../components/BravoPasswordReset.vue';

const meta = {
    title: 'Components/BravoPasswordReset',
    component: BravoPasswordReset,
    tags: ['autodocs'],
    parameters: {
        layout: 'fullscreen',
        docs: {
            story: {
                height: '500px',
            },
        },
    },
    argTypes: {
        submitHandler: {
            description: 'Function that handles the password reset request',
            table: {
                type: {
                    summary: '(email: string) => Promise<void>',
                },
            },
        },
        checkSsoUser: {
            description: 'Function that checks if the user is an SSO user',
            table: {
                type: {
                    summary: '(email: string) => Promise<boolean>',
                },
            },
        },
        onSubmit: {
            description: 'Emitted when the form is submitted with a valid email',
            table: {
                type: {
                    summary: '(event: { email: string }) => void',
                },
            },
        },
        onBackToLogin: {
            description: 'Emitted when the user clicks the "Back to Login" button on the success screen',
            table: {
                type: {
                    summary: '() => void',
                },
            },
        },
    },
} satisfies Meta<typeof BravoPasswordReset>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default story with basic functionality
export const Default: Story = {
    args: {},
    render: (args) => ({
        components: { BravoPasswordReset },
        setup() {
            const onSubmit = (data: { email: string }) => {
                console.log('Reset password request submitted for:', data.email);
            };
            
            const onBackToLogin = () => {
                console.log('Back to login clicked');
                alert('Navigating back to login screen');
            };
            
            return { args, onSubmit, onBackToLogin };
        },
        template: '<BravoPasswordReset v-bind="args" @submit="onSubmit" @back-to-login="onBackToLogin" />',
    }),
};

// Story with successful reset request
export const SuccessScreen: Story = {
    args: {},
    render: (args) => ({
        components: { BravoPasswordReset },
        setup() {
            const submitHandler = async (email: string) => {
                console.log('Processing password reset for:', email);
                await new Promise(resolve => setTimeout(resolve, 1000));
                return Promise.resolve();
            };
            
            const onSubmit = (data: { email: string }) => {
                console.log('Reset password request submitted for:', data.email);
            };
            
            const onBackToLogin = () => {
                console.log('Back to login clicked');
                alert('Navigating back to login screen');
            };
            
            return { args, submitHandler, onSubmit, onBackToLogin };
        },
        template: `
            <div>
                <h3>Success Screen</h3>
                <p>Submit the form to see the success screen.</p>
                <BravoPasswordReset 
                    v-bind="args" 
                    :submit-handler="submitHandler"
                    @submit="onSubmit" 
                    @back-to-login="onBackToLogin"
                />
            </div>
        `,
    }),
};

// Story demonstrating SSO user scenario
export const SsoUserScreen: Story = {
    args: {},
    render: (args) => ({
        components: { BravoPasswordReset },
        setup() {
            // This handler always returns true to force showing the SSO user screen
            const checkSsoUser = async (email: string) => {
                console.log('Checking if user is SSO user:', email);
                // Add a short delay to simulate network request
                await new Promise(resolve => setTimeout(resolve, 500));
                // Always return true to force the SSO screen for this demo
                return true;
            };
            
            const onSubmit = (data: { email: string }) => {
                console.log('Reset password request submitted for:', data.email);
            };
            
            const onBackToLogin = () => {
                console.log('Back to login clicked');
                alert('Navigating back to login screen');
            };
            
            return { args, checkSsoUser, onSubmit, onBackToLogin };
        },
        template: `
            <div>
                <h3>SSO User Screen</h3>
                <p>This story always shows the SSO user screen, regardless of the email entered.</p>
                <BravoPasswordReset 
                    v-bind="args" 
                    :check-sso-user="checkSsoUser"
                    @submit="onSubmit" 
                    @back-to-login="onBackToLogin"
                />
            </div>
        `,
    }),
};

// Story demonstrating conditional SSO detection
export const ConditionalSsoDetectionEmailEndsWithCompany: Story = {
    args: {},
    render: (args) => ({
        components: { BravoPasswordReset },
        setup() {
            // This handler checks if the email ends with @company.com
            const checkSsoUser = async (email: string) => {
                console.log('Checking if user is SSO user:', email);
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // For demo purposes, any email with @company.com is considered an SSO user
                return email.endsWith('@company.com');
            };
            
            const onSubmit = (data: { email: string }) => {
                console.log('Reset password request submitted for:', data.email);
            };
            
            const onBackToLogin = () => {
                console.log('Back to login clicked');
                alert('Navigating back to login screen');
            };
            
            return { args, checkSsoUser, onSubmit, onBackToLogin };
        },
        template: `
            <div>
                <h3>Conditional SSO Detection</h3>
                <p>Enter an email with <strong>@company.com</strong> to see the SSO user screen, or any other email for the regular password reset flow.</p>
                <BravoPasswordReset 
                    v-bind="args" 
                    :check-sso-user="checkSsoUser"
                    @submit="onSubmit" 
                    @back-to-login="onBackToLogin"
                />
            </div>
        `,
    }),
};

// Story demonstrating slow loading state
export const SlowResponse: Story = {
    args: {},
    render: (args) => ({
        components: { BravoPasswordReset },
        setup() {
            const submitHandler = async (email: string) => {
                await new Promise(resolve => setTimeout(resolve, 3000));
                console.log('Password reset processed slowly for:', email);
                return Promise.resolve();
            };
            
            const onSubmit = (data: { email: string }) => {
                console.log('Reset password request submitted for:', data.email);
            };
            
            const onBackToLogin = () => {
                console.log('Back to login clicked');
                alert('Navigating back to login screen');
            };
            
            return { args, submitHandler, onSubmit, onBackToLogin };
        },
        template: `
            <div>
                <h3>Slow Response</h3>
                <p>This demonstrates a slow server response (3 seconds) to clearly show the loading state.</p>
                <BravoPasswordReset 
                    v-bind="args" 
                    :submit-handler="submitHandler"
                    @submit="onSubmit" 
                    @back-to-login="onBackToLogin"
                />
            </div>
        `,
    }),
};

// Story demonstrating error state
export const WithError: Story = {
    args: {},
    render: (args) => ({
        components: { BravoPasswordReset },
        setup() {
            const submitHandler = async (email: string) => {
                await new Promise(resolve => setTimeout(resolve, 1000));
                console.log('Error processing request for:', email);
                return Promise.reject(new Error('Something went wrong and we were unable to process your request. Please check your email and try again.'));
            };
            
            const onSubmit = (data: { email: string }) => {
                console.log('Reset password request submitted for:', data.email);
            };
            
            const onBackToLogin = () => {
                console.log('Back to login clicked');
                alert('Navigating back to login screen');
            };
            
            return { args, submitHandler, onSubmit, onBackToLogin };
        },
        template: `
            <div>
                <h3>Error State</h3>
                <p>This demonstrates the component displaying an error message from the server.</p>
                <BravoPasswordReset 
                    v-bind="args" 
                    :submit-handler="submitHandler"
                    @submit="onSubmit" 
                    @back-to-login="onBackToLogin"
                />
            </div>
        `,
    }),
}; 