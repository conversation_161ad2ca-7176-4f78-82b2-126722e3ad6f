import Dropdown from 'primevue/dropdown';
import { Meta, StoryObj } from '@storybook/vue3';

const dropdownOptions = ['Edit', 'Clone', 'Archive', 'Delete'];

export default {
    component: Dropdown,
    title: 'Legacy & Custom/Inputs/DropDownButton',
    parameters: {
        docs: {
            description: {
                component: 'Launches a menu of clickable options',
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        size: { control: 'select', options: ['small', 'normal', 'large'] },
        onClick: { control: 'function' },
    },
} as Meta<typeof Dropdown>;

type Story = StoryObj<typeof Dropdown>;

export const Default: Story = {
    args: { options: dropdownOptions, placeholder: 'Actions' },
    render: (args) => ({
        components: {
            Dropdown,
        },
        data: () => ({ args, value: null }),
        template: `<Dropdown v-bind="args" v-model="value" />`,
    }),
};
