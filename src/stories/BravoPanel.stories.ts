import type { Meta, StoryObj } from '@storybook/vue3';
import BravoPanel from '../components/BravoPanel.vue';
import Button from 'primevue/button';

const meta = {
    title: 'Panel/Panel',
    component: BravoPanel,
    tags: ['autodocs'],
    argTypes: {
        header: { control: 'text' },
        toggleable: { control: 'boolean' },
        collapsed: { control: 'boolean' },
        pt: { control: 'object' },
    },
} satisfies Meta<typeof BravoPanel>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    args: {
        header: 'Basic Panel',
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoPanel header="Basic Panel">
    <p class="m-0">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
      Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
    </p>
  </BravoPanel>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoPanel },
        template: `
      <BravoPanel header="Basic Panel">
        <p>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
          Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
        </p>
      </BravoPanel>
    `,
    }),
};

// Toggleable story
export const Toggleable: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoPanel toggleable>
    <template #header>
      <span>Toggleable Panel</span>
    </template>
    <p class="m-0">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
      Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
    </p>
  </BravoPanel>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoPanel },
        template: `
      <BravoPanel toggleable v-bind="args">
        <template #header>
          <span>Toggleable Panel</span>
        </template>
        <p class="m-0">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
          Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
        </p>
      </BravoPanel>
    `,
    }),
};

// Custom Header story
export const CustomHeader: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoPanel toggleable>
    <template #header>
      <div class="flex align-items-center gap-2">
        <span class="pi pi-cog"></span>
        <span>Custom Header</span>
      </div>
    </template>
    <p class="m-0">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
      Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
    </p>
  </BravoPanel>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoPanel },
        template: `
      <BravoPanel toggleable v-bind="args">
        <template #header>
          <div class="flex align-items-center gap-2">
            <span class="pi pi-cog"></span>
            <span>Custom Header</span>
          </div>
        </template>
        <p class="m-0">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
          Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
        </p>
      </BravoPanel>
    `,
    }),
};
