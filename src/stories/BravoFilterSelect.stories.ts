import type { Meta, StoryObj } from '@storybook/vue3';
import BravoFilterSelect from '../components/BravoFilterSelect.vue';
import { ref } from 'vue';

// Sample data
const statusOptions = [
    { label: 'Active', value: 'active' },
    { label: 'Inactive', value: 'inactive' },
    { label: 'Pending', value: 'pending' },
    { label: 'Archived', value: 'archived' },
];

const categoryOptions = [
    { label: 'Electronics', value: 'electronics' },
    { label: 'Clothing', value: 'clothing' },
    { label: 'Books', value: 'books' },
    { label: 'Home & Garden', value: 'home' },
    { label: 'Sports', value: 'sports' },
    { label: 'Toys', value: 'toys' },
];

const priorityOptions = [
    { label: 'High', value: 'high' },
    { label: 'Medium', value: 'medium' },
    { label: 'Low', value: 'low' },
];

const departmentOptions = [
    { label: 'Engineering', value: 'engineering' },
    { label: 'Marketing', value: 'marketing' },
    { label: 'Sales', value: 'sales' },
    { label: 'Support', value: 'support' },
    { label: 'HR', value: 'hr' },
];

const meta = {
    title: 'Filters/BravoFilterSelect',
    component: BravoFilterSelect,
    tags: ['autodocs'],
    argTypes: {
        label: { control: 'text' },
        placeholder: { control: 'text' },
        filterOptions: { control: 'object' },
        optionLabel: { control: 'text' },
        optionValue: { control: 'text' },
        id: { control: 'text' },
        dataTestId: { control: 'text' },
        modelValue: { control: 'text' },
        'update:modelValue': { action: 'update:modelValue' },
        'filter-change': { action: 'filter-change' },
        focus: { action: 'focus' },
        blur: { action: 'blur' },
    },
    parameters: {
        docs: {
            description: {
                component:
                    'A single-select filter component based on BravoSelectField. The component provides a clean, minimal design for filtering with single option selection.',
            },
        },
    },
} satisfies Meta<typeof BravoFilterSelect>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic filter
export const Basic: Story = {
    args: {
        label: 'Status',
        placeholder: 'Select status',
        filterOptions: statusOptions,
        id: 'status-filter',
        dataTestId: 'status-filter',
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFilterSelect
    v-model="selectedStatus"
    label="Status"
    placeholder="Select status"
    :filterOptions="statusOptions"
    id="status-filter"
    data-test-id="status-filter"
    @filter-change="onFilterChange"
  />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoFilterSelect },
        setup() {
            const selectedStatus = ref(null);
            const onFilterChange = (value: unknown) => {
                console.log('Filter changed:', value);
            };
            return { ...args, selectedStatus, onFilterChange };
        },
        template: `
      <div style="padding: 20px; background-color: white;">
        <BravoFilterSelect
          v-model="selectedStatus"
          :label="label"
          :placeholder="placeholder"
          :filterOptions="filterOptions"
          :id="id"
          :data-test-id="dataTestId"
          @filter-change="onFilterChange"
        />
      </div>
    `,
    }),
};

// Multiple filters
export const MultipleFilters: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div style="display: flex; gap: 1rem;">
    <BravoFilterSelect
      v-model="selectedStatus"
      label="Status"
      placeholder="Select status"
      :filterOptions="statusOptions"
      id="status-filter"
      data-test-id="status-filter"
      @filter-change="onFilterChange"
    />
    <BravoFilterSelect
      v-model="selectedCategory"
      label="Category"
      placeholder="Select category"
      :filterOptions="categoryOptions"
      id="category-filter"
      data-test-id="category-filter"
      @filter-change="onFilterChange"
    />
    <BravoFilterSelect
      v-model="selectedPriority"
      label="Priority"
      placeholder="Select priority"
      :filterOptions="priorityOptions"
      id="priority-filter"
      data-test-id="priority-filter"
      @filter-change="onFilterChange"
    />
  </div>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoFilterSelect },
        setup() {
            const selectedStatus = ref(null);
            const selectedCategory = ref(null);
            const selectedPriority = ref(null);
            const onFilterChange = (value: unknown) => {
                console.log('Filter changed:', value);
            };
            return { 
                statusOptions, 
                categoryOptions, 
                priorityOptions,
                selectedStatus, 
                selectedCategory, 
                selectedPriority,
                onFilterChange 
            };
        },
        template: `
      <div style="padding: 20px; background-color: white; display: flex; gap: 1rem; flex-wrap: wrap;">
        <BravoFilterSelect
          v-model="selectedStatus"
          label="Status"
          placeholder="Select status"
          :filterOptions="statusOptions"
          id="status-filter"
          data-test-id="status-filter"
          @filter-change="onFilterChange"
        />
        <BravoFilterSelect
          v-model="selectedCategory"
          label="Category"
          placeholder="Select category"
          :filterOptions="categoryOptions"
          id="category-filter"
          data-test-id="category-filter"
          @filter-change="onFilterChange"
        />
        <BravoFilterSelect
          v-model="selectedPriority"
          label="Priority"
          placeholder="Select priority"
          :filterOptions="priorityOptions"
          id="priority-filter"
          data-test-id="priority-filter"
          @filter-change="onFilterChange"
        />
      </div>
    `,
    }),
};

// With preselected value
export const WithPreselectedValue: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFilterSelect
    v-model="selectedPriority"
    label="Priority"
    placeholder="Select priority"
    :filterOptions="priorityOptions"
    id="priority-filter"
    data-test-id="priority-filter"
    @filter-change="onFilterChange"
  />
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoFilterSelect },
        setup() {
            const selectedPriority = ref('high');
            const onFilterChange = (value: unknown) => {
                console.log('Filter changed:', value);
            };
            return { priorityOptions, selectedPriority, onFilterChange };
        },
        template: `
      <div style="padding: 20px; background-color: white;">
        <BravoFilterSelect
          v-model="selectedPriority"
          label="Priority"
          placeholder="Select priority"
          :filterOptions="priorityOptions"
          id="priority-filter"
          data-test-id="priority-filter"
          @filter-change="onFilterChange"
        />
      </div>
    `,
    }),
};

// Disabled state
export const DisabledState: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFilterSelect
    v-model="selectedStatus"
    label="Status (Disabled)"
    placeholder="Select status"
    :filterOptions="statusOptions"
    id="status-filter"
    data-test-id="status-filter"
    disabled
  />
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoFilterSelect },
        setup() {
            const selectedStatus = ref(null);
            return { statusOptions, selectedStatus };
        },
        template: `
      <div style="padding: 20px; background-color: white;">
        <BravoFilterSelect
          v-model="selectedStatus"
          label="Status (Disabled)"
          placeholder="Select status"
          :filterOptions="statusOptions"
          id="status-filter"
          data-test-id="status-filter"
          disabled
        />
      </div>
    `,
    }),
};

// View mode
export const ViewMode: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFilterSelect
    v-model="selectedDepartment"
    label="Department (View Mode)"
    placeholder="Select department"
    :filterOptions="departmentOptions"
    id="department-filter"
    data-test-id="department-filter"
    view
  />
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoFilterSelect },
        setup() {
            const selectedDepartment = ref('engineering');
            return { departmentOptions, selectedDepartment };
        },
        template: `
      <div style="padding: 20px; background-color: white;">
        <BravoFilterSelect
          v-model="selectedDepartment"
          label="Department (View Mode)"
          placeholder="Select department"
          :filterOptions="departmentOptions"
          id="department-filter"
          data-test-id="department-filter"
          view
        />
      </div>
    `,
    }),
};

// Text mode
export const TextMode: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFilterSelect
    v-model="selectedCategory"
    label="Category (Text Mode)"
    placeholder="Select category"
    :filterOptions="categoryOptions"
    id="category-filter"
    data-test-id="category-filter"
    text
  />
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoFilterSelect },
        setup() {
            const selectedCategory = ref('electronics');
            return { categoryOptions, selectedCategory };
        },
        template: `
      <div style="padding: 20px; background-color: white;">
        <BravoFilterSelect
          v-model="selectedCategory"
          label="Category (Text Mode)"
          placeholder="Select category"
          :filterOptions="categoryOptions"
          id="category-filter"
          data-test-id="category-filter"
          text
        />
      </div>
    `,
    }),
};

// Custom styling
export const CustomStyling: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFilterSelect
    v-model="selectedStatus"
    label="Status"
    placeholder="Select status"
    :filterOptions="statusOptions"
    id="status-filter"
    data-test-id="status-filter"
    class="custom-filter"
    style="width: 300px;"
  />
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoFilterSelect },
        setup() {
            const selectedStatus = ref(null);
            return { statusOptions, selectedStatus };
        },
        template: `
      <div style="padding: 20px; background-color: white;">
        <BravoFilterSelect
          v-model="selectedStatus"
          label="Status"
          placeholder="Select status"
          :filterOptions="statusOptions"
          id="status-filter"
          data-test-id="status-filter"
          class="custom-filter"
          style="width: 300px;"
        />
      </div>
    `,
    }),
};

// Without label
export const WithoutLabel: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFilterSelect
    v-model="selectedStatus"
    placeholder="Filter by status"
    :filterOptions="statusOptions"
    id="status-filter"
    data-test-id="status-filter"
  />
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoFilterSelect },
        setup() {
            const selectedStatus = ref(null);
            return { statusOptions, selectedStatus };
        },
        template: `
      <div style="padding: 20px; background-color: white;">
        <BravoFilterSelect
          v-model="selectedStatus"
          placeholder="Filter by status"
          :filterOptions="statusOptions"
          id="status-filter"
          data-test-id="status-filter"
        />
      </div>
    `,
    }),
};

// Interactive example
export const InteractiveExample: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div>
    <div style="display: flex; gap: 1rem; margin-bottom: 1rem;">
      <BravoFilterSelect
        v-model="selectedStatus"
        label="Status"
        placeholder="Select status"
        :filterOptions="statusOptions"
        id="status-filter"
        data-test-id="status-filter"
        @filter-change="onFilterChange"
      />
      <BravoFilterSelect
        v-model="selectedPriority"
        label="Priority"
        placeholder="Select priority"
        :filterOptions="priorityOptions"
        id="priority-filter"
        data-test-id="priority-filter"
        @filter-change="onFilterChange"
      />
    </div>
    <div style="padding: 1rem; background-color: #f5f5f5; border-radius: 4px;">
      <h4>Selected Filters:</h4>
      <p><strong>Status:</strong> {{ selectedStatus || 'None' }}</p>
      <p><strong>Priority:</strong> {{ selectedPriority || 'None' }}</p>
    </div>
  </div>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoFilterSelect },
        setup() {
            const selectedStatus = ref(null);
            const selectedPriority = ref(null);
            const onFilterChange = (value: unknown) => {
                console.log('Filter changed:', value);
            };
            return { 
                statusOptions, 
                priorityOptions,
                selectedStatus, 
                selectedPriority,
                onFilterChange 
            };
        },
        template: `
      <div style="padding: 20px; background-color: white;">
        <div style="display: flex; gap: 1rem; margin-bottom: 1rem;">
          <BravoFilterSelect
            v-model="selectedStatus"
            label="Status"
            placeholder="Select status"
            :filterOptions="statusOptions"
            id="status-filter"
            data-test-id="status-filter"
            @filter-change="onFilterChange"
          />
          <BravoFilterSelect
            v-model="selectedPriority"
            label="Priority"
            placeholder="Select priority"
            :filterOptions="priorityOptions"
            id="priority-filter"
            data-test-id="priority-filter"
            @filter-change="onFilterChange"
          />
        </div>
        <div style="padding: 1rem; background-color: #f5f5f5; border-radius: 4px;">
          <h4 style="margin: 0 0 0.5rem 0;">Selected Filters:</h4>
          <p style="margin: 0.25rem 0;"><strong>Status:</strong> {{ selectedStatus || 'None' }}</p>
          <p style="margin: 0.25rem 0;"><strong>Priority:</strong> {{ selectedPriority || 'None' }}</p>
        </div>
      </div>
    `,
    }),
}; 