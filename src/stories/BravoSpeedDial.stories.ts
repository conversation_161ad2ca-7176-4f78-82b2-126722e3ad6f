import type { Meta, StoryObj } from '@storybook/vue3';
import BravoSpeedDial from '../components/BravoSpeedDial.vue';
import { ref } from 'vue';

interface BravoSpeedDialProps {
    model?: any[];
    direction?: 'up' | 'down' | 'left' | 'right';
    type?: 'linear' | 'circle' | 'semi-circle' | 'quarter-circle';
    radius?: number;
    mask?: boolean;
    disabled?: boolean;
    hideOnClickOutside?: boolean;
    buttonClassName?: string;
    maskClassName?: string;
    showIcon?: string;
    hideIcon?: string;
    rotateAnimation?: boolean;
}

const meta = {
    title: 'Button/SpeedDial',
    component: BravoSpeedDial,
    tags: ['autodocs'],
    argTypes: {
        model: { control: 'object' },
        direction: {
            control: 'select',
            options: ['up', 'down', 'left', 'right'],
        },
        type: {
            control: 'select',
            options: ['linear', 'circle', 'semi-circle', 'quarter-circle'],
        },
        radius: { control: 'number' },
        mask: { control: 'boolean' },
        disabled: { control: 'boolean' },
        hideOnClickOutside: { control: 'boolean' },
        buttonClassName: { control: 'text' },
        maskClassName: { control: 'text' },
        showIcon: { control: 'text' },
        hideIcon: { control: 'text' },
        rotateAnimation: { control: 'boolean' },
    },
} as Meta<typeof BravoSpeedDial>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoSpeedDial
    :model="items"
    direction="up"
    :radius="80"
  />
</template>

<script setup>
import { ref } from 'vue';

const items = ref([
  {
    label: 'Add',
    icon: 'pi pi-pencil',
    command: () => {
      console.log('Add clicked');
    }
  },
  {
    label: 'Update',
    icon: 'pi pi-refresh',
    command: () => {
      console.log('Update clicked');
    }
  },
  {
    label: 'Delete',
    icon: 'pi pi-trash',
    command: () => {
      console.log('Delete clicked');
    }
  }
]);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoSpeedDial },
        setup() {
            const items = ref([
                {
                    label: 'Add',
                    icon: 'pi pi-pencil',
                    command: () => {
                        console.log('Add clicked');
                    },
                },
                {
                    label: 'Update',
                    icon: 'pi pi-refresh',
                    command: () => {
                        console.log('Update clicked');
                    },
                },
                {
                    label: 'Delete',
                    icon: 'pi pi-trash',
                    command: () => {
                        console.log('Delete clicked');
                    },
                },
            ]);

            return { items };
        },
        template: `
      <BravoSpeedDial
        :model="items"
        direction="up"
        :radius="80"
      />
    `,
    }),
};

// Circle type story
export const CircleType: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoSpeedDial
    :model="items"
    type="circle"
    :radius="120"
    buttonClassName="p-button-warning"
  />
</template>

<script setup>
import { ref } from 'vue';

const items = ref([
  {
    label: 'Add',
    icon: 'pi pi-pencil',
    command: () => {
      console.log('Add clicked');
    }
  },
  {
    label: 'Update',
    icon: 'pi pi-refresh',
    command: () => {
      console.log('Update clicked');
    }
  },
  {
    label: 'Delete',
    icon: 'pi pi-trash',
    command: () => {
      console.log('Delete clicked');
    }
  },
  {
    label: 'Share',
    icon: 'pi pi-share-alt',
    command: () => {
      console.log('Share clicked');
    }
  }
]);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoSpeedDial },
        setup() {
            const items = ref([
                {
                    label: 'Add',
                    icon: 'pi pi-pencil',
                    command: () => {
                        console.log('Add clicked');
                    },
                },
                {
                    label: 'Update',
                    icon: 'pi pi-refresh',
                    command: () => {
                        console.log('Update clicked');
                    },
                },
                {
                    label: 'Delete',
                    icon: 'pi pi-trash',
                    command: () => {
                        console.log('Delete clicked');
                    },
                },
                {
                    label: 'Share',
                    icon: 'pi pi-share-alt',
                    command: () => {
                        console.log('Share clicked');
                    },
                },
            ]);

            return { items };
        },
        template: `
      <BravoSpeedDial
        :model="items"
        type="circle"
        :radius="120"
        buttonClassName="p-button-warning"
      />
    `,
    }),
};
