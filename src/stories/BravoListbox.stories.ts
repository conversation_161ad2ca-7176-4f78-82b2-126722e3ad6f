import type { Meta, StoryObj } from '@storybook/vue3';
import BravoListbox from '../components/BravoListbox.vue';
import { ref } from 'vue';
import Listbox from 'primevue/listbox';

interface BravoListboxProps {
    options?: any[];
    modelValue?: any;
    optionLabel?: string;
    optionValue?: string;
    multiple?: boolean;
    filter?: boolean;
    checkmark?: boolean;
    invalid?: boolean;
    disabled?: boolean;
}

const meta = {
    title: 'Form/Listbox',
    component: BravoListbox,
    tags: ['autodocs'],
    argTypes: {
        options: { control: 'object' },
        modelValue: { control: 'object' },
        optionLabel: { control: 'text' },
        optionValue: { control: 'text' },
        multiple: { control: 'boolean' },
        filter: { control: 'boolean' },
        checkmark: { control: 'boolean' },
        invalid: { control: 'boolean' },
        disabled: { control: 'boolean' },
    },
} satisfies Meta<typeof BravoListbox>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <Listbox
    v-model="selectedCity"
    :options="cities"
    optionLabel="name"
    class="w-full md:w-56"
  />
</template>

<script setup>
import { ref } from 'vue';

const selectedCity = ref(null);
const cities = ref([
  { name: 'New York', code: 'NY' },
  { name: 'Rome', code: 'RM' },
  { name: 'London', code: 'LDN' },
  { name: 'Istanbul', code: 'IST' },
  { name: 'Paris', code: 'PRS' }
]);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoListbox },
        setup() {
            const selectedCity = ref(null);
            const cities = ref([
                { name: 'New York', code: 'NY' },
                { name: 'Rome', code: 'RM' },
                { name: 'London', code: 'LDN' },
                { name: 'Istanbul', code: 'IST' },
                { name: 'Paris', code: 'PRS' },
            ]);

            return { selectedCity, cities };
        },
        template: `
      <BravoListbox
        v-model="selectedCity"
        :options="cities"
        optionLabel="name"
        class="w-full md:w-56"
      />
    `,
    }),
};

// Multiple selection story
export const Multiple: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <Listbox
    v-model="selectedCities"
    :options="cities"
    optionLabel="name"
    multiple
    class="w-full md:w-56"
  />
</template>

<script setup>
import { ref } from 'vue';

const selectedCities = ref([]);
const cities = ref([
  { name: 'New York', code: 'NY' },
  { name: 'Rome', code: 'RM' },
  { name: 'London', code: 'LDN' },
  { name: 'Istanbul', code: 'IST' },
  { name: 'Paris', code: 'PRS' }
]);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoListbox },
        setup() {
            const selectedCities = ref([]);
            const cities = ref([
                { name: 'New York', code: 'NY' },
                { name: 'Rome', code: 'RM' },
                { name: 'London', code: 'LDN' },
                { name: 'Istanbul', code: 'IST' },
                { name: 'Paris', code: 'PRS' },
            ]);

            return { selectedCities, cities };
        },
        template: `
      <BravoListbox
        v-model="selectedCities"
        :options="cities"
        optionLabel="name"
        multiple
        class="w-full md:w-56"
      />
    `,
    }),
};

// Filter story
export const Filter: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <Listbox
    v-model="selectedCity"
    :options="cities"
    optionLabel="name"
    filter
    class="w-full md:w-56"
  />
</template>

<script setup>
import { ref } from 'vue';

const selectedCity = ref(null);
const cities = ref([
  { name: 'New York', code: 'NY' },
  { name: 'Rome', code: 'RM' },
  { name: 'London', code: 'LDN' },
  { name: 'Istanbul', code: 'IST' },
  { name: 'Paris', code: 'PRS' }
]);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoListbox },
        setup() {
            const selectedCity = ref(null);
            const cities = ref([
                { name: 'New York', code: 'NY' },
                { name: 'Rome', code: 'RM' },
                { name: 'London', code: 'LDN' },
                { name: 'Istanbul', code: 'IST' },
                { name: 'Paris', code: 'PRS' },
            ]);

            return { selectedCity, cities };
        },
        template: `
      <BravoListbox
        v-model="selectedCity"
        :options="cities"
        optionLabel="name"
        filter
        class="w-full md:w-56"
      />
    `,
    }),
};
