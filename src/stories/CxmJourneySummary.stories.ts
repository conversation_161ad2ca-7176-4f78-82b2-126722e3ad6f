import { CxmJourneySummary } from '../components';
import { Meta, StoryObj } from '@storybook/vue3';
// Storybook Documentation: https://storybook.js.org/docs/vue/writing-stories/introduction

// Default export defines metadata about your component
export default {
    component: CxmJourneySummary,
    title: 'Legacy & Custom/JourneySummary', // Name without Cxm prefix for display in SB
    parameters: {
        docs: {
            description: {
                component: 'Journey Summary shows the progress of journey stages.', // Add description for component
            },
        },
    },
    tags: ['autodocs'], // Include the auto generated docsPage: https://storybook.js.org/docs/7.0/vue/writing-docs/docs-page
    argTypes: {
        // Add controls for specific arguments
    },
} as Meta<typeof CxmJourneySummary>;

type Story = StoryObj<typeof CxmJourneySummary>;

export const Running: Story = {
    args: {
        dataTestId: '',
        truncateDescription: true,
        workflow: {
            status: 'Running',
            name: 'Archers Secret Spy Mission',
            description:
                'Blue print for a successful mission yo! mostly just drinking while others do everything. nice okay okay okay lanaaa!!!!',
            actions: [
                {
                    internalName: 'prep for mission',
                    status: 'Completed',
                    type: 'com.ovationcxm.journeys.stage',
                },
                {
                    internalName: 'start mission',
                    status: 'Completed',
                    type: 'com.ovationcxm.journeys.stage',
                },
                {
                    internalName: 'help the team',
                    status: 'Cancelled',
                    type: 'com.ovationcxm.journeys.stage',
                },
                {
                    internalName: 'finish mission really long stage name finish mission hello  hello yes ',
                    status: 'Initialized',
                    type: 'com.ovationcxm.journeys.stage',
                },
            ],
        },
    },
    render: (args) => ({
        components: { CxmJourneySummary },
        setup() {
            return { args };
        },
        template: '<div style="max-width:400px;"><CxmJourneySummary v-bind="args" /></div>',
    }),
};

export const Completed: Story = {
    args: {
        dataTestId: '',
        truncateDescription: true,
        workflow: {
            status: 'Completed',
            name: 'Archers Secret Spy Mission',
            description:
                'Blue print for a successful mission yo! mostly just drinking while others do everything. nice okay okay okay lanaaa!!!!',
            actions: [
                {
                    internalName: 'prep for mission',
                    status: 'Completed',
                    type: 'com.ovationcxm.journeys.stage',
                },
                {
                    internalName: 'start mission',
                    status: 'Completed',
                    type: 'com.ovationcxm.journeys.stage',
                },
            ],
        },
    },
    render: (args) => ({
        components: { CxmJourneySummary },
        setup() {
            return { args };
        },
        template: '<div style="max-width:400px;"><CxmJourneySummary v-bind="args" /></div>',
    }),
};

export const Closed: Story = {
    args: {
        dataTestId: '',
        truncateDescription: true,
        workflow: {
            status: 'Cancelled',
            name: 'Archers Secret Spy Mission',
            description:
                'Blue print for a successful mission yo! mostly just drinking while others do everything. nice okay okay okay lanaaa!!!!',
            actions: [
                {
                    internalName: 'prep for mission',
                    status: 'Completed',
                    type: 'com.ovationcxm.journeys.stage',
                },
                {
                    internalName: 'start mission',
                    status: 'Cancelled',
                    type: 'com.ovationcxm.journeys.stage',
                },
                {
                    internalName: 'help the team',
                    status: 'Cancelled',
                    type: 'com.ovationcxm.journeys.stage',
                },
                {
                    internalName: 'finish mission really long stage name finish mission hello  hello yes ',
                    status: 'Cancelled',
                    type: 'com.ovationcxm.journeys.stage',
                },
            ],
        },
    },
    render: (args) => ({
        components: { CxmJourneySummary },
        setup() {
            return { args };
        },
        template: '<div style="max-width:400px;"><CxmJourneySummary v-bind="args" /></div>',
    }),
};
