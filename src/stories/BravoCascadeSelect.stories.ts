import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3';
import BravoCascadeSelect from '../components/BravoCascadeSelect.vue';
import CascadeSelect from 'primevue/cascadeselect';

// Define the props interface for CascadeSelect component
interface BravoCascadeSelectProps {
    modelValue?: any;
    options?: any[];
    optionLabel?: string;
    optionValue?: string;
    optionGroupLabel?: string;
    optionGroupChildren?: string[];
    placeholder?: string;
    disabled?: boolean;
    loading?: boolean;
    variant?: 'outlined' | 'filled';
    invalid?: boolean;
    tabindex?: string;
    inputId?: string;
    ariaLabelledby?: string;
    appendTo?: string;
    panelClass?: string;
    panelStyle?: string | object;
    inputClass?: string;
    showClear?: boolean;
    size?: 'small' | 'large';
}

// Sample data for the stories
const countries = [
    {
        name: 'Australia',
        code: 'AU',
        states: [
            {
                name: 'New South Wales',
                cities: [
                    { cname: 'Sydney', code: 'SYD' },
                    { cname: 'Newcastle', code: 'NCL' },
                    { cname: 'Wollongong', code: 'WOL' },
                ],
            },
            {
                name: 'Queensland',
                cities: [
                    { cname: 'Brisbane', code: 'BNE' },
                    { cname: 'Gold Coast', code: 'GCT' },
                    { cname: 'Townsville', code: 'TSV' },
                ],
            },
        ],
    },
    {
        name: 'United States',
        code: 'US',
        states: [
            {
                name: 'California',
                cities: [
                    { cname: 'Los Angeles', code: 'LA' },
                    { cname: 'San Diego', code: 'SD' },
                    { cname: 'San Francisco', code: 'SF' },
                ],
            },
            {
                name: 'Florida',
                cities: [
                    { cname: 'Miami', code: 'MI' },
                    { cname: 'Orlando', code: 'ORL' },
                    { cname: 'Tampa', code: 'TPA' },
                ],
            },
        ],
    },
    {
        name: 'Canada',
        code: 'CA',
        states: [
            {
                name: 'Ontario',
                cities: [
                    { cname: 'Toronto', code: 'TOR' },
                    { cname: 'Ottawa', code: 'OTT' },
                    { cname: 'Hamilton', code: 'HAM' },
                ],
            },
            {
                name: 'Quebec',
                cities: [
                    { cname: 'Montreal', code: 'MTL' },
                    { cname: 'Quebec City', code: 'QUE' },
                    { cname: 'Laval', code: 'LAV' },
                ],
            },
        ],
    },
];

// Default panel style for horizontal expansion
const defaultPanelStyle = {
    display: 'flex',
    flexDirection: 'row',
};

const meta = {
    title: 'Form/CascadeSelect',
    component: BravoCascadeSelect,
    tags: ['autodocs'],
    argTypes: {
        modelValue: { control: 'object' },
        options: { control: 'object' },
        optionLabel: { control: 'text' },
        optionValue: { control: 'text' },
        optionGroupLabel: { control: 'text' },
        optionGroupChildren: { control: 'array' },
        placeholder: { control: 'text' },
        disabled: { control: 'boolean' },
        loading: { control: 'boolean' },
        variant: {
            control: 'select',
            options: ['outlined', 'filled'],
        },
        invalid: { control: 'boolean' },
        tabindex: { control: 'text' },
        inputId: { control: 'text' },
        ariaLabelledby: { control: 'text' },
        appendTo: { control: 'text' },
        panelClass: { control: 'text' },
        panelStyle: { control: 'object' },
        inputClass: { control: 'text' },
        showClear: { control: 'boolean' },
        size: {
            control: 'select',
            options: ['small', 'large'],
        },
    },
} satisfies Meta<typeof BravoCascadeSelect>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    args: {
        options: countries,
        optionLabel: 'cname',
        optionGroupLabel: 'name',
        optionGroupChildren: ['states', 'cities'],
        placeholder: 'Select a City',
        panelStyle: defaultPanelStyle,
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoCascadeSelect 
    v-model="selectedCity" 
    :options="countries" 
    optionLabel="cname" 
    optionGroupLabel="name"
    :optionGroupChildren="['states', 'cities']" 
    placeholder="Select a City" />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoCascadeSelect },
        setup() {
            return { args, countries };
        },
        template: `<BravoCascadeSelect v-bind="args" />`,
    }),
};

// Loading state
export const Loading: Story = {
    args: {
        loading: true,
        placeholder: 'Loading...',
        panelStyle: defaultPanelStyle,
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoCascadeSelect loading placeholder="Loading..." />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoCascadeSelect },
        template: `<BravoCascadeSelect v-bind="args" />`,
    }),
};

// Variants
export const Variants: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-col gap-2">
    <BravoCascadeSelect 
      :options="countries" 
      optionLabel="cname" 
      optionGroupLabel="name"
      :optionGroupChildren="['states', 'cities']" 
      placeholder="Default (Outlined)" />
    <BravoCascadeSelect 
      variant="filled" 
      :options="countries" 
      optionLabel="cname" 
      optionGroupLabel="name"
      :optionGroupChildren="['states', 'cities']" 
      placeholder="Filled" />
  </div>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoCascadeSelect },
        setup() {
            return { countries, defaultPanelStyle };
        },
        template: `
      <div class="flex flex-col gap-2">
        <BravoCascadeSelect 
          :options="countries" 
          optionLabel="cname" 
          optionGroupLabel="name"
          :optionGroupChildren="['states', 'cities']" 
          :panelStyle="defaultPanelStyle"
          placeholder="Default (Outlined)" />
        <BravoCascadeSelect 
          variant="filled" 
          :options="countries" 
          optionLabel="cname" 
          optionGroupLabel="name"
          :optionGroupChildren="['states', 'cities']" 
          :panelStyle="defaultPanelStyle"
          placeholder="Filled" />
      </div>
    `,
    }),
};

// Invalid state
export const Invalid: Story = {
    args: {
        options: countries,
        optionLabel: 'cname',
        optionGroupLabel: 'name',
        optionGroupChildren: ['states', 'cities'],
        placeholder: 'Select a City',
        invalid: true,
        panelStyle: defaultPanelStyle,
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoCascadeSelect 
    invalid
    :options="countries" 
    optionLabel="cname" 
    optionGroupLabel="name"
    :optionGroupChildren="['states', 'cities']" 
    placeholder="Select a City" />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoCascadeSelect },
        setup() {
            return { args };
        },
        template: `<BravoCascadeSelect v-bind="args" />`,
    }),
};

// Disabled state
export const Disabled: Story = {
    args: {
        disabled: true,
        placeholder: 'Disabled',
        panelStyle: defaultPanelStyle,
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoCascadeSelect disabled placeholder="Disabled" />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoCascadeSelect },
        template: `<BravoCascadeSelect v-bind="args" />`,
    }),
};

// Clear Icon
export const ClearIcon: Story = {
    args: {
        options: countries,
        optionLabel: 'cname',
        optionGroupLabel: 'name',
        optionGroupChildren: ['states', 'cities'],
        placeholder: 'Select a City',
        showClear: true,
        panelStyle: defaultPanelStyle,
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoCascadeSelect 
    showClear
    :options="countries" 
    optionLabel="cname" 
    optionGroupLabel="name"
    :optionGroupChildren="['states', 'cities']" 
    placeholder="Select a City" />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoCascadeSelect },
        setup() {
            return { args };
        },
        template: `<BravoCascadeSelect v-bind="args" />`,
    }),
};

// Sizes
export const Sizes: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-col gap-2">
    <BravoCascadeSelect 
      size="small"
      :options="countries" 
      optionLabel="cname" 
      optionGroupLabel="name"
      :optionGroupChildren="['states', 'cities']" 
      placeholder="Small" />
    <BravoCascadeSelect 
      :options="countries" 
      optionLabel="cname" 
      optionGroupLabel="name"
      :optionGroupChildren="['states', 'cities']" 
      placeholder="Normal" />
    <BravoCascadeSelect 
      size="large"
      :options="countries" 
      optionLabel="cname" 
      optionGroupLabel="name"
      :optionGroupChildren="['states', 'cities']" 
      placeholder="Large" />
  </div>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoCascadeSelect },
        setup() {
            return { countries, defaultPanelStyle };
        },
        template: `
      <div class="flex flex-col gap-2">
        <BravoCascadeSelect 
          size="small"
          :options="countries" 
          optionLabel="cname" 
          optionGroupLabel="name"
          :optionGroupChildren="['states', 'cities']" 
          :panelStyle="defaultPanelStyle"
          placeholder="Small" />
        <BravoCascadeSelect 
          :options="countries" 
          optionLabel="cname" 
          optionGroupLabel="name"
          :optionGroupChildren="['states', 'cities']" 
          :panelStyle="defaultPanelStyle"
          placeholder="Normal" />
        <BravoCascadeSelect 
          size="large"
          :options="countries" 
          optionLabel="cname" 
          optionGroupLabel="name"
          :optionGroupChildren="['states', 'cities']" 
          :panelStyle="defaultPanelStyle"
          placeholder="Large" />
      </div>
    `,
    }),
};

// Custom template
export const CustomTemplate: Story = {
    args: {
        options: countries,
        optionLabel: 'cname',
        optionGroupLabel: 'name',
        optionGroupChildren: ['states', 'cities'],
        placeholder: 'Select a City',
        panelStyle: defaultPanelStyle,
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoCascadeSelect 
    :options="countries" 
    optionLabel="cname" 
    optionGroupLabel="name"
    :optionGroupChildren="['states', 'cities']" 
    placeholder="Select a City">
    <template #option="slotProps">
      <div class="flex items-center">
        <img v-if="slotProps.option.states" :alt="slotProps.option.name" src="https://primefaces.org/cdn/primevue/images/flag/flag_placeholder.png" :class="\`flag flag-\${slotProps.option.code.toLowerCase()} mr-2\`" style="width: 18px" />
        <i v-if="slotProps.option.cities" class="pi pi-compass mr-2"></i>
        <i v-if="slotProps.option.cname" class="pi pi-map-marker mr-2"></i>
        <span>{{ slotProps.option.cname || slotProps.option.name }}</span>
      </div>
    </template>
    <template #dropdownicon>
      <i class="pi pi-map" />
    </template>
  </BravoCascadeSelect>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoCascadeSelect },
        setup() {
            return { args, countries };
        },
        template: `
      <BravoCascadeSelect v-bind="args">
        <template #option="slotProps">
          <div class="flex items-center">
            <img v-if="slotProps.option.states" :alt="slotProps.option.name" src="https://primefaces.org/cdn/primevue/images/flag/flag_placeholder.png" :class="\`flag flag-\${slotProps.option.code.toLowerCase()} mr-2\`" style="width: 18px" />
            <i v-if="slotProps.option.cities" class="pi pi-compass mr-2"></i>
            <i v-if="slotProps.option.cname" class="pi pi-map-marker mr-2"></i>
            <span>{{ slotProps.option.cname || slotProps.option.name }}</span>
          </div>
        </template>
        <template #dropdownicon>
          <i class="pi pi-map" />
        </template>
      </BravoCascadeSelect>
    `,
    }),
};
