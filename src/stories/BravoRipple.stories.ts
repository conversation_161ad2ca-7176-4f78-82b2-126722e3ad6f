import type { Meta, StoryObj } from '@storybook/vue3';
import { BravoRipple } from '../directives/BravoRipple';
import Button from 'primevue/button';

const meta = {
    title: 'Misc/Ripple',
    tags: ['autodocs'],
    decorators: [
        (story) => ({
            components: { story },
            directives: { 'bravo-ripple': BravoRipple },
            template: '<story />',
        }),
    ],
} satisfies Meta;

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
    render: () => ({
        components: { Button },
        template: `
      <div class="card flex flex-wrap gap-3">
        <div 
          v-bravo-ripple 
          class="bg-primary w-8rem h-8rem flex align-items-center justify-content-center text-white font-bold border-round cursor-pointer"
        >
          Click Me
        </div>
        <Button v-bravo-ripple label="Button" />
      </div>
    `,
    }),
};
