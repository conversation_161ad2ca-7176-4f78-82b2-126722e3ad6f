import type { Meta, StoryObj } from '@storybook/vue3';
import BravoTreeTable from '../components/BravoTreeTable.vue';
import Column from 'primevue/column';
import { ref } from 'vue';

interface BravoTreeTableProps {
    value?: any[];
    expandedKeys?: object;
    selectionKeys?: object;
    selectionMode?: 'single' | 'multiple' | 'checkbox';
    metaKeySelection?: boolean;
    rows?: number;
    first?: number;
    totalRecords?: number;
    paginator?: boolean;
    paginatorPosition?: 'top' | 'bottom' | 'both';
    currentPageReportTemplate?: string;
    sortField?: string;
    sortOrder?: number;
}

const meta = {
    title: 'Data/TreeTable',
    component: BravoTreeTable,
    tags: ['autodocs'],
    argTypes: {
        value: { control: 'object' },
        expandedKeys: { control: 'object' },
        selectionKeys: { control: 'object' },
        selectionMode: { control: 'select', options: ['single', 'multiple', 'checkbox'] },
        metaKeySelection: { control: 'boolean' },
        rows: { control: 'number' },
        first: { control: 'number' },
        totalRecords: { control: 'number' },
        paginator: { control: 'boolean' },
        paginatorPosition: { control: 'select', options: ['top', 'bottom', 'both'] },
        currentPageReportTemplate: { control: 'text' },
        sortField: { control: 'text' },
        sortOrder: { control: 'number' },
    },
} as Meta<typeof BravoTreeTable>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoTreeTable :value="nodes">
    <Column field="name" header="Name" expander></Column>
    <Column field="size" header="Size"></Column>
    <Column field="type" header="Type"></Column>
  </BravoTreeTable>
</template>

<script setup>
import { ref } from 'vue';
import BravoTreeTable from '../components/BravoTreeTable.vue';
import Column from 'primevue/column';

const nodes = ref([
  {
    key: '0',
    data: { name: 'Documents', size: '75kb', type: 'Folder' },
    children: [
      {
        key: '0-0',
        data: { name: 'Work', size: '55kb', type: 'Folder' },
        children: [
          { key: '0-0-0', data: { name: 'Expenses.doc', size: '30kb', type: 'Document' } },
          { key: '0-0-1', data: { name: 'Resume.doc', size: '25kb', type: 'Document' } }
        ]
      },
      {
        key: '0-1',
        data: { name: 'Home', size: '20kb', type: 'Folder' },
        children: [
          { key: '0-1-0', data: { name: 'Invoices.txt', size: '20kb', type: 'Text' } }
        ]
      }
    ]
  },
  {
    key: '1',
    data: { name: 'Pictures', size: '150kb', type: 'Folder' },
    children: [
      { key: '1-0', data: { name: 'barcelona.jpg', size: '90kb', type: 'Picture' } },
      { key: '1-1', data: { name: 'logo.jpg', size: '30kb', type: 'Picture' } },
      { key: '1-2', data: { name: 'primeui.png', size: '30kb', type: 'Picture' } }
    ]
  }
]);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoTreeTable, Column },
        setup() {
            const nodes = ref([
                {
                    key: '0',
                    data: { name: 'Documents', size: '75kb', type: 'Folder' },
                    children: [
                        {
                            key: '0-0',
                            data: { name: 'Work', size: '55kb', type: 'Folder' },
                            children: [
                                { key: '0-0-0', data: { name: 'Expenses.doc', size: '30kb', type: 'Document' } },
                                { key: '0-0-1', data: { name: 'Resume.doc', size: '25kb', type: 'Document' } },
                            ],
                        },
                        {
                            key: '0-1',
                            data: { name: 'Home', size: '20kb', type: 'Folder' },
                            children: [{ key: '0-1-0', data: { name: 'Invoices.txt', size: '20kb', type: 'Text' } }],
                        },
                    ],
                },
                {
                    key: '1',
                    data: { name: 'Pictures', size: '150kb', type: 'Folder' },
                    children: [
                        { key: '1-0', data: { name: 'barcelona.jpg', size: '90kb', type: 'Picture' } },
                        { key: '1-1', data: { name: 'logo.jpg', size: '30kb', type: 'Picture' } },
                        { key: '1-2', data: { name: 'primeui.png', size: '30kb', type: 'Picture' } },
                    ],
                },
            ]);

            return { nodes, ...args };
        },
        template: `
      <BravoTreeTable :value="nodes">
        <Column field="name" header="Name" expander></Column>
        <Column field="size" header="Size"></Column>
        <Column field="type" header="Type"></Column>
      </BravoTreeTable>
    `,
    }),
};

// Selection and pagination story
export const SelectionAndPagination: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoTreeTable
    :value="nodes"
    paginator
    :rows="5"
    selectionMode="checkbox"
    v-model:selectionKeys="selectedKeys"
  >
    <Column field="name" header="Name" expander></Column>
    <Column field="size" header="Size" sortable></Column>
    <Column field="type" header="Type" sortable></Column>
  </BravoTreeTable>
</template>

<script setup>
import { ref } from 'vue';
import BravoTreeTable from '../components/BravoTreeTable.vue';
import Column from 'primevue/column';

const nodes = ref([
  {
    key: '0',
    data: { name: 'Documents', size: '75kb', type: 'Folder' },
    children: [
      {
        key: '0-0',
        data: { name: 'Work', size: '55kb', type: 'Folder' },
        children: [
          { key: '0-0-0', data: { name: 'Expenses.doc', size: '30kb', type: 'Document' } },
          { key: '0-0-1', data: { name: 'Resume.doc', size: '25kb', type: 'Document' } }
        ]
      },
      {
        key: '0-1',
        data: { name: 'Home', size: '20kb', type: 'Folder' },
        children: [
          { key: '0-1-0', data: { name: 'Invoices.txt', size: '20kb', type: 'Text' } }
        ]
      }
    ]
  },
  {
    key: '1',
    data: { name: 'Pictures', size: '150kb', type: 'Folder' },
    children: [
      { key: '1-0', data: { name: 'barcelona.jpg', size: '90kb', type: 'Picture' } },
      { key: '1-1', data: { name: 'logo.jpg', size: '30kb', type: 'Picture' } },
      { key: '1-2', data: { name: 'primeui.png', size: '30kb', type: 'Picture' } }
    ]
  }
]);

const selectedKeys = ref({});
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoTreeTable, Column },
        setup() {
            const nodes = ref([
                {
                    key: '0',
                    data: { name: 'Documents', size: '75kb', type: 'Folder' },
                    children: [
                        {
                            key: '0-0',
                            data: { name: 'Work', size: '55kb', type: 'Folder' },
                            children: [
                                { key: '0-0-0', data: { name: 'Expenses.doc', size: '30kb', type: 'Document' } },
                                { key: '0-0-1', data: { name: 'Resume.doc', size: '25kb', type: 'Document' } },
                            ],
                        },
                        {
                            key: '0-1',
                            data: { name: 'Home', size: '20kb', type: 'Folder' },
                            children: [{ key: '0-1-0', data: { name: 'Invoices.txt', size: '20kb', type: 'Text' } }],
                        },
                    ],
                },
                {
                    key: '1',
                    data: { name: 'Pictures', size: '150kb', type: 'Folder' },
                    children: [
                        { key: '1-0', data: { name: 'barcelona.jpg', size: '90kb', type: 'Picture' } },
                        { key: '1-1', data: { name: 'logo.jpg', size: '30kb', type: 'Picture' } },
                        { key: '1-2', data: { name: 'primeui.png', size: '30kb', type: 'Picture' } },
                    ],
                },
            ]);

            const selectedKeys = ref({});

            return { nodes, selectedKeys, ...args };
        },
        template: `
      <BravoTreeTable
        :value="nodes"
        paginator
        :rows="5"
        selectionMode="checkbox"
        v-model:selectionKeys="selectedKeys"
      >
        <Column field="name" header="Name" expander></Column>
        <Column field="size" header="Size" sortable></Column>
        <Column field="type" header="Type" sortable></Column>
      </BravoTreeTable>
    `,
    }),
};
