import type { Meta, StoryObj } from '@storybook/vue3'
import { ref } from 'vue'
import BravoSquareImageCropper from '../components/BravoSquareImageCropper.vue'

const meta: Meta<typeof BravoSquareImageCropper> = {
  title: 'Components/BravoSquareImageCropper',
  component: BravoSquareImageCropper,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
A Vue 3 component for cropping images into perfect squares with an intuitive drag-and-resize interface and a Preview/Submit workflow.

## Features
- Upload images via file input or drag & drop
- Interactive square crop overlay with visual feedback
- Drag to move crop area
- Resize handles to adjust crop size (maintains square aspect ratio)
- Visual dimming of areas outside crop selection
- **Preview/Submit workflow**: Preview the crop before submitting
- Clear preview functionality to go back and adjust the crop area
- Modern BravoButton integration with hover states

## Workflow
1. **Upload**: Choose or drag & drop an image
2. **Adjust**: Position and resize the square crop area
3. **Preview**: Click "Preview" to see how the cropped image will look
4. **Submit**: Click "Submit" to emit the final cropped image blob, or "Clear Preview" to go back and adjust

## Events
- \`cropped\`: Emitted with the cropped image blob when "Submit" is clicked (not on preview)
- \`crop-error\`: Emitted when an error occurs during cropping

## Usage
Upload an image, adjust the square crop area by dragging and resizing, click "Preview" to see the result, then "Submit" to emit the cropped image blob.
        `,
      },
    },
  },
  argTypes: {
    onCropped: {
      action: 'cropped',
      description: 'Event emitted with the cropped image blob when Submit is clicked',
    },
    onCropError: {
      action: 'crop-error',
      description: 'Event emitted on crop error',
    },
  },
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: 'Basic usage of the BravoSquareImageCropper component. Upload an image, preview the crop, and submit to generate a blob.',
      },
    },
  },
}

export const WithDragAndDrop: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: 'Try dragging an image file directly onto the upload area instead of using the file picker. The preview/submit workflow remains the same.',
      },
    },
  },
}

// Interactive example with event handling
export const WithEventHandlers: Story = {
  args: {},
  render: (args) => ({
    components: { BravoSquareImageCropper },
    setup() {
      const eventLog = ref<string[]>([])
      const submissionCount = ref(0)

      const handleCropped = (blob: Blob) => {
        submissionCount.value++
        const timestamp = new Date().toLocaleTimeString()
        const logEntry = `[${timestamp}] ✅ SUBMIT EVENT: Cropped image blob emitted (${blob.size} bytes)`
        eventLog.value.unshift(logEntry)
        console.log(logEntry, blob)
        
        // Create a download link to verify the blob
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `cropped-image-${submissionCount.value}.png`
        a.style.display = 'none'
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      }

      const handleError = (error: { message: string; [key: string]: unknown }) => {
        const timestamp = new Date().toLocaleTimeString()
        const logEntry = `[${timestamp}] ❌ ERROR: ${error.message}`
        eventLog.value.unshift(logEntry)
        console.error('❌ Error cropping image:', error)
      }

      return {
        args,
        handleCropped,
        handleError,
        eventLog,
        submissionCount,
      }
    },
    template: `
      <div style="max-width: 800px; padding: 20px;">
        <h3 style="margin-bottom: 16px; color: #374151;">Event Tracking Demo</h3>
        
        <!-- Event Counter -->
        <div style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
          <div style="display: flex; align-items: center; gap: 16px;">
            <div style="background: #0ea5e9; color: white; padding: 8px 16px; border-radius: 4px; font-weight: bold;">
              Submissions: {{ submissionCount }}
            </div>
            <div style="color: #0369a1; font-size: 14px;">
              ℹ️ This counter only increases when you click "Submit", not "Preview"
            </div>
          </div>
        </div>

        <!-- Instructions -->
        <div style="background: #fefce8; border: 1px solid #fde047; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
          <h4 style="margin: 0 0 8px 0; color: #854d0e;">🧪 Test Instructions:</h4>
          <ol style="margin: 0; padding-left: 20px; color: #a16207; line-height: 1.5; font-size: 14px;">
            <li>Upload an image and adjust the crop area</li>
            <li>Click "Preview" - notice NO event is logged and counter stays the same</li>
            <li>Click "Submit" - watch the event log and counter increase</li>
            <li>The submitted image will automatically download to verify the blob</li>
          </ol>
        </div>

        <BravoSquareImageCropper
          v-bind="args"
          @cropped="handleCropped"
          @crop-error="handleError"
        />

        <!-- Event Log -->
        <div v-if="eventLog.length > 0" style="margin-top: 24px;">
          <h4 style="margin-bottom: 12px; color: #374151;">📋 Event Log:</h4>
          <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; max-height: 200px; overflow-y: auto;">
            <div v-for="(log, index) in eventLog" :key="index" style="font-family: monospace; font-size: 13px; margin-bottom: 4px; color: #374151;">
              {{ log }}
            </div>
            <div v-if="eventLog.length === 0" style="color: #9ca3af; font-style: italic;">
              No events logged yet. Upload an image and try the Preview vs Submit buttons.
            </div>
          </div>
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: 'Interactive example with detailed event tracking to demonstrate that Preview does NOT emit events, only Submit does.',
      },
    },
  },
}

// Documentation example
export const Documentation: Story = {
  args: {},
  render: (args) => ({
    components: { BravoSquareImageCropper },
    setup() {
      return { args }
    },
    template: `
      <div style="max-width: 800px; padding: 20px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;">
        <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 24px; margin-bottom: 24px;">
          <h2 style="margin: 0 0 16px 0; color: #1e293b; font-size: 20px;">How to Use</h2>
          <ol style="margin: 0; padding-left: 20px; color: #475569; line-height: 1.6;">
            <li style="margin-bottom: 8px;"><strong>Upload an image:</strong> Click "Choose File" or drag & drop an image onto the upload area</li>
            <li style="margin-bottom: 8px;"><strong>Position the crop area:</strong> Drag the white square to position it over your desired crop area</li>
            <li style="margin-bottom: 8px;"><strong>Resize the crop area:</strong> Drag any corner handle to resize the square crop area</li>
            <li style="margin-bottom: 8px;"><strong>Preview the result:</strong> Click the "Preview" button to see how the cropped image will look</li>
            <li style="margin-bottom: 8px;"><strong>Submit or adjust:</strong> Click "Submit" to emit the cropped blob, or "Clear Preview" to go back and adjust the crop area</li>
          </ol>
        </div>
        
        <BravoSquareImageCropper v-bind="args" />
        
        <div style="background: #fefce8; border: 1px solid #fde047; border-radius: 8px; padding: 16px; margin-top: 24px;">
          <h3 style="margin: 0 0 12px 0; color: #854d0e; font-size: 16px;">💡 Tips</h3>
          <ul style="margin: 0; padding-left: 20px; color: #a16207; line-height: 1.5;">
            <li>The crop area maintains a perfect square aspect ratio</li>
            <li>Areas outside the crop selection are dimmed for better visibility</li>
            <li>Preview lets you see the result before committing to the crop</li>
            <li>The "cropped" event is only emitted when you click "Submit", not during preview</li>
            <li>Use "Clear Preview" to go back and adjust the crop area if needed</li>
            <li>The component works with all common image formats (JPEG, PNG, GIF, etc.)</li>
            <li>Supports both file picker and drag & drop for image upload</li>
          </ul>
        </div>
      </div>
    `,
  }),
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: 'Complete documentation and usage example with instructions for the Preview/Submit workflow.',
      },
    },
  },
}

// Preview workflow demonstration
export const PreviewWorkflowDemo: Story = {
  args: {},
  render: (args) => ({
    components: { BravoSquareImageCropper },
    setup() {
      const actionLog = ref<string[]>([])
      const previewCount = ref(0)
      const submitCount = ref(0)

      const handleCropped = (blob: Blob) => {
        submitCount.value++
        const timestamp = new Date().toLocaleTimeString()
        const logEntry = `[${timestamp}] 🚀 SUBMIT: Event emitted with blob (${blob.size} bytes) - Download started`
        actionLog.value.unshift(logEntry)
        console.log('✅ Final submission:', blob)
        
        // Auto-download to prove the blob is real
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `submitted-crop-${submitCount.value}.png`
        a.style.display = 'none'
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      }

      const handleError = (error: { message: string; [key: string]: unknown }) => {
        const timestamp = new Date().toLocaleTimeString()
        const logEntry = `[${timestamp}] ❌ ERROR: ${error.message}`
        actionLog.value.unshift(logEntry)
        console.error('❌ Error:', error)
      }

      // Track when preview is clicked (we can't directly track this from the component,
      // but we can infer it by watching for preview images appearing)
      const trackPreviewClick = () => {
        previewCount.value++
        const timestamp = new Date().toLocaleTimeString()
        const logEntry = `[${timestamp}] 👁️ PREVIEW: Visual preview created (NO event emitted)`
        actionLog.value.unshift(logEntry)
      }

      return {
        args,
        handleCropped,
        handleError,
        actionLog,
        previewCount,
        submitCount,
        trackPreviewClick,
      }
    },
    template: `
      <div style="max-width: 700px; padding: 20px;">
        <!-- Status Dashboard -->
        <div style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 20px; margin-bottom: 24px;">
          <h3 style="margin: 0 0 16px 0; color: #0369a1; font-size: 18px;">🔄 Preview vs Submit Tracker</h3>
          
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
            <div style="background: #e0f2fe; padding: 12px; border-radius: 6px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #0277bd;">{{ previewCount }}</div>
              <div style="color: #0277bd; font-size: 14px;">👁️ Previews Created</div>
              <div style="color: #0277bd; font-size: 12px; margin-top: 4px;">(No events emitted)</div>
            </div>
            
            <div style="background: #e8f5e8; padding: 12px; border-radius: 6px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #2e7d32;">{{ submitCount }}</div>
              <div style="color: #2e7d32; font-size: 14px;">🚀 Actual Submissions</div>
              <div style="color: #2e7d32; font-size: 12px; margin-top: 4px;">(Events emitted + downloads)</div>
            </div>
          </div>
          
          <p style="margin: 0; color: #0369a1; line-height: 1.5; font-size: 14px;">
            Notice how Preview creates visual feedback but Submit triggers actual events and downloads.
            Click Preview multiple times, then Submit once to see the difference.
          </p>
        </div>
        
        <!-- Manual Preview Tracker Button (for demonstration) -->
        <div style="background: #fffbeb; border: 1px solid #f59e0b; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
          <p style="margin: 0 0 12px 0; color: #92400e; font-size: 14px;">
            📝 Click this button each time you click "Preview" in the component below to track preview actions:
          </p>
          <button @click="trackPreviewClick" style="background: #f59e0b; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 14px;">
            📝 I clicked Preview
          </button>
        </div>
        
        <BravoSquareImageCropper
          v-bind="args"
          @cropped="handleCropped"
          @crop-error="handleError"
        />
        
        <!-- Action Log -->
        <div v-if="actionLog.length > 0" style="margin-top: 24px;">
          <h4 style="margin-bottom: 12px; color: #374151;">📋 Action Log:</h4>
          <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; max-height: 250px; overflow-y: auto;">
            <div v-for="(log, index) in actionLog" :key="index" 
                 style="font-family: monospace; font-size: 13px; margin-bottom: 6px; padding: 4px; border-radius: 3px;"
                 :style="log.includes('SUBMIT') ? 'background: #dcfce7; color: #166534;' : log.includes('PREVIEW') ? 'background: #dbeafe; color: #1d4ed8;' : 'background: #fef2f2; color: #dc2626;'">
              {{ log }}
            </div>
          </div>
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: 'Enhanced demo with detailed tracking to prove Preview creates visual feedback only, while Submit emits events and triggers downloads.',
      },
    },
  },
}