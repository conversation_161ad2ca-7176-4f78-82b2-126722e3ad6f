import Checkbox from 'primevue/checkbox';
import { Meta, StoryObj } from '@storybook/vue3';

export default {
    component: Checkbox,
    title: 'components/Inputs/CheckInput',
    parameters: {
        docs: {
            description: {
                component: 'Allows a user to select a value true/false',
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        disabled: { control: 'boolean' },
    },
} as Meta<typeof Checkbox>;

type Story = StoryObj<typeof Checkbox>;

export const Default: Story = {
    args: { name: 'yellow' },
    render: (args) => ({
        components: {
            Checkbox,
        },
        data: () => ({ args, checked: false }),
        template: `
      <div style="display: flex; align-items: center">
        <Checkbox v-bind="args" v-model="checked" :binary="true"/>
        <label :for="args.name" style="margin-left: 6px"> {{args.name}} </label>
      </div>`,
    }),
};
