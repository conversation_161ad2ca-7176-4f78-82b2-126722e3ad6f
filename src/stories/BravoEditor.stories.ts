import type { Meta, StoryObj } from '@storybook/vue3';
import BravoEditor from '../components/BravoEditor.vue';
import { ref } from 'vue';

// Define the component props type
type EditorProps = {
    modelValue?: string;
    readonly?: boolean;
    style?: Record<string, any>;
};

const meta = {
    title: 'Form/Editor',
    component: BravoEditor,
    tags: ['autodocs'],
    argTypes: {
        modelValue: { control: 'text' },
        readonly: { control: 'boolean' },
        style: { control: 'object' },
    },
} as Meta<typeof BravoEditor>;

export default meta;
type Story = StoryObj<EditorProps>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoEditor
    v-model="value"
    :style="{ height: '320px' }"
  />
</template>

<script setup>
import { ref } from 'vue';

const value = ref('<h2>Welcome to PrimeVue Editor</h2><p>Start typing your content here...</p>');
</script>
`,
            },
        },
    },
    render: (args: any) => ({
        components: { BravoEditor },
        setup() {
            const value = ref('<h2>Welcome to PrimeVue Editor</h2><p>Start typing your content here...</p>');
            return { value, args };
        },
        template: `
      <BravoEditor
        v-model="value"
        :style="{ height: '320px' }"
      />
    `,
    }),
};

// Read-only story
export const ReadOnly: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoEditor
    v-model="value"
    :style="{ height: '320px' }"
    readonly
  />
</template>

<script setup>
import { ref } from 'vue';

const value = ref('<h2>Read-only Content</h2><p>This content cannot be edited.</p>');
</script>
`,
            },
        },
    },
    render: (args: any) => ({
        components: { BravoEditor },
        setup() {
            const value = ref('<h2>Read-only Content</h2><p>This content cannot be edited.</p>');
            return { value, args };
        },
        template: `
      <BravoEditor
        v-model="value"
        :style="{ height: '320px' }"
        readonly
      />
    `,
    }),
};

// Custom toolbar story
export const CustomToolbar: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoEditor
    v-model="value"
    :style="{ height: '320px' }"
  >
    <template v-slot:toolbar>
      <span class="ql-formats">
        <select class="ql-header">
          <option value="1">Heading</option>
          <option value="2">Subheading</option>
          <option selected>Normal</option>
        </select>
        <span class="ql-formats">
          <button class="ql-bold" aria-label="Bold"></button>
          <button class="ql-italic" aria-label="Italic"></button>
          <button class="ql-underline" aria-label="Underline"></button>
        </span>
        <span class="ql-formats">
          <button class="ql-list" value="ordered" aria-label="Ordered List"></button>
          <button class="ql-list" value="bullet" aria-label="Unordered List"></button>
        </span>
        <span class="ql-formats">
          <button class="ql-link" aria-label="Insert Link"></button>
          <button class="ql-image" aria-label="Insert Image"></button>
        </span>
      </span>
    </template>
  </BravoEditor>
</template>

<script setup>
import { ref } from 'vue';

const value = ref('<h2>Custom Toolbar Example</h2><p>This editor has a simplified toolbar.</p>');
</script>
`,
            },
        },
    },
    render: (args: any) => ({
        components: { BravoEditor },
        setup() {
            const value = ref('<h2>Custom Toolbar Example</h2><p>This editor has a simplified toolbar.</p>');
            return { value, args };
        },
        template: `
      <BravoEditor
        v-model="value"
        :style="{ height: '320px' }"
      >
        <template v-slot:toolbar>
          <span class="ql-formats">
            <select class="ql-header">
              <option value="1">Heading</option>
              <option value="2">Subheading</option>
              <option selected>Normal</option>
            </select>
            <span class="ql-formats">
              <button class="ql-bold" aria-label="Bold"></button>
              <button class="ql-italic" aria-label="Italic"></button>
              <button class="ql-underline" aria-label="Underline"></button>
            </span>
            <span class="ql-formats">
              <button class="ql-list" value="ordered" aria-label="Ordered List"></button>
              <button class="ql-list" value="bullet" aria-label="Unordered List"></button>
            </span>
            <span class="ql-formats">
              <button class="ql-link" aria-label="Insert Link"></button>
              <button class="ql-image" aria-label="Insert Image"></button>
            </span>
          </span>
        </template>
      </BravoEditor>
    `,
    }),
};
