import { ref } from 'vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import BravoToast from '../components/BravoToast.vue';
import BravoButton from '../components/BravoButton.vue';
import ToastService from 'primevue/toastservice';
import { useToast } from 'primevue/usetoast';
import { getCurrentInstance } from 'vue';
import type { ToastServiceMethods } from 'primevue/toastservice';
import Toast from 'primevue/toast';

// Define the props interface for Toast component
interface BravoToastProps {
    position?: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right' | 'center';
    group?: string;
    breakpoints?: {
        [key: string]: string;
    };
}

const meta = {
    title: 'Messages/Toast',
    component: BravoToast,
    tags: ['autodocs'],
    argTypes: {
        group: { control: 'text' },
        breakpoints: { control: 'object' },
    },
    // Add the ToastService plugin
    decorators: [
        (story) => ({
            components: { story },
            setup() {
                const app = getCurrentInstance()?.appContext.app;
                if (app && !app._context.config.globalProperties.$toast) {
                    app.use(ToastService);
                }
                return {};
            },
            template: '<story />',
        }),
    ],
} satisfies Meta<typeof BravoToast>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    args: {},
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoToast />
  <BravoButton label="Show" @click="show" />
</template>

<script setup>
import { useToast } from 'primevue/usetoast';

const toast = useToast();
const show = () => {
  toast.add({ severity: 'info', summary: 'Info', detail: 'Message Content', life: 3000 });
};
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoToast, BravoButton },
        setup() {
            const toast = useToast();
            const show = () => {
                toast.add({ severity: 'info', summary: 'Info', detail: 'Message Content', life: 3000 });
            };
            return { show };
        },
        template: `
      <div>
        <BravoToast v-bind="args" />
        <BravoButton label="Show" @click="show" />
      </div>
    `,
    }),
};

// Severity examples
export const Severities: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoToast />
  <div class="flex gap-2">
    <BravoButton label="Success" severity="success" @click="showSuccess" />
    <BravoButton label="Info" severity="info" @click="showInfo" />
    <BravoButton label="Warn" severity="warn" @click="showWarn" />
    <BravoButton label="Error" severity="danger" @click="showError" />
  </div>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoToast, BravoButton },
        setup() {
            const toast = useToast();

            const showSuccess = () => {
                toast.add({ severity: 'success', summary: 'Success', detail: 'Success Message', life: 3000 });
            };

            const showInfo = () => {
                toast.add({ severity: 'info', summary: 'Info', detail: 'Info Message', life: 3000 });
            };

            const showWarn = () => {
                toast.add({ severity: 'warn', summary: 'Warning', detail: 'Warning Message', life: 3000 });
            };

            const showError = () => {
                toast.add({ severity: 'error', summary: 'Error', detail: 'Error Message', life: 3000 });
            };

            return { showSuccess, showInfo, showWarn, showError };
        },
        template: `
      <div>
        <BravoToast v-bind="args" />
        <div class="flex gap-2">
          <BravoButton label="Success" severity="success" @click="showSuccess" />
          <BravoButton label="Info" severity="info" @click="showInfo" />
          <BravoButton label="Warn" severity="warn" @click="showWarn" />
          <BravoButton label="Error" severity="danger" @click="showError" />
        </div>
      </div>
    `,
    }),
};

// Position examples
export const Positions: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoToast position="top-left" group="tl" />
  <BravoToast position="bottom-left" group="bl" />
  <BravoToast position="bottom-right" group="br" />
  <div class="flex gap-2">
    <BravoButton label="Top Left" @click="showTopLeft" />
    <BravoButton label="Bottom Left" @click="showBottomLeft" />
    <BravoButton label="Bottom Right" @click="showBottomRight" />
  </div>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoToast, BravoButton },
        setup() {
            const toast = useToast();

            const showTopLeft = () => {
                toast.add({
                    severity: 'info',
                    summary: 'Top Left',
                    detail: 'Message Content',
                    group: 'tl',
                    life: 3000,
                });
            };

            const showBottomLeft = () => {
                toast.add({
                    severity: 'info',
                    summary: 'Bottom Left',
                    detail: 'Message Content',
                    group: 'bl',
                    life: 3000,
                });
            };

            const showBottomRight = () => {
                toast.add({
                    severity: 'info',
                    summary: 'Bottom Right',
                    detail: 'Message Content',
                    group: 'br',
                    life: 3000,
                });
            };

            return { showTopLeft, showBottomLeft, showBottomRight };
        },
        template: `
      <div>
        <BravoToast position="top-left" group="tl" />
        <BravoToast position="bottom-left" group="bl" />
        <BravoToast position="bottom-right" group="br" />
        <div class="flex gap-2">
          <BravoButton label="Top Left" @click="showTopLeft" />
          <BravoButton label="Bottom Left" @click="showBottomLeft" />
          <BravoButton label="Bottom Right" @click="showBottomRight" />
        </div>
      </div>
    `,
    }),
};

// Sticky example
export const Sticky: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoToast />
  <div class="flex gap-2">
    <BravoButton label="Sticky" @click="showSticky" />
    <BravoButton label="Clear" severity="secondary" @click="clear" />
  </div>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoToast, BravoButton },
        setup() {
            const toast = useToast();

            const showSticky = () => {
                toast.add({ severity: 'info', summary: 'Sticky', detail: 'This is a sticky message', sticky: true });
            };

            const clear = () => {
                toast.removeAllGroups();
            };

            return { showSticky, clear };
        },
        template: `
      <div>
        <BravoToast v-bind="args" />
        <div class="flex gap-2">
          <BravoButton label="Sticky" @click="showSticky" />
          <BravoButton label="Clear" severity="secondary" @click="clear" />
        </div>
      </div>
    `,
    }),
};
