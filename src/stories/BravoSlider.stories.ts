import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import BravoSlider from '../components/BravoSlider.vue';
import { ref } from 'vue';

const meta = {
    title: 'Form/Slider',
    component: BravoSlider,
    tags: ['autodocs'],
    argTypes: {
        modelValue: { control: 'number' },
        min: { control: 'number' },
        max: { control: 'number' },
        step: { control: 'number' },
        range: { control: 'boolean' },
    },
} satisfies Meta<typeof BravoSlider>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoSlider v-model="value" />
</template>

<script setup>
import { ref } from 'vue';

const value = ref(50);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoSlider },
        setup() {
            const value = ref(50);
            return { value };
        },
        template: `
      <BravoSlider
        v-model="value"
      />
    `,
    }),
};

export const Range: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoSlider
    v-model="rangeValues"
    range
    :min="0"
    :max="100"
    :step="10"
  />
</template>

<script setup>
import { ref } from 'vue';

const rangeValues = ref([20, 80]);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoSlider },
        setup() {
            const rangeValues = ref([20, 80]);
            return { rangeValues };
        },
        template: `
      <BravoSlider
        v-model="rangeValues"
        range
        :min="0"
        :max="100"
        :step="10"
      />
    `,
    }),
};
