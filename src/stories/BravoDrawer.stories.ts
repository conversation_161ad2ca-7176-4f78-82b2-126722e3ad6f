import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import BravoDrawer from '../components/BravoDrawer.vue';
import BravoButton from '../components/BravoButton.vue';
import { ref } from 'vue';

const meta = {
    title: 'Overlay/Drawer',
    component: BravoDrawer,
    tags: ['autodocs'],
    argTypes: {
        visible: { control: 'boolean' },
        position: {
            control: 'select',
            options: ['left', 'right', 'top', 'bottom'],
        },
        modal: { control: 'boolean' },
        blockScroll: { control: 'boolean' },
        baseZIndex: { control: 'number' },
        dismissable: { control: 'boolean' },
        showCloseIcon: { control: 'boolean' },
        closeOnEscape: { control: 'boolean' },
    },
} satisfies Meta<typeof BravoDrawer>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Drawer
export const Basic: Story = {
    render: (args) => ({
        components: { <PERSON>Drawer, <PERSON>Button },
        setup() {
            const visible = ref(false);
            return { visible };
        },
        template: `
      <div class="card flex justify-content-center">
        <BravoButton icon="pi pi-arrow-right" label="Show" @click="visible = true" />
        <BravoDrawer v-model:visible="visible">
          <h3>Basic Drawer</h3>
          <p class="m-0">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
          </p>
        </BravoDrawer>
      </div>
    `,
    }),
};

// Position
export const Position: Story = {
    render: (args) => ({
        components: { BravoDrawer, BravoButton },
        setup() {
            const visible = ref(false);
            const position = ref('left');

            const positions = ['left', 'right', 'top', 'bottom'];

            const showDrawer = (pos: string) => {
                position.value = pos;
                visible.value = true;
            };

            return { visible, position, positions, showDrawer };
        },
        template: `
      <div class="card">
        <div class="flex flex-wrap gap-2">
          <BravoButton v-for="pos in positions" :key="pos"
            @click="showDrawer(pos)"
            :label="pos"
            class="p-button-secondary" />
        </div>
        
        <BravoDrawer v-model:visible="visible" :position="position">
          <h3>{{ position }} Drawer</h3>
          <p class="m-0">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
          </p>
        </BravoDrawer>
      </div>
    `,
    }),
};

// Full Screen
export const FullScreen: Story = {
    render: (args) => ({
        components: { BravoDrawer, BravoButton },
        setup() {
            const visible = ref(false);
            return { visible };
        },
        template: `
      <div class="card flex justify-content-center">
        <BravoButton icon="pi pi-arrow-right" label="Show" @click="visible = true" />
        <BravoDrawer v-model:visible="visible" :style="{ width: '100vw' }" position="full">
          <template #header>
            <h3>Full Screen Drawer</h3>
          </template>
          <p class="m-0">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
          </p>
          <template #footer>
            <BravoButton icon="pi pi-check" label="Save" @click="visible = false" />
          </template>
        </BravoDrawer>
      </div>
    `,
    }),
};
