import BravoButton from '../components/BravoButton.vue';
import BravoBadge from '../components/BravoBadge.vue';
import { Meta, StoryObj } from '@storybook/vue3';
// Storybook Documentation: https://storybook.js.org/docs/vue/writing-stories/introduction

// Default export defines metadata about your component
export default {
    component: BravoButton,
    title: 'Button/Button', // Name without Cxm prefix for display in SB
    parameters: {
        docs: {
            description: {
                component: 'Buttons allow users to trigger an action or event with a click.', // Add description for component
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        // Add controls for specific arguments
        outlined: { control: 'boolean' },
        disabled: { control: 'boolean' },
        text: { control: 'boolean' },
        label: { control: 'text' },
        onClick: { control: { type: 'function' } },
    },
} satisfies Meta<typeof BravoButton>;

type Story = StoryObj<typeof BravoButton>;

export const Primary: Story = { args: { label: 'Save', id: 'btn-submit' } };
export const Secondary: Story = { args: { label: 'Submit', severity: 'secondary', id: 'btn-submit' } };
export const TertiaryPrimaryText: Story = {
    args: { label: 'Delete', severity: 'primary', text: true, id: 'btn-back' },
};
export const TertiarySecondaryText: Story = {
    args: { label: 'Delete', severity: 'secondary', text: true, id: 'btn-back' },
};
export const Info: Story = { args: { label: 'Cancel', severity: 'info', id: 'btn-submit' } };

// Example: Reuse args across multiple stories
export const IconOnlySecondary: Story = { args: { label: '', icon: 'pi pi-user', severity: 'secondary' } };
export const IconOnlyInfo: Story = { args: { label: '', icon: 'pi pi-user', severity: 'info' } };
export const IconOnlyTextPrimary: Story = { args: { label: '', icon: 'pi pi-user', severity: 'primary', text: true } };
export const IconOnlyTextSecondary: Story = {
    args: { label: '', icon: 'pi pi-user', severity: 'secondary', text: true },
};
export const TextAndIcon: Story = { args: { ...IconOnlySecondary.args, label: 'Delete' } };

export const Warning: Story = { args: { label: 'Confirm', severity: 'warn', id: 'btn-submit' } };
export const Danger: Story = { args: { label: 'Confirm', severity: 'danger', id: 'btn-submit' } };
export const Success: Story = { args: { label: 'Confirm', severity: 'success', id: 'btn-submit' } };

export const BadgeExamples: Story = {
    parameters: {
        docs: {
            description: {
                story: 'Buttons can display badge indicators to show counts or status information.',
            },
            source: {
                code: `
<template>
  <div class="badge-examples-container" style="display: flex; gap: 1rem; align-items: center;">
    <div style="position: relative; display: inline-block;">
      <BravoButton label="Emails" severity="primary" />
      <BravoBadge value="8" severity="info" style="position: absolute; top: -10px; right: -10px;" />
    </div>
    <div style="position: relative; display: inline-block;">
      <BravoButton icon="pi pi-envelope" />
      <BravoBadge value="4" severity="danger" style="position: absolute; top: -10px; right: -10px;" />
    </div>
  </div>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoButton, BravoBadge },
        template: `
      <div class="badge-examples-container" style="display: flex; gap: 1rem; align-items: center;">
        <div style="position: relative; display: inline-block;">
          <BravoButton label="Emails" severity="primary" />
          <BravoBadge value="8" severity="info" style="position: absolute; top: -10px; right: -10px;" />
        </div>
        <div style="position: relative; display: inline-block;">
          <BravoButton icon="pi pi-envelope" />
          <BravoBadge value="4" severity="danger" style="position: absolute; top: -10px; right: -10px;" />
        </div>
      </div>
    `,
    }),
};

export const ButtonSizes: Story = {
    parameters: {
        docs: {
            description: {
                story: 'Buttons come in three different sizes: small, medium (default), and large.',
            },
            source: {
                code: `
<template>
  <div class="button-sizes-container" style="display: flex; gap: 1rem; align-items: center;">
    <BravoButton label="Small" severity="primary" size="small" />
    <BravoButton label="Medium" severity="primary" />
    <BravoButton label="Large" severity="primary" size="large" />
  </div>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoButton },
        template: `
      <div class="button-sizes-container" style="display: flex; gap: 1rem; align-items: center;">
        <BravoButton label="Small" severity="primary" size="small" />
        <BravoButton label="Medium" severity="primary" />
        <BravoButton label="Large" severity="primary" size="large" />
      </div>
    `,
    }),
};
