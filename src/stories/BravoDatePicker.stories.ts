import type { Meta, StoryObj } from '@storybook/vue3';
import BravoDatePicker from '../components/BravoDatePicker.vue';
import { ref } from 'vue';

// Define the props interface for DatePicker component
interface BravoDatePickerProps {
    modelValue?: Date | Date[] | null;
    dateFormat?: string;
    placeholder?: string;
    showIcon?: boolean;
    showTime?: boolean;
    showButtonBar?: boolean;
    selectionMode?: 'single' | 'multiple' | 'range';
    inline?: boolean;
    variant?: 'outlined' | 'filled';
}

// Meta information for the component
const meta = {
    title: 'Form/DatePicker',
    component: BravoDatePicker,
    tags: ['autodocs'],
    argTypes: {
        modelValue: { control: 'date' },
        dateFormat: { control: 'text' },
        placeholder: { control: 'text' },
        showIcon: { control: 'boolean' },
        showTime: { control: 'boolean' },
        showButtonBar: { control: 'boolean' },
        selectionMode: {
            control: 'select',
            options: ['single', 'multiple', 'range'],
        },
        inline: { control: 'boolean' },
        variant: {
            control: 'select',
            options: ['outlined', 'filled'],
        },
    },
} satisfies Meta<typeof BravoDatePicker>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <DatePicker v-model="date" placeholder="Select Date" />
</template>

<script setup>
import { ref } from 'vue';
const date = ref(null);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoDatePicker },
        setup() {
            const date = ref<Date | null>(null);
            return { date };
        },
        template: `
      <BravoDatePicker
        v-model="date"
        placeholder="Select Date"
      />
    `,
    }),
};

// Range selection story
export const Range: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoDatePicker
    v-model="dateRange"
    selectionMode="range"
    placeholder="Select Date Range"
    :manualInput="false"
  />
</template>

<script setup>
import { ref } from 'vue';
const dateRange = ref(null);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoDatePicker },
        setup() {
            const dateRange = ref<Date[] | null>(null);
            return { dateRange };
        },
        template: `
      <BravoDatePicker
        v-model="dateRange"
        selectionMode="range"
        placeholder="Select Date Range"
        :manualInput="false"
      />
    `,
    }),
};

// Time picker story
export const DateTime: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoDatePicker
    v-model="dateTime"
    showTime
    placeholder="Select Date and Time"
    showButtonBar
  />
</template>

<script setup>
import { ref } from 'vue';
const dateTime = ref(null);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoDatePicker },
        setup() {
            const dateTime = ref<Date | null>(null);
            return { dateTime };
        },
        template: `
      <BravoDatePicker
        v-model="dateTime"
        showTime
        placeholder="Select Date and Time"
        showButtonBar
      />
    `,
    }),
};

// Inline calendar story
export const Inline: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoDatePicker
    v-model="date"
    inline
    showWeek
  />
</template>

<script setup>
import { ref } from 'vue';
const date = ref(null);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoDatePicker },
        setup() {
            const date = ref<Date | null>(null);
            return { date };
        },
        template: `
      <BravoDatePicker
        v-model="date"
        inline
        showWeek
      />
    `,
    }),
};
