import type { Meta, StoryObj } from '@storybook/vue3';
import BravoPassword from '../components/BravoPassword.vue';
import { ref } from 'vue';

interface BravoPasswordProps {
    placeholder?: string;
    modelValue?: string;
    toggleMask?: boolean;
    feedback?: boolean;
    mediumRegex?: string;
    strongRegex?: string;
    promptLabel?: string;
    weakLabel?: string;
    mediumLabel?: string;
    strongLabel?: string;
}

const meta = {
    title: 'Form/Password',
    component: BravoPassword,
    tags: ['autodocs'],
    argTypes: {
        placeholder: { control: 'text' },
        modelValue: { control: 'text' },
        toggleMask: { control: 'boolean' },
        feedback: { control: 'boolean' },
        promptLabel: { control: 'text' },
        weakLabel: { control: 'text' },
        mediumLabel: { control: 'text' },
        strongLabel: { control: 'text' },
    },
} as Meta<typeof BravoPassword>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoPassword v-model="value" placeholder="Enter your password" :feedback="false" />
</template>

<script setup>
import { ref } from 'vue';

const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoPassword },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoPassword
        v-model="value"
        placeholder="Enter your password"
        :feedback="false"
      />
    `,
    }),
};

// With Password Meter
export const WithPasswordMeter: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoPassword
    v-model="value"
    placeholder="Enter your password"
    :feedback="true"
    toggleMask
  />
</template>

<script setup>
import { ref } from 'vue';

const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoPassword },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoPassword
        v-model="value"
        placeholder="Enter your password"
        :feedback="true"
        toggleMask
      />
    `,
    }),
};
