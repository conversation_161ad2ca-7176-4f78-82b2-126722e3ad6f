import { Meta } from '@storybook/blocks';

<Meta title="overview/Introduction" />

<style>
    {`
  `}
</style>

# Bravo Design System

&nbsp;
Welcome to the UI Component Library. This is a reusable component library that helps our teams build UIs faster.
&nbsp;

## Getting Started & Overview

The Bravo Design System uses Primevue component library along with Primeicons and some custom icons. We use Inter font.

All components are named as 'BravoComponentName' where the component name matches the name in Primevue. Please ensure you always use the Bravo components to ensure consisteny across applications.

This is an older document but leaving it here for reference:

The <b><a href="https://docs.google.com/document/d/1WuG5wIzJ88X7nMtUkvlRsUKXkoadN2qWRxW5neiXRPU/edit#heading=h.xdq3i1bk4ue6">UI Component Library Overview</a></b> document contains the following information:

- Repo Overview
- Adding a Component
- Creating a Story in storybook
- Pipeline Overview (publishing to npm, Chromatic flow)
- Local Development
- Usage (package setup, Component usage)

If you have any questions about the component library, please message us in the design-system channel in Slack.
