import type { Meta, StoryObj } from '@storybook/vue3';
import BravoFileUpload from '../components/BravoFileUpload.vue';
import { ref } from 'vue';
import FileUpload from 'primevue/fileupload';

// Define the props interface for FileUpload component
interface FileUploadProps {
    name?: string;
    url?: string;
    multiple?: boolean;
    accept?: string;
    maxFileSize?: number;
    auto?: boolean;
    customUpload?: boolean;
}

// Meta information for the component
const meta = {
    title: 'File/FileUpload',
    component: BravoFileUpload,
    tags: ['autodocs'],
    argTypes: {
        name: { control: 'text' },
        url: { control: 'text' },
        multiple: { control: 'boolean' },
        accept: { control: 'text' },
        maxFileSize: { control: 'number' },
        auto: { control: 'boolean' },
        customUpload: { control: 'boolean' },
    },
} as Meta<typeof BravoFileUpload>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFileUpload
    name="demo[]"
    url="/api/upload"
    @upload="onUpload"
    :multiple="true"
    accept="image/*"
    :maxFileSize="1000000"
  >
    <template #empty>
      <p>Drag and drop files here to upload.</p>
    </template>
  </BravoFileUpload>
</template>

<script setup>
const onUpload = () => {
  // Handle successful upload
  console.log('Upload completed');
};
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoFileUpload },
        setup() {
            const onUpload = () => {
                console.log('Upload completed');
            };

            return { onUpload };
        },
        template: `
      <BravoFileUpload
        name="demo[]"
        url="/api/upload"
        @upload="onUpload"
        :multiple="true"
        accept="image/*"
        :maxFileSize="1000000"
      >
        <template #empty>
          <p>Drag and drop files here to upload.</p>
        </template>
      </BravoFileUpload>
    `,
    }),
};

// Auto upload story
export const AutoUpload: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFileUpload
    name="demo[]"
    url="/api/upload"
    @upload="onUpload"
    :multiple="true"
    accept="image/*"
    :maxFileSize="1000000"
    :auto="true"
  >
    <template #empty>
      <p>Drag and drop files to auto upload.</p>
    </template>
  </BravoFileUpload>
</template>

<script setup>
const onUpload = () => {
  // Handle successful upload
  console.log('Auto upload completed');
};
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoFileUpload },
        setup() {
            const onUpload = () => {
                console.log('Auto upload completed');
            };

            return { onUpload };
        },
        template: `
      <BravoFileUpload
        name="demo[]"
        url="/api/upload"
        @upload="onUpload"
        :multiple="true"
        accept="image/*"
        :maxFileSize="1000000"
        :auto="true"
      >
        <template #empty>
          <p>Drag and drop files to auto upload.</p>
        </template>
      </BravoFileUpload>
    `,
    }),
};

// Custom upload story
export const CustomUpload: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFileUpload
    name="demo[]"
    @uploader="customUploader"
    :multiple="true"
    accept="image/*"
    :maxFileSize="1000000"
    :customUpload="true"
  >
    <template #empty>
      <p>Drag and drop files for custom upload handling.</p>
    </template>
  </BravoFileUpload>
</template>

<script setup>
const customUploader = (event) => {
  // Handle custom upload logic
  const formData = new FormData();
  for (let file of event.files) {
    formData.append('file', file);
  }
  // Implement your custom upload logic here
  console.log('Custom upload handler called');
  event.options.clear();
};
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoFileUpload },
        setup() {
            const customUploader = (event: any) => {
                const formData = new FormData();
                for (const file of event.files) {
                    formData.append('file', file);
                }
                // Implement your custom upload logic here
                console.log('Custom upload handler called');
                event.options.clear();
            };

            return { customUploader };
        },
        template: `
      <BravoFileUpload
        name="demo[]"
        @uploader="customUploader"
        :multiple="true"
        accept="image/*"
        :maxFileSize="1000000"
        :customUpload="true"
      >
        <template #empty>
          <p>Drag and drop files for custom upload handling.</p>
        </template>
      </BravoFileUpload>
    `,
    }),
};
