import type { Meta, StoryObj } from '@storybook/vue3';
import BravoRadioButton from '../components/BravoRadioButton.vue';
import { ref } from 'vue';

// Define the props interface for RadioButton component
interface BravoRadioButtonProps {
    modelValue?: any;
    value?: any;
    name?: string;
    disabled?: boolean;
    invalid?: boolean;
    variant?: 'outlined' | 'filled';
    size?: 'small' | 'large';
    inputId?: string;
    'aria-label'?: string;
    'aria-labelledby'?: string;
}

const meta = {
    title: 'Form/RadioButton',
    component: BravoRadioButton,
    tags: ['autodocs'],
    argTypes: {
        modelValue: { control: 'text' },
        value: { control: 'text' },
        name: { control: 'text' },
        disabled: { control: 'boolean' },
        invalid: { control: 'boolean' },
        variant: {
            control: 'select',
            options: ['outlined', 'filled'],
        },
        size: {
            control: 'select',
            options: ['small', 'normal', 'large'],
        },
        inputId: { control: 'text' },
        'aria-label': { control: 'text' },
        'aria-labelledby': { control: 'text' },
    },
} satisfies Meta<typeof BravoRadioButton>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex items-center gap-2">
    <BravoRadioButton v-model="value" inputId="option1" name="option" value="Option 1" />
    <label for="option1">Option 1</label>
  </div>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoRadioButton },
        setup() {
            const value = ref(null);
            const uniqueId = `option1_${Date.now()}`;
            return { value, uniqueId };
        },
        template: `
      <div class="flex items-center gap-2">
        <BravoRadioButton v-model="value" :inputId="uniqueId" name="option" value="Option 1" />
        <label :for="uniqueId">Option 1</label>
      </div>
    `,
    }),
};

// Group example
export const Group: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-wrap gap-4">
    <div class="flex items-center gap-2">
      <BravoRadioButton v-model="selectedValue" inputId="option1" name="group" value="Option 1" />
      <label for="option1">Option 1</label>
    </div>
    <div class="flex items-center gap-2">
      <BravoRadioButton v-model="selectedValue" inputId="option2" name="group" value="Option 2" />
      <label for="option2">Option 2</label>
    </div>
    <div class="flex items-center gap-2">
      <BravoRadioButton v-model="selectedValue" inputId="option3" name="group" value="Option 3" />
      <label for="option3">Option 3</label>
    </div>
  </div>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoRadioButton },
        setup() {
            const selectedValue = ref(null);
            return { selectedValue };
        },
        template: `
      <div class="flex flex-wrap gap-4">
        <div class="flex items-center gap-2">
          <BravoRadioButton v-model="selectedValue" inputId="option1" name="group" value="Option 1" />
          <label for="option1">Option 1</label>
        </div>
        <div class="flex items-center gap-2">
          <BravoRadioButton v-model="selectedValue" inputId="option2" name="group" value="Option 2" />
          <label for="option2">Option 2</label>
        </div>
        <div class="flex items-center gap-2">
          <BravoRadioButton v-model="selectedValue" inputId="option3" name="group" value="Option 3" />
          <label for="option3">Option 3</label>
        </div>
      </div>
    `,
    }),
};

// Sizes example
export const Sizes: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-wrap gap-4">
    <div class="flex items-center gap-2">
      <BravoRadioButton v-model="size" inputId="size_small" name="size" value="Small" size="small" />
      <label for="size_small" class="text-sm">Small</label>
    </div>
    <div class="flex items-center gap-2">
      <BravoRadioButton v-model="size" inputId="size_normal" name="size" value="Normal" />
      <label for="size_normal">Normal</label>
    </div>
    <div class="flex items-center gap-2">
      <BravoRadioButton v-model="size" inputId="size_large" name="size" value="Large" size="large" />
      <label for="size_large" class="text-lg">Large</label>
    </div>
  </div>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoRadioButton },
        setup() {
            const size = ref('Normal');
            return { size };
        },
        template: `
      <div class="flex flex-wrap gap-4">
        <div class="flex items-center gap-2">
          <BravoRadioButton v-model="size" inputId="size_small" name="size" value="Small" size="small" />
          <label for="size_small" class="text-sm">Small</label>
        </div>
        <div class="flex items-center gap-2">
          <BravoRadioButton v-model="size" inputId="size_normal" name="size" value="Normal" />
          <label for="size_normal">Normal</label>
        </div>
        <div class="flex items-center gap-2">
          <BravoRadioButton v-model="size" inputId="size_large" name="size" value="Large" size="large" />
          <label for="size_large" class="text-lg">Large</label>
        </div>
      </div>
    `,
    }),
};

// Invalid state
export const Invalid: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex items-center gap-2">
    <BravoRadioButton v-model="value" inputId="invalid" name="invalid" value="1" :invalid="true" />
    <label for="invalid">Invalid State</label>
  </div>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoRadioButton },
        setup() {
            const value = ref(null);
            return { value };
        },
        template: `
      <div class="flex items-center gap-2">
        <BravoRadioButton v-model="value" inputId="invalid" name="invalid" value="1" :invalid="true" />
        <label for="invalid">Invalid State</label>
      </div>
    `,
    }),
};

// Disabled state
export const Disabled: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-wrap gap-4">
    <div class="flex items-center gap-2">
      <BravoRadioButton v-model="value" inputId="disabled1" name="disabled" value="1" disabled />
      <label for="disabled1">Option 1</label>
    </div>
    <div class="flex items-center gap-2">
      <BravoRadioButton v-model="value" inputId="disabled2" name="disabled" value="2" disabled />
      <label for="disabled2">Option 2</label>
    </div>
  </div>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoRadioButton },
        setup() {
            const value = ref(null);
            return { value };
        },
        template: `
      <div class="flex flex-wrap gap-4">
        <div class="flex items-center gap-2">
          <BravoRadioButton v-model="value" inputId="disabled1" name="disabled" value="1" disabled />
          <label for="disabled1">Option 1</label>
        </div>
        <div class="flex items-center gap-2">
          <BravoRadioButton v-model="value" inputId="disabled2" name="disabled" value="2" disabled />
          <label for="disabled2">Option 2</label>
        </div>
      </div>
    `,
    }),
};
