import type { Meta, StoryObj } from '@storybook/vue3';
import BravoAvatar from '../components/BravoAvatar.vue';
import AvatarGroup from 'primevue/avatargroup';
import Badge from 'primevue/badge';
import Avatar from 'primevue/avatar';

const meta = {
    title: 'Misc/Avatar',
    component: BravoAvatar,
    tags: ['autodocs'],
    argTypes: {
        firstName: { control: 'text' },
        lastName: { control: 'text' },
        image: { control: 'text' },
    },
} satisfies Meta<typeof BravoAvatar>;

export default meta;
type Story = StoryObj<typeof meta>;

// Label Avatar
export const Label: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <Avatar label="P" class="mr-2" size="xlarge" />
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoAvatar },
        template: `
      <div class="flex align-items-center">
        <BravoAvatar firstName="P" class="mr-2" />
        <BravoAvatar firstName="P" lastName="K" class="mr-2" />
      </div>
    `,
    }),
};

// Icon Avatar
export const Icon: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoAvatar class="mr-2" />
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoAvatar },
        template: `
      <div class="flex align-items-center">
        <BravoAvatar class="mr-2" />
      </div>
    `,
    }),
};

// Image Avatar
export const Image: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex align-items-center gap-2">
    <Avatar 
      image="https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png" 
      size="xlarge" 
      shape="circle" 
    />
    <Avatar 
      image="https://primefaces.org/cdn/primevue/images/avatar/asiyajavayant.png" 
      size="large" 
      shape="circle" 
    />
    <Avatar 
      image="https://primefaces.org/cdn/primevue/images/avatar/onyamalimba.png" 
      shape="circle" 
    />
    <Avatar 
      image="https://www.gravatar.com/avatar/05dfd4b41340d09cae045235eb0893c3?d=mp" 
      size="xlarge" 
      class="flex items-center justify-center" 
    />
  </div>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { Avatar },
        template: `
      <div class="flex align-items-center gap-2">
        <Avatar 
          image="https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png" 
          size="xlarge" 
          shape="circle" 
        />
        <Avatar 
          image="https://primefaces.org/cdn/primevue/images/avatar/asiyajavayant.png" 
          size="large" 
          shape="circle" 
        />
        <Avatar 
          image="https://primefaces.org/cdn/primevue/images/avatar/onyamalimba.png" 
          shape="circle" 
        />
        <Avatar 
          image="https://www.gravatar.com/avatar/05dfd4b41340d09cae045235eb0893c3?d=mp" 
          size="xlarge" 
          class="flex items-center justify-center" 
        />
      </div>
    `,
    }),
};

// BravoAvatar with Image
export const BravoImage: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex align-items-center gap-2">
    <BravoAvatar 
      image="https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png" 
    />
    <BravoAvatar 
      image="https://cdn.prod.website-files.com/5d4b12a4c03a64ab754f23bd/67e6c86d7cca6933dfe07dbd_jiralogo.png"
    />
  </div>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoAvatar },
        template: `
      <div class="flex align-items-center gap-2">
        <BravoAvatar 
          image="https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png" 
        />
        <BravoAvatar 
          image="https://cdn.prod.website-files.com/5d4b12a4c03a64ab754f23bd/67e6c86d7cca6933dfe07dbd_jiralogo.png"
        />
      </div>
    `,
    }),
};

// Avatar with Badge
export const WithBadge: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <span class="p-overlay-badge" style="position: relative; display: inline-block;">
    <Avatar icon="pi pi-user" size="large" />
    <Badge value="4" severity="danger" style="position: absolute; top: 0; right: 0; transform: translate(25%, -25%);" />
  </span>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { Avatar, Badge },
        template: `
      <span class="p-overlay-badge" style="position: relative; display: inline-block;">
        <Avatar icon="pi pi-user" size="large" />
        <Badge value="4" severity="danger" style="position: absolute; top: 0; right: 0; transform: translate(25%, -25%);" />
      </span>
    `,
    }),
};

// Avatar Group
export const Group: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <AvatarGroup>
    <Avatar image="https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png" size="small" shape="circle" />
    <Avatar image="https://primefaces.org/cdn/primevue/images/avatar/asiyajavayant.png" size="small" shape="circle" />
    <Avatar image="https://primefaces.org/cdn/primevue/images/avatar/onyamalimba.png" size="small" shape="circle" />
    <Avatar label="+2" shape="circle" size="small" style="background-color: #9c27b0; color: #ffffff" />
  </AvatarGroup>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { Avatar, AvatarGroup },
        template: `
      <AvatarGroup>
        <Avatar image="https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png" size="small" shape="circle" />
        <Avatar image="https://primefaces.org/cdn/primevue/images/avatar/asiyajavayant.png" size="small" shape="circle" />
        <Avatar image="https://primefaces.org/cdn/primevue/images/avatar/onyamalimba.png" size="small" shape="circle" />
        <Avatar label="+2" shape="circle" size="small" style="background-color: #9c27b0; color: #ffffff" />
      </AvatarGroup>
    `,
    }),
};
