import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import BravoFilterMultiselect from '../components/BravoFilterMultiSelect.vue';
import { ref } from 'vue';

// Sample data
const statusOptions = [
    { label: 'Active', value: 'active' },
    { label: 'Inactive', value: 'inactive' },
    { label: 'Pending', value: 'pending' },
    { label: 'Archived', value: 'archived' },
];

const categoryOptions = [
    { label: 'Electronics', value: 'electronics' },
    { label: 'Clothing', value: 'clothing' },
    { label: 'Books', value: 'books' },
    { label: 'Home & Garden', value: 'home' },
    { label: 'Sports', value: 'sports' },
    { label: 'Toys', value: 'toys' },
];

const priorityOptions = [
    { label: 'High', value: 'high' },
    { label: 'Medium', value: 'medium' },
    { label: 'Low', value: 'low' },
];

const groupedOptions = [
    {
        label: 'Status',
        items: statusOptions,
    },
    {
        label: 'Priority',
        items: priorityOptions,
    },
];

const meta = {
    title: 'Filters/BravoFilterMultiselect',
    component: BravoFilterMultiselect,
    tags: ['autodocs'],
    argTypes: {
        label: { control: 'text' },
        placeholder: { control: 'text' },
        filterOptions: { control: 'object' },
        optionLabel: { control: 'text' },
        optionValue: { control: 'text' },
        modelValue: { control: 'object' },
        'update:modelValue': { action: 'update:modelValue' },
        'filter-change': { action: 'filter-change' },
    },
    parameters: {
        docs: {
            description: {
                component:
                    'A customized filter component based on PrimeVue MultiSelect. The component has no border in its static state and displays a light grey background on hover.',
            },
        },
    },
} satisfies Meta<typeof BravoFilterMultiselect>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic filter
export const Basic: Story = {
    args: {
        label: 'Status',
        placeholder: 'Select status',
        filterOptions: statusOptions,
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFilter
    v-model="selectedStatus"
    label="Status"
    placeholder="Select status"
    :filterOptions="statusOptions"
    @filter-change="onFilterChange"
  />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoFilterMultiselect },
        setup() {
            const selectedStatus = ref([]);
            const onFilterChange = (value: unknown) => {
                console.log('Filter changed:', value);
            };
            return { ...args, selectedStatus, onFilterChange };
        },
        template: `
      <div style="padding: 20px; background-color: white;">
        <BravoFilterMultiselect
          v-model="selectedStatus"
          :label="label"
          :placeholder="placeholder"
          :filterOptions="filterOptions"
          @filter-change="onFilterChange"
        />
      </div>
    `,
    }),
};

// Multiple filters
export const MultipleFilters: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div style="display: flex; gap: 1rem;">
    <BravoFilter
      v-model="selectedStatus"
      label="Status"
      placeholder="Select status"
      :filterOptions="statusOptions"
      @filter-change="onFilterChange"
    />
    <BravoFilter
      v-model="selectedCategories"
      label="Categories"
      placeholder="Select categories"
      :filterOptions="categoryOptions"
      @filter-change="onFilterChange"
    />
  </div>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoFilterMultiselect },
        setup() {
            const selectedStatus = ref([]);
            const selectedCategories = ref([]);
            const onFilterChange = (value: unknown) => {
                console.log('Filter changed:', value);
            };
            return { statusOptions, categoryOptions, selectedStatus, selectedCategories, onFilterChange };
        },
        template: `
      <div style="padding: 20px; background-color: white; display: flex; gap: 1rem;">
        <BravoFilterMultiselect
          v-model="selectedStatus"
          label="Status"
          placeholder="Select status"
          :filterOptions="statusOptions"
          @filter-change="onFilterChange"
        />
        <BravoFilterMultiselect
          v-model="selectedCategories"
          label="Categories"
          placeholder="Select categories"
          :filterOptions="categoryOptions"
          @filter-change="onFilterChange"
        />
      </div>
    `,
    }),
};

// With preselected values
export const WithPreselectedValues: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFilter
    v-model="selectedPriorities"
    label="Priority"
    placeholder="Select priority"
    :filterOptions="priorityOptions"
    @filter-change="onFilterChange"
  />
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoFilterMultiselect },
        setup() {
            const selectedPriorities = ref(['high', 'medium']);
            const onFilterChange = (value: unknown) => {
                console.log('Filter changed:', value);
            };
            return { priorityOptions, selectedPriorities, onFilterChange };
        },
        template: `
      <div style="padding: 20px; background-color: white;">
        <BravoFilter
          v-model="selectedPriorities"
          label="Priority"
          placeholder="Select priority"
          :filterOptions="priorityOptions"
          @filter-change="onFilterChange"
        />
      </div>
    `,
    }),
};

// Disabled state
export const DisabledState: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFilter
    v-model="selectedStatus"
    label="Status (Disabled)"
    placeholder="Select status"
    :filterOptions="statusOptions"
    disabled
  />
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoFilterMultiselect },
        setup() {
            const selectedStatus = ref([]);
            return { statusOptions, selectedStatus };
        },
        template: `
      <div style="padding: 20px; background-color: white;">
        <BravoFilterMultiselect
          v-model="selectedStatus"
          label="Status (Disabled)"
          placeholder="Select status"
          :filterOptions="statusOptions"
          disabled
        />
      </div>
    `,
    }),
};

// Loading state
export const LoadingState: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFilter
    v-model="selectedStatus"
    label="Status (Loading)"
    placeholder="Loading options..."
    :filterOptions="[]"
    loading
  />
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoFilterMultiselect },
        setup() {
            const selectedStatus = ref([]);
            return { selectedStatus };
        },
        template: `
      <div style="padding: 20px; background-color: white;">
        <BravoFilterMultiselect
          v-model="selectedStatus"
          label="Status (Loading)"
          placeholder="Loading options..."
          :filterOptions="[]"
          loading
        />
      </div>
    `,
    }),
};

// Custom styling
export const CustomStyling: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFilter
    v-model="selectedStatus"
    label="Status"
    placeholder="Select status"
    :filterOptions="statusOptions"
    class="custom-filter"
    style="width: 300px;"
  />
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoFilterMultiselect },
        setup() {
            const selectedStatus = ref([]);
            return { statusOptions, selectedStatus };
        },
        template: `
      <div style="padding: 20px; background-color: white;">
        <BravoFilterMultiselect
          v-model="selectedStatus"
          label="Status"
          placeholder="Select status"
          :filterOptions="statusOptions"
          class="custom-filter"
          style="width: 300px;"
        />
      </div>
    `,
    }),
};

// Grouped options
export const GroupedOptions: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFilter
    v-model="selectedFilters"
    label="Grouped Filters"
    placeholder="Select filters"
    :filterOptions="groupedOptions"
    optionGroupLabel="label"
    optionGroupChildren="items"
  />
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoFilterMultiselect },
        setup() {
            const selectedFilters = ref([]);
            return { groupedOptions, selectedFilters };
        },
        template: `
      <div style="padding: 20px; background-color: white;">
        <BravoFilterMultiselect
          v-model="selectedFilters"
          label="Grouped Filters"
          placeholder="Select filters"
          :filterOptions="groupedOptions"
          optionGroupLabel="label"
          optionGroupChildren="items"
        />
      </div>
    `,
    }),
};
