import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import BravoBadge from '../components/BravoBadge.vue';

const meta = {
    title: 'Misc/Badge',
    component: BravoBadge,
    tags: ['autodocs'],
    argTypes: {
        value: { control: 'text' },
        severity: {
            control: 'select',
            options: ['success', 'info', 'warn', 'danger'],
        },
        size: {
            control: 'select',
            options: ['small', 'normal', 'large', 'xlarge'],
        },
    },
} satisfies Meta<typeof BravoBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
    args: {
        value: '2',
    },
    render: (args) => ({
        components: { BravoBadge },
        template: `<BravoBadge v-bind="args" />`,
    }),
};

export const Severity: Story = {
    parameters: {
        docs: {
            description: {
                story: 'Different severity levels for badges',
            },
        },
    },
    render: (args) => ({
        components: { BravoBadge },
        template: `
      <div class="flex gap-2">
        <BravoBadge value="2" />
        <BravoBadge value="8" severity="success" />
        <BravoBadge value="4" severity="info" />
        <BravoBadge value="12" severity="warn" />
        <BravoBadge value="3" severity="danger" />
      </div>
    `,
    }),
};

export const Size: Story = {
    render: (args) => ({
        components: { BravoBadge },
        template: `
      <div class="flex gap-2">
        <BravoBadge value="2" size="small" />
        <BravoBadge value="4" />
        <BravoBadge value="6" size="large" />
        <BravoBadge value="8" size="xlarge" />
      </div>
    `,
    }),
};
