import RadioButton from 'primevue/radiobutton';
import { Meta, StoryObj } from '@storybook/vue3';

export default {
    component: RadioButton,
    title: 'Legacy & Custom/Inputs/Radio',
    parameters: {
        docs: {
            description: {
                component: '',
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        disabled: { control: 'boolean' },
    },
} as Meta<typeof RadioButton>;

type Story = StoryObj<typeof RadioButton>;

export const Default: Story = {
    args: {},
    render: (args) => ({
        components: {
            RadioButton,
        },
        data: () => ({
            args,
            value: null,
            options: [
                { name: 'red' },
                { name: 'orange' },
                { name: 'yellow' },
                { name: 'green' },
                { name: 'blue' },
                { name: 'purple' },
            ],
        }),
        template: `
      <div> 
        <div style="margin-bottom: 8px;">
          <label style="color:#6C7075">Label</label>
        </div>
        <div class="" v-for="option in options" :key="option.name" style="margin-bottom: 8px;">
            <RadioButton v-bind="args" v-model="value" :value="option.name"/>
            <label :for="options.name" style="margin-left: 6px;">{{ option.name }}</label>
        </div>
      </div>`,
    }),
};
