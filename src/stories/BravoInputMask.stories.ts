import type { Meta, StoryObj } from '@storybook/vue3';
import BravoInputMask from '../components/BravoInputMask.vue';
import { ref } from 'vue';
import InputMask from 'primevue/inputmask';

// Define the props interface for InputMask component
interface BravoInputMaskProps {
    placeholder?: string;
    mask?: string;
    modelValue?: string;
    slotChar?: string;
    autoClear?: boolean;
    variant?: 'outlined' | 'filled';
    size?: 'small' | 'large';
    invalid?: boolean;
    disabled?: boolean;
}

// Meta information for the component
const meta = {
    title: 'Form/InputMask',
    component: BravoInputMask,
    tags: ['autodocs'],
    argTypes: {
        placeholder: { control: 'text' },
        mask: { control: 'text' },
        modelValue: { control: 'text' },
        slotChar: { control: 'text' },
        autoClear: { control: 'boolean' },
        variant: { control: 'select', options: ['outlined', 'filled'] },
        size: { control: 'select', options: ['small', 'large'] },
        invalid: { control: 'boolean' },
        disabled: { control: 'boolean' },
    },
} satisfies Meta<typeof BravoInputMask>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <InputMask
    v-model="value"
    mask="99-999999"
    placeholder="99-999999"
  />
</template>

<script setup>
import { ref } from 'vue';
const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoInputMask },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoInputMask
        v-model="value"
        mask="99-999999"
        placeholder="99-999999"
      />
    `,
    }),
};

// Phone Number story
export const PhoneNumber: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <InputMask
    v-model="value"
    mask="(*************"
    placeholder="(*************"
  />
</template>

<script setup>
import { ref } from 'vue';
const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoInputMask },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoInputMask
        v-model="value"
        mask="(*************"
        placeholder="(*************"
      />
    `,
    }),
};

// Date story with SlotChar
export const DateWithSlotChar: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <InputMask
    v-model="value"
    mask="99/99/9999"
    placeholder="99/99/9999"
    slotChar="mm/dd/yyyy"
  />
</template>

<script setup>
import { ref } from 'vue';
const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoInputMask },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoInputMask
        v-model="value"
        mask="99/99/9999"
        placeholder="99/99/9999"
        slotChar="mm/dd/yyyy"
      />
    `,
    }),
};

// Optional Mask story
export const OptionalMask: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <InputMask
    v-model="value"
    mask="(*************? x99999"
    placeholder="(*************? x99999"
    :autoClear="false"
  />
</template>

<script setup>
import { ref } from 'vue';
const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoInputMask },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoInputMask
        v-model="value"
        mask="(*************? x99999"
        placeholder="(*************? x99999"
        :autoClear="false"
      />
    `,
    }),
};
