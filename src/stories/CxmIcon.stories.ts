import { CxmIcon } from '../components';
import { Meta, StoryObj } from '@storybook/vue3';

export default {
    component: CxmIcon,
    title: 'icons/Icon List',
    parameters: {
        docs: {
            description: {
                component: `Icons are 24px * 24px by default and may include padding to match designs. 
        Most Icons have transparent backgrounds and can be styled with the CSS color property. 
        Some icons have colors built into paths that are not intended to be overridden.
        All icons can be sized with CSS height and width properties.`,
            },
        },
    },
    tags: ['autodocs'], // Include the auto generated docsPage: https://storybook.js.org/docs/7.0/vue/writing-docs/docs-page
    argTypes: {},
} as Meta<typeof CxmIcon>;

type Story = StoryObj<typeof CxmIcon>;

// export const Ovation: Story = { args: { iconName: 'ovation'}}

export const ArrowCaretDownLarge: Story = {
    args: { name: 'arrow-caret-down-large' },
};
export const ArrowCaretDownSmall: Story = {
    args: { name: 'arrow-caret-down-small' },
};
export const ArrowCaretLeftLarge: Story = {
    args: { name: 'arrow-caret-left-large' },
};
export const ArrowCaretLeftSmall: Story = {
    args: { name: 'arrow-caret-left-small' },
};
export const ArrowCaretRightLarge: Story = {
    args: { name: 'arrow-caret-right-large' },
};
export const ArrowCaretRightSmall: Story = {
    args: { name: 'arrow-caret-right-small' },
};
export const ArrowCaretUpLarge: Story = {
    args: { name: 'arrow-caret-up-large' },
};
export const ArrowCaretUpSmall: Story = {
    args: { name: 'arrow-caret-up-small' },
};
export const ArrowDropDown: Story = { args: { name: 'arrow-drop-down' } };
export const ArrowDropLeft: Story = { args: { name: 'arrow-drop-left' } };
export const ArrowDropRight: Story = { args: { name: 'arrow-drop-right' } };
export const ArrowDropUp: Story = { args: { name: 'arrow-drop-up' } };
export const ArrowPointerDownLarge: Story = {
    args: { name: 'arrow-pointer-down-large' },
};
export const ArrowPointerDownSmall: Story = {
    args: { name: 'arrow-pointer-down-small' },
};
export const ArrowPointerLeftLarge: Story = {
    args: { name: 'arrow-pointer-left-large' },
};
export const ArrowPointerLeftSmall: Story = {
    args: { name: 'arrow-pointer-left-small' },
};
export const ArrowPointerRightLarge: Story = {
    args: { name: 'arrow-pointer-right-large' },
};
export const ArrowPointerRightSmall: Story = {
    args: { name: 'arrow-pointer-right-small' },
};
export const ArrowPointerUpLarge: Story = {
    args: { name: 'arrow-pointer-up-large' },
};
export const ArrowPointerUpSmall: Story = {
    args: { name: 'arrow-pointer-up-small' },
};
export const Calendar: Story = { args: { name: 'calendar' } };
export const Collapse: Story = { args: { name: 'collapse' } };
export const Drag: Story = { args: { name: 'drag' } };
export const Delete: Story = { args: { name: 'delete' } };
export const Edit: Story = { args: { name: 'edit' } };
export const Ellipses: Story = { args: { name: 'ellipses' } };
export const Expand: Story = { args: { name: 'expand' } };
export const Filter: Story = { args: { name: 'filter' } };
export const File: Story = { args: { name: 'file' } };
export const Info: Story = { args: { name: 'info' } };
export const JourneyStatusComplete: Story = {
    args: { name: 'journey-complete' },
};
export const JourneyStatusTodo: Story = { args: { name: 'journey-todo' } };
export const JourneyStatusSkipped: Story = {
    args: { name: 'journey-skipped' },
};
export const JourneyStatusError: Story = { args: { name: 'journey-error' } };
export const JourneyStatusCanceled: Story = {
    args: { name: 'journey-canceled' },
};
export const JourneyStatusPartialComplete: Story = {
    args: { name: 'journey-partial-complete' },
};
export const JourneyStatusPartialIncomplete: Story = {
    args: { name: 'journey-partial-incomplete' },
};
export const Lightning: Story = { args: { name: 'lightning' } };
export const Marker: Story = { args: { name: 'marker' } };
export const OpenNew: Story = { args: { name: 'open-new' } };
export const Phone: Story = { args: { name: 'phone' } };
export const Plus: Story = { args: { name: 'plus' } };
export const QuestionLarge: Story = { args: { name: 'question-large' } };
export const QuestionSmall: Story = { args: { name: 'question-small' } };
export const Resize: Story = { args: { name: 'resize' } };
export const Search: Story = { args: { name: 'search' } };
export const Stage: Story = { args: { name: 'stage' } };
export const Step: Story = { args: { name: 'step' } };
export const Task: Story = { args: { name: 'task' } };
export const TaskList: Story = { args: { name: 'task-list' } };
export const TimesX: Story = { args: { name: 'times-x' } };
export const WarningTriangle: Story = { args: { name: 'warning-triangle' } };
export const WarningCircle: Story = { args: { name: 'warning-circle' } };
export const NumberBubble: Story = { args: { name: 'number-bubble' } };
export const StepCompletedBubble: Story = {
    args: { name: 'step-completed' },
};
export const Reload: Story = {
    args: { name: 'reload' },
};
