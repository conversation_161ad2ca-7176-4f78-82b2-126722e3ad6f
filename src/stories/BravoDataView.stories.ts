import type { Meta, StoryObj } from '@storybook/vue3';
import BravoDataView from '../components/BravoDataView.vue';
import BravoButton from '../components/BravoButton.vue';
import BravoTag from '../components/BravoTag.vue';
import BravoSelectButton from '../components/BravoSelectButton.vue';
import Dropdown from 'primevue/dropdown';
import { ref } from 'vue';

const meta = {
    title: 'Data/DataView',
    component: BravoDataView,
    tags: ['autodocs'],
    argTypes: {
        value: { control: 'object' },
        paginator: { control: 'boolean' },
        rows: { control: 'number' },
        sortField: { control: 'text' },
        sortOrder: { control: 'number' },
        layout: { control: 'radio', options: ['list', 'grid'] },
        dataKey: { control: 'text' },
    },
} satisfies Meta<typeof BravoDataView>;

export default meta;
type Story = StoryObj<typeof meta>;

const products = [
    {
        id: '1000',
        code: 'f230fh0g3',
        name: 'Bamboo Watch',
        description: 'Product Description',
        image: 'bamboo-watch.jpg',
        price: 65,
        category: 'Accessories',
        quantity: 24,
        inventoryStatus: 'INSTOCK',
        rating: 5,
    },
    {
        id: '1001',
        code: 'nvklal433',
        name: 'Black Watch',
        description: 'Product Description',
        image: 'black-watch.jpg',
        price: 72,
        category: 'Accessories',
        quantity: 61,
        inventoryStatus: 'OUTOFSTOCK',
        rating: 4,
    },
    {
        id: '1002',
        code: 'zz21cz3c1',
        name: 'Blue Band',
        description: 'Product Description',
        image: 'blue-band.jpg',
        price: 79,
        category: 'Fitness',
        quantity: 2,
        inventoryStatus: 'LOWSTOCK',
        rating: 3,
    },
    {
        id: '1003',
        code: '244wgerg2',
        name: 'Blue T-Shirt',
        description: 'Product Description',
        image: 'blue-t-shirt.jpg',
        price: 29,
        category: 'Clothing',
        quantity: 25,
        inventoryStatus: 'INSTOCK',
        rating: 5,
    },
];

const getSeverity = (product: any) => {
    switch (product.inventoryStatus) {
        case 'INSTOCK':
            return 'success';
        case 'LOWSTOCK':
            return 'warning';
        case 'OUTOFSTOCK':
            return 'danger';
        default:
            return null;
    }
};

// Basic story
export const Basic: Story = {
    args: {
        value: products,
        layout: 'list',
        dataKey: 'id',
    },
    render: (args) => ({
        components: { BravoDataView, BravoButton, BravoTag },
        setup() {
            return { args, getSeverity };
        },
        template: `
      <BravoDataView v-bind="args">
        <template #list="slotProps">
          <div class="flex flex-col">
            <div v-for="(item, index) in slotProps.items" :key="item.id">
              <div class="flex flex-col sm:flex-row sm:items-center p-6 gap-4" :class="{ 'border-t border-surface-200 dark:border-surface-700': index !== 0 }">
                <div class="md:w-40 relative">
                  <img class="block xl:block mx-auto rounded w-full" :src="\`https://primefaces.org/cdn/primevue/images/product/\${item.image}\`" :alt="item.name" />
                  <div class="absolute bg-black/70 rounded-border" style="left: 4px; top: 4px">
                    <BravoTag :value="item.inventoryStatus" :severity="getSeverity(item)"></BravoTag>
                  </div>
                </div>
                <div class="flex flex-col md:flex-row justify-between md:items-center flex-1 gap-6">
                  <div class="flex flex-row md:flex-col justify-between items-start gap-2">
                    <div>
                      <span class="font-medium text-surface-500 dark:text-surface-400 text-sm">{{ item.category }}</span>
                      <div class="text-lg font-medium mt-2">{{ item.name }}</div>
                    </div>
                    <div class="bg-surface-100 p-1" style="border-radius: 30px">
                      <div class="bg-surface-0 flex items-center gap-2 justify-center py-1 px-2" style="border-radius: 30px; box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.04), 0px 1px 2px 0px rgba(0, 0, 0, 0.06)">
                        <span class="text-surface-900 font-medium text-sm">{{ item.rating }}</span>
                        <i class="pi pi-star-fill text-yellow-500"></i>
                      </div>
                    </div>
                  </div>
                  <div class="flex flex-col md:items-end gap-8">
                    <span class="text-xl font-semibold">\${{ item.price }}</span>
                    <div class="flex flex-row-reverse md:flex-row gap-2">
                      <BravoButton icon="pi pi-heart" outlined></BravoButton>
                      <BravoButton icon="pi pi-shopping-cart" label="Buy Now" :disabled="item.inventoryStatus === 'OUTOFSTOCK'" class="flex-auto md:flex-initial whitespace-nowrap"></BravoButton>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        
        <template #grid="slotProps">
          <div class="grid">
            <div v-for="item in slotProps.items" :key="item.id" class="col-12 sm:col-6 lg:col-4 xl:col-3 p-2">
              <div class="p-4 border border-surface-200 dark:border-surface-700 bg-surface-0 dark:bg-surface-900 rounded h-full">
                <div class="bg-surface-50 flex justify-center rounded p-4 mb-3">
                  <div class="relative mx-auto">
                    <img class="rounded w-full" :src="\`https://primefaces.org/cdn/primevue/images/product/\${item.image}\`" :alt="item.name" style="max-width: 200px"/>
                    <div class="absolute bg-black/70 rounded-border" style="left: 4px; top: 4px">
                      <BravoTag :value="item.inventoryStatus" :severity="getSeverity(item)"></BravoTag>
                    </div>
                  </div>
                </div>
                <div>
                  <div class="flex flex-row justify-between items-start gap-2 mb-3">
                    <div>
                      <span class="font-medium text-surface-500 dark:text-surface-400 text-sm">{{ item.category }}</span>
                      <div class="text-lg font-medium mt-1">{{ item.name }}</div>
                    </div>
                    <div class="bg-surface-100 p-1" style="border-radius: 30px">
                      <div class="bg-surface-0 flex items-center gap-2 justify-center py-1 px-2" style="border-radius: 30px; box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.04), 0px 1px 2px 0px rgba(0, 0, 0, 0.06)">
                        <span class="text-surface-900 font-medium text-sm">{{ item.rating }}</span>
                        <i class="pi pi-star-fill text-yellow-500"></i>
                      </div>
                    </div>
                  </div>
                  <div class="flex flex-col gap-3">
                    <span class="text-2xl font-semibold">\${{ item.price }}</span>
                    <div class="flex gap-2">
                      <BravoButton icon="pi pi-shopping-cart" label="Buy Now" :disabled="item.inventoryStatus === 'OUTOFSTOCK'" class="flex-auto"></BravoButton>
                      <BravoButton icon="pi pi-heart" outlined></BravoButton>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </BravoDataView>
    `,
    }),
};

// Layout Switcher story
export const LayoutSwitcher: Story = {
    args: {
        value: products,
        layout: 'list',
        dataKey: 'id',
    },
    render: (args) => ({
        components: { BravoDataView, BravoButton, BravoTag, BravoSelectButton },
        setup() {
            const layout = ref(args.layout);
            const options = ['list', 'grid'];

            return { args, layout, options, getSeverity };
        },
        template: `
      <div>
        <BravoDataView 
          :value="args.value" 
          :dataKey="args.dataKey" 
          :layout="layout"
          :paginator="args.paginator"
          :rows="args.rows"
          :sortField="args.sortField"
          :sortOrder="args.sortOrder"
        >
          <template #header>
            <div class="flex justify-end">
              <BravoSelectButton v-model="layout" :options="options" :allowEmpty="false">
                <template #option="{ option }">
                  <i :class="[option === 'list' ? 'pi pi-bars' : 'pi pi-table']" />
                </template>
              </BravoSelectButton>
            </div>
          </template>

          <template #list="slotProps">
            <div class="flex flex-col">
              <div v-for="(item, index) in slotProps.items" :key="item.id">
                <div class="flex flex-col sm:flex-row sm:items-center p-6 gap-4" :class="{ 'border-t border-surface-200 dark:border-surface-700': index !== 0 }">
                  <div class="md:w-40 relative">
                    <img class="block xl:block mx-auto rounded w-full" :src="\`https://primefaces.org/cdn/primevue/images/product/\${item.image}\`" :alt="item.name" />
                    <div class="absolute bg-black/70 rounded-border" style="left: 4px; top: 4px">
                      <BravoTag :value="item.inventoryStatus" :severity="getSeverity(item)"></BravoTag>
                    </div>
                  </div>
                  <div class="flex flex-col md:flex-row justify-between md:items-center flex-1 gap-6">
                    <div class="flex flex-row md:flex-col justify-between items-start gap-2">
                      <div>
                        <span class="font-medium text-surface-500 dark:text-surface-400 text-sm">{{ item.category }}</span>
                        <div class="text-lg font-medium mt-2">{{ item.name }}</div>
                      </div>
                      <div class="bg-surface-100 p-1" style="border-radius: 30px">
                        <div class="bg-surface-0 flex items-center gap-2 justify-center py-1 px-2" style="border-radius: 30px; box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.04), 0px 1px 2px 0px rgba(0, 0, 0, 0.06)">
                          <span class="text-surface-900 font-medium text-sm">{{ item.rating }}</span>
                          <i class="pi pi-star-fill text-yellow-500"></i>
                        </div>
                      </div>
                    </div>
                    <div class="flex flex-col md:items-end gap-8">
                      <span class="text-xl font-semibold">\${{ item.price }}</span>
                      <div class="flex flex-row-reverse md:flex-row gap-2">
                        <BravoButton icon="pi pi-heart" outlined></BravoButton>
                        <BravoButton icon="pi pi-shopping-cart" label="Buy Now" :disabled="item.inventoryStatus === 'OUTOFSTOCK'" class="flex-auto md:flex-initial whitespace-nowrap"></BravoButton>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <template #grid="slotProps">
            <div class="grid">
              <div v-for="item in slotProps.items" :key="item.id" class="col-12 sm:col-6 lg:col-4 xl:col-3 p-2">
                <div class="p-4 border border-surface-200 dark:border-surface-700 bg-surface-0 dark:bg-surface-900 rounded h-full">
                  <div class="bg-surface-50 flex justify-center rounded p-4 mb-3">
                    <div class="relative mx-auto">
                      <img class="rounded w-full" :src="\`https://primefaces.org/cdn/primevue/images/product/\${item.image}\`" :alt="item.name" style="max-width: 200px"/>
                      <div class="absolute bg-black/70 rounded-border" style="left: 4px; top: 4px">
                        <BravoTag :value="item.inventoryStatus" :severity="getSeverity(item)"></BravoTag>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div class="flex flex-row justify-between items-start gap-2 mb-3">
                      <div>
                        <span class="font-medium text-surface-500 dark:text-surface-400 text-sm">{{ item.category }}</span>
                        <div class="text-lg font-medium mt-1">{{ item.name }}</div>
                      </div>
                      <div class="bg-surface-100 p-1" style="border-radius: 30px">
                        <div class="bg-surface-0 flex items-center gap-2 justify-center py-1 px-2" style="border-radius: 30px; box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.04), 0px 1px 2px 0px rgba(0, 0, 0, 0.06)">
                          <span class="text-surface-900 font-medium text-sm">{{ item.rating }}</span>
                          <i class="pi pi-star-fill text-yellow-500"></i>
                        </div>
                      </div>
                    </div>
                    <div class="flex flex-col gap-3">
                      <span class="text-2xl font-semibold">\${{ item.price }}</span>
                      <div class="flex gap-2">
                        <BravoButton icon="pi pi-shopping-cart" label="Buy Now" :disabled="item.inventoryStatus === 'OUTOFSTOCK'" class="flex-auto"></BravoButton>
                        <BravoButton icon="pi pi-heart" outlined></BravoButton>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </BravoDataView>
      </div>
    `,
    }),
};

// Pagination example
export const Pagination: Story = {
    args: {
        value: products,
        layout: 'list',
        dataKey: 'id',
        paginator: true,
        rows: 2,
    },
    render: (args) => ({
        components: { BravoDataView, BravoButton, BravoTag },
        setup() {
            return { args, getSeverity };
        },
        template: `
      <BravoDataView v-bind="args">
        <template #list="slotProps">
          <div class="flex flex-col">
            <div v-for="(item, index) in slotProps.items" :key="item.id">
              <div class="flex flex-col sm:flex-row sm:items-center p-6 gap-4" :class="{ 'border-t border-surface-200 dark:border-surface-700': index !== 0 }">
                <div class="md:w-40 relative">
                  <img class="block xl:block mx-auto rounded w-full" :src="\`https://primefaces.org/cdn/primevue/images/product/\${item.image}\`" :alt="item.name" />
                  <div class="absolute bg-black/70 rounded-border" style="left: 4px; top: 4px">
                    <Tag :value="item.inventoryStatus" :severity="getSeverity(item)"></Tag>
                  </div>
                </div>
                <div class="flex flex-col md:flex-row justify-between md:items-center flex-1 gap-6">
                  <div class="flex flex-row md:flex-col justify-between items-start gap-2">
                    <div>
                      <span class="font-medium text-surface-500 dark:text-surface-400 text-sm">{{ item.category }}</span>
                      <div class="text-lg font-medium mt-2">{{ item.name }}</div>
                    </div>
                    <div class="bg-surface-100 p-1" style="border-radius: 30px">
                      <div class="bg-surface-0 flex items-center gap-2 justify-center py-1 px-2" style="border-radius: 30px; box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.04), 0px 1px 2px 0px rgba(0, 0, 0, 0.06)">
                        <span class="text-surface-900 font-medium text-sm">{{ item.rating }}</span>
                        <i class="pi pi-star-fill text-yellow-500"></i>
                      </div>
                    </div>
                  </div>
                  <div class="flex flex-col md:items-end gap-8">
                    <span class="text-xl font-semibold">\${{ item.price }}</span>
                    <div class="flex flex-row-reverse md:flex-row gap-2">
                      <BravoButton icon="pi pi-heart" outlined></BravoButton>
                      <BravoButton icon="pi pi-shopping-cart" label="Buy Now" :disabled="item.inventoryStatus === 'OUTOFSTOCK'" class="flex-auto md:flex-initial whitespace-nowrap"></BravoButton>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        
        <template #grid="slotProps">
          <div class="grid">
            <div v-for="item in slotProps.items" :key="item.id" class="col-12 sm:col-6 lg:col-4 xl:col-3 p-2">
              <div class="p-4 border border-surface-200 dark:border-surface-700 bg-surface-0 dark:bg-surface-900 rounded h-full">
                <div class="bg-surface-50 flex justify-center rounded p-4 mb-3">
                  <div class="relative mx-auto">
                    <img class="rounded w-full" :src="\`https://primefaces.org/cdn/primevue/images/product/\${item.image}\`" :alt="item.name" style="max-width: 200px"/>
                    <div class="absolute bg-black/70 rounded-border" style="left: 4px; top: 4px">
                      <BravoTag :value="item.inventoryStatus" :severity="getSeverity(item)"></BravoTag>
                    </div>
                  </div>
                </div>
                <div>
                  <div class="flex flex-row justify-between items-start gap-2 mb-3">
                    <div>
                      <span class="font-medium text-surface-500 dark:text-surface-400 text-sm">{{ item.category }}</span>
                      <div class="text-lg font-medium mt-1">{{ item.name }}</div>
                    </div>
                    <div class="bg-surface-100 p-1" style="border-radius: 30px">
                      <div class="bg-surface-0 flex items-center gap-2 justify-center py-1 px-2" style="border-radius: 30px; box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.04), 0px 1px 2px 0px rgba(0, 0, 0, 0.06)">
                        <span class="text-surface-900 font-medium text-sm">{{ item.rating }}</span>
                        <i class="pi pi-star-fill text-yellow-500"></i>
                      </div>
                    </div>
                  </div>
                  <div class="flex flex-col gap-3">
                    <span class="text-2xl font-semibold">\${{ item.price }}</span>
                    <div class="flex gap-2">
                      <BravoButton icon="pi pi-shopping-cart" label="Buy Now" :disabled="item.inventoryStatus === 'OUTOFSTOCK'" class="flex-auto"></BravoButton>
                      <BravoButton icon="pi pi-heart" outlined></BravoButton>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </BravoDataView>
    `,
    }),
};

// Sorting example
export const Sorting: Story = {
    args: {
        value: products,
        layout: 'list',
        dataKey: 'id',
    },
    render: (args) => ({
        components: { BravoDataView, BravoButton, BravoTag, Dropdown },
        setup() {
            const sortOptions = [
                { label: 'Price High to Low', value: '!price' },
                { label: 'Price Low to High', value: 'price' },
                { label: 'Name', value: 'name' },
            ];

            const sortKey = ref<string | null>(null);
            const sortOrder = ref<number | null>(null);
            const sortField = ref<string | null>(null);

            const onSortChange = (event: any) => {
                const value = event.value;

                if (value.indexOf('!') === 0) {
                    sortOrder.value = -1;
                    sortField.value = value.substring(1, value.length);
                    sortKey.value = value;
                } else {
                    sortOrder.value = 1;
                    sortField.value = value;
                    sortKey.value = value;
                }
            };

            return { args, getSeverity, sortOptions, sortKey, sortOrder, sortField, onSortChange };
        },
        template: `
      <BravoDataView 
        :value="args.value" 
        :dataKey="args.dataKey" 
        :layout="args.layout"
        :sortField="sortField"
        :sortOrder="sortOrder"
      >
        <template #header>
          <div class="flex justify-content-end">
            <Dropdown v-model="sortKey" :options="sortOptions" optionLabel="label" placeholder="Sort By Price" @change="onSortChange" />
          </div>
        </template>
        
        <template #list="slotProps">
          <div class="flex flex-col">
            <div v-for="(item, index) in slotProps.items" :key="item.id">
              <div class="flex flex-col sm:flex-row sm:items-center p-6 gap-4" :class="{ 'border-t border-surface-200 dark:border-surface-700': index !== 0 }">
                <div class="md:w-40 relative">
                  <img class="block xl:block mx-auto rounded w-full" :src="\`https://primefaces.org/cdn/primevue/images/product/\${item.image}\`" :alt="item.name" />
                  <div class="absolute bg-black/70 rounded-border" style="left: 4px; top: 4px">
                    <BravoTag :value="item.inventoryStatus" :severity="getSeverity(item)"></BravoTag>
                  </div>
                </div>
                <div class="flex flex-col md:flex-row justify-between md:items-center flex-1 gap-6">
                  <div class="flex flex-row md:flex-col justify-between items-start gap-2">
                    <div>
                      <span class="font-medium text-surface-500 dark:text-surface-400 text-sm">{{ item.category }}</span>
                      <div class="text-lg font-medium mt-2">{{ item.name }}</div>
                    </div>
                    <div class="bg-surface-100 p-1" style="border-radius: 30px">
                      <div class="bg-surface-0 flex items-center gap-2 justify-center py-1 px-2" style="border-radius: 30px; box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.04), 0px 1px 2px 0px rgba(0, 0, 0, 0.06)">
                        <span class="text-surface-900 font-medium text-sm">{{ item.rating }}</span>
                        <i class="pi pi-star-fill text-yellow-500"></i>
                      </div>
                    </div>
                  </div>
                  <div class="flex flex-col md:items-end gap-8">
                    <span class="text-xl font-semibold">\${{ item.price }}</span>
                    <div class="flex flex-row-reverse md:flex-row gap-2">
                      <BravoButton icon="pi pi-heart" outlined></BravoButton>
                      <BravoButton icon="pi pi-shopping-cart" label="Buy Now" :disabled="item.inventoryStatus === 'OUTOFSTOCK'" class="flex-auto md:flex-initial whitespace-nowrap"></BravoButton>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        
        <template #grid="slotProps">
          <div class="grid">
            <div v-for="item in slotProps.items" :key="item.id" class="col-12 sm:col-6 lg:col-4 xl:col-3 p-2">
              <div class="p-4 border border-surface-200 dark:border-surface-700 bg-surface-0 dark:bg-surface-900 rounded h-full">
                <div class="bg-surface-50 flex justify-center rounded p-4 mb-3">
                  <div class="relative mx-auto">
                    <img class="rounded w-full" :src="\`https://primefaces.org/cdn/primevue/images/product/\${item.image}\`" :alt="item.name" style="max-width: 200px"/>
                    <div class="absolute bg-black/70 rounded-border" style="left: 4px; top: 4px">
                      <Tag :value="item.inventoryStatus" :severity="getSeverity(item)"></Tag>
                    </div>
                  </div>
                </div>
                <div>
                  <div class="flex flex-row justify-between items-start gap-2 mb-3">
                    <div>
                      <span class="font-medium text-surface-500 dark:text-surface-400 text-sm">{{ item.category }}</span>
                      <div class="text-lg font-medium mt-1">{{ item.name }}</div>
                    </div>
                    <div class="bg-surface-100 p-1" style="border-radius: 30px">
                      <div class="bg-surface-0 flex items-center gap-2 justify-center py-1 px-2" style="border-radius: 30px; box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.04), 0px 1px 2px 0px rgba(0, 0, 0, 0.06)">
                        <span class="text-surface-900 font-medium text-sm">{{ item.rating }}</span>
                        <i class="pi pi-star-fill text-yellow-500"></i>
                      </div>
                    </div>
                  </div>
                  <div class="flex flex-col gap-3">
                    <span class="text-2xl font-semibold">\${{ item.price }}</span>
                    <div class="flex gap-2">
                      <BravoButton icon="pi pi-shopping-cart" label="Buy Now" :disabled="item.inventoryStatus === 'OUTOFSTOCK'" class="flex-auto"></BravoButton>
                      <BravoButton icon="pi pi-heart" outlined></BravoButton>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </BravoDataView>
    `,
    }),
};
