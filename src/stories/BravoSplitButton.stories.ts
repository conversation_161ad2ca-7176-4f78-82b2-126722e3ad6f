import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import BravoSplitButton from '../components/BravoSplitButton.vue';
import { ref } from 'vue';

interface BravoSplitButtonProps {
    label?: string;
    icon?: string;
    model?: any[];
    severity?: 'secondary' | 'success' | 'info' | 'warning' | 'help' | 'danger';
    raised?: boolean;
    rounded?: boolean;
    text?: boolean;
    outlined?: boolean;
    size?: 'small' | 'large';
    disabled?: boolean;
    loading?: boolean;
}

const meta = {
    title: 'Button/SplitButton',
    component: BravoSplitButton,
    tags: ['autodocs'],
    argTypes: {
        label: { control: 'text' },
        icon: { control: 'text' },
        model: { control: 'object' },
        severity: {
            control: 'select',
            options: ['secondary', 'success', 'info', 'warning', 'help', 'danger'],
        },
        raised: { control: 'boolean' },
        rounded: { control: 'boolean' },
        text: { control: 'boolean' },
        outlined: { control: 'boolean' },
        size: {
            control: 'select',
            options: ['small', 'large'],
        },
        disabled: { control: 'boolean' },
        loading: { control: 'boolean' },
    },
} as Meta<typeof BravoSplitButton>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoSplitButton
    label="Save"
    icon="pi pi-save"
    :model="items"
    @click="save"
  />
</template>

<script setup>
import { ref } from 'vue';

const items = ref([
  {
    label: 'Save as Draft',
    icon: 'pi pi-file',
    command: () => {
      console.log('Save as draft');
    }
  },
  {
    label: 'Save Template',
    icon: 'pi pi-star',
    command: () => {
      console.log('Save template');
    }
  }
]);

const save = () => {
  console.log('Save clicked');
};
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoSplitButton },
        setup() {
            const items = ref([
                {
                    label: 'Save as Draft',
                    icon: 'pi pi-file',
                    command: () => {
                        console.log('Save as draft');
                    },
                },
                {
                    label: 'Save Template',
                    icon: 'pi pi-star',
                    command: () => {
                        console.log('Save template');
                    },
                },
            ]);

            const save = () => {
                console.log('Save clicked');
            };

            return { items, save };
        },
        template: `
      <BravoSplitButton
        label="Save"
        icon="pi pi-save"
        :model="items"
        @click="save"
      />
    `,
    }),
};

// Severity variants story
export const Severity: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex gap-2">
    <BravoSplitButton
      label="Primary"
      :model="items"
    />
    <BravoSplitButton
      label="Secondary"
      severity="secondary"
      :model="items"
    />
    <BravoSplitButton
      label="Success"
      severity="success"
      :model="items"
    />
    <BravoSplitButton
      label="Info"
      severity="info"
      :model="items"
    />
    <BravoSplitButton
      label="Warning"
      severity="warning"
      :model="items"
    />
    <BravoSplitButton
      label="Danger"
      severity="danger"
      :model="items"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';

const items = ref([
  {
    label: 'Option 1',
    icon: 'pi pi-refresh',
    command: () => {
      console.log('Option 1');
    }
  },
  {
    label: 'Option 2',
    icon: 'pi pi-cog',
    command: () => {
      console.log('Option 2');
    }
  }
]);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoSplitButton },
        setup() {
            const items = ref([
                {
                    label: 'Option 1',
                    icon: 'pi pi-refresh',
                    command: () => {
                        console.log('Option 1');
                    },
                },
                {
                    label: 'Option 2',
                    icon: 'pi pi-cog',
                    command: () => {
                        console.log('Option 2');
                    },
                },
            ]);

            return { items };
        },
        template: `
      <div class="flex gap-2">
        <BravoSplitButton
          label="Primary"
          :model="items"
        />
        <BravoSplitButton
          label="Secondary"
          severity="secondary"
          :model="items"
        />
        <BravoSplitButton
          label="Success"
          severity="success"
          :model="items"
        />
        <BravoSplitButton
          label="Info"
          severity="info"
          :model="items"
        />
        <BravoSplitButton
          label="Warning"
          severity="warning"
          :model="items"
        />
        <BravoSplitButton
          label="Danger"
          severity="danger"
          :model="items"
        />
      </div>
    `,
    }),
};
