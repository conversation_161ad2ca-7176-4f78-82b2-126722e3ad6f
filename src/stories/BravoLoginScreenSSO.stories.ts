import type { Meta, StoryObj } from '@storybook/vue3';
import BravoLoginScreenSSO from '../components/BravoLoginScreenSSO.vue';
import { ref } from 'vue';

const meta = {
    title: 'Components/BravoLoginScreenSSO',
    component: BravoLoginScreenSSO,
    tags: ['autodocs'],
    parameters: {
        layout: 'fullscreen',
        docs: {
            story: {
                height: '500px',
            },
        },
    },
    argTypes: {
        submitHandler: {
            description: 'Function that handles the login submission',
            table: {
                type: {
                    summary: '(credentials: { email: string; password: string; rememberMe: boolean }) => Promise<void>',
                },
            },
        },
        emailCheckHandler: {
            description: 'Function that checks if the email should use SSO or password authentication',
            table: {
                type: {
                    summary: '(email: string) => Promise<boolean>',
                },
            },
        },
        onSubmit: {
            description: 'Emitted when the form is submitted with login credentials',
            table: {
                type: {
                    summary: '(event: { email: string; password: string; rememberMe: boolean }) => void',
                },
            },
        },
        onSsoRedirect: {
            description: 'Emitted when an email is identified as requiring SSO authentication',
            table: {
                type: {
                    summary: '(event: { email: string }) => void',
                },
            },
        },
        onForgotPassword: {
            description: 'Emitted when the "Forgot your password?" button is clicked',
            table: {
                type: {
                    summary: '() => void',
                },
            },
        },
    },
} satisfies Meta<typeof BravoLoginScreenSSO>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default story with no pre-determined flow
export const Default: Story = {
    args: {},
    render: (args) => ({
        components: { BravoLoginScreenSSO },
        setup() {
            const onSubmit = (credentials: { email: string; password: string; rememberMe: boolean }) => {
                console.log('Login submitted:', credentials);
            };

            const onSsoRedirect = (data: { email: string }) => {
                console.log('SSO redirect for:', data.email);
            };

            const onForgotPassword = () => {
                console.log('Forgot password clicked');
                alert('Forgot password functionality would be implemented here');
            };

            return { args, onSubmit, onSsoRedirect, onForgotPassword };
        },
        template: '<BravoLoginScreenSSO v-bind="args" @submit="onSubmit" @sso-redirect="onSsoRedirect" @forgot-password="onForgotPassword" />',
    }),
};

// Story demonstrating SSO authentication flow
export const SSOAuthentication: Story = {
    args: {},
    render: (args) => ({
        components: { BravoLoginScreenSSO },
        setup() {
            // This handler always returns true to simulate SSO authentication
            const emailCheckHandler = async (email: string) => {
                console.log('Checking email for SSO:', email);
                return true; // Always use SSO for this demo
            };

            const onSubmit = (credentials: { email: string; password: string; rememberMe: boolean }) => {
                console.log('Login submitted:', credentials);
            };

            const onSsoRedirect = (data: { email: string }) => {
                console.log('SSO redirect for:', data.email);
                // In a real app, this would redirect to the SSO provider
                alert(`Redirecting to SSO provider for ${data.email}`);
            };

            const onForgotPassword = () => {
                console.log('Forgot password clicked');
                alert('Forgot password functionality would be implemented here');
            };

            return {
                args,
                emailCheckHandler,
                onSubmit,
                onSsoRedirect,
                onForgotPassword
            };
        },
        template: `
            <div>
                <h3>SSO Authentication Flow</h3>
                <p>Enter any email and click Continue. This demo will always redirect to SSO.</p>
                <BravoLoginScreenSSO
                    v-bind="args"
                    :email-check-handler="emailCheckHandler"
                    @submit="onSubmit"
                    @sso-redirect="onSsoRedirect"
                    @forgot-password="onForgotPassword"
                />
            </div>
        `,
    }),
};

// Story demonstrating password authentication flow
export const PasswordAuthentication: Story = {
    args: {},
    render: (args) => ({
        components: { BravoLoginScreenSSO },
        setup() {
            // This handler always returns false to simulate password authentication
            const emailCheckHandler = async (email: string) => {
                console.log('Checking email for SSO:', email);
                return false; // Always use password authentication for this demo
            };

            const onSubmit = (credentials: { email: string; password: string; rememberMe: boolean }) => {
                console.log('Login submitted:', credentials);
                alert(`Login submitted for ${credentials.email}`);
            };

            const onSsoRedirect = (data: { email: string }) => {
                console.log('SSO redirect for:', data.email);
            };

            const onForgotPassword = () => {
                console.log('Forgot password clicked');
                alert('Forgot password functionality would be implemented here');
            };

            return {
                args,
                emailCheckHandler,
                onSubmit,
                onSsoRedirect,
                onForgotPassword
            };
        },
        template: `
            <div>
                <h3>Password Authentication Flow</h3>
                <p>Enter any email and click Continue. This demo will always show the password field.</p>
                <BravoLoginScreenSSO
                    v-bind="args"
                    :email-check-handler="emailCheckHandler"
                    @submit="onSubmit"
                    @sso-redirect="onSsoRedirect"
                    @forgot-password="onForgotPassword"
                />
            </div>
        `,
    }),
};

// Story demonstrating conditional SSO based on email domain
export const ConditionalAuthentication: Story = {
    args: {},
    render: (args) => ({
        components: { BravoLoginScreenSSO },
        setup() {
            // This handler checks if the email domain should use SSO
            const emailCheckHandler = async (email: string) => {
                console.log('Checking email for SSO:', email);
                // Use SSO for emails ending with @company.com
                return email.endsWith('@company.com');
            };

            const onSubmit = (credentials: { email: string; password: string; rememberMe: boolean }) => {
                console.log('Login submitted:', credentials);
                alert(`Login submitted for ${credentials.email}`);
            };

            const onSsoRedirect = (data: { email: string }) => {
                console.log('SSO redirect for:', data.email);
                alert(`Redirecting to SSO provider for ${data.email}`);
            };

            const onForgotPassword = () => {
                console.log('Forgot password clicked');
                alert('Forgot password functionality would be implemented here');
            };

            return {
                args,
                emailCheckHandler,
                onSubmit,
                onSsoRedirect,
                onForgotPassword
            };
        },
        template: `
            <div>
                <h3>Conditional Authentication Flow</h3>
                <p>Enter an email with @company.com to trigger SSO, or any other email to show the password field.</p>
                <BravoLoginScreenSSO
                    v-bind="args"
                    :email-check-handler="emailCheckHandler"
                    @submit="onSubmit"
                    @sso-redirect="onSsoRedirect"
                    @forgot-password="onForgotPassword"
                />
            </div>
        `,
    }),
};

// Story demonstrating email trimming functionality
export const EmailTrimming: Story = {
    args: {},
    render: (args) => ({
        components: { BravoLoginScreenSSO },
        setup() {
            const trimmedEmail = ref('');

            // This handler shows the trimmed email value
            const emailCheckHandler = async (email: string) => {
                console.log('Email before trimming:', email);
                // Store the trimmed email for display
                trimmedEmail.value = email;
                // Always use password auth for this demo
                return false;
            };

            const onSubmit = (credentials: { email: string; password: string; rememberMe: boolean }) => {
                console.log('Login submitted with trimmed email:', credentials.email);
                trimmedEmail.value = credentials.email;
            };

            const onForgotPassword = () => {
                console.log('Forgot password clicked');
                alert('Forgot password functionality would be implemented here');
            };

            return {
                args,
                emailCheckHandler,
                onSubmit,
                onForgotPassword,
                trimmedEmail
            };
        },
        template: `
            <div>
                <h3>Email Trimming Demonstration</h3>
                <p>Enter an email with leading or trailing spaces and click Continue.</p>
                <div v-if="trimmedEmail" class="result-box">
                    <p><strong>Trimmed Email:</strong> "{{ trimmedEmail }}"</p>
                </div>
                <BravoLoginScreenSSO
                    v-bind="args"
                    :email-check-handler="emailCheckHandler"
                    @submit="onSubmit"
                    @forgot-password="onForgotPassword"
                />
                <style>
                    .result-box {
                        margin-top: 20px;
                        padding: 10px 15px;
                        background-color: #f0f9ff;
                        border: 1px solid #bae6fd;
                        border-radius: 6px;
                    }
                </style>
            </div>
        `,
    }),
};

// Story demonstrating network error handling
export const NetworkErrorHandling: Story = {
    args: {},
    render: (args) => ({
        components: { BravoLoginScreenSSO },
        setup() {
            const hasTriedLogin = ref(false);

            // This handler simulates a network error
            const emailCheckHandler = async (email: string) => {
                console.log('Simulating network error for:', email);
                hasTriedLogin.value = true;

                // Simulate a network error with a slight delay to feel realistic
                await new Promise(resolve => setTimeout(resolve, 800));

                // Throw a TypeError which is common for network failures
                throw new TypeError('Failed to fetch: NetworkError when attempting to fetch resource');

                // This line is never reached but keeps TypeScript happy
                return false;
            };

            const onSubmit = (credentials: { email: string; password: string; rememberMe: boolean }) => {
                console.log('Login submitted:', credentials);
            };

            const onSsoRedirect = (data: { email: string }) => {
                console.log('SSO redirect for:', data.email);
            };

            const onForgotPassword = () => {
                console.log('Forgot password clicked');
                alert('Forgot password functionality would be implemented here');
            };

            return {
                args,
                emailCheckHandler,
                onSubmit,
                onSsoRedirect,
                onForgotPassword,
                hasTriedLogin
            };
        },
        template: `
            <div>
                <h3>Network Error Handling</h3>
                <p>This demo simulates a network error or CORS issue during the email validation process.</p>
                <p>Enter any email and click Continue to see the error handling in action.</p>
                <div v-if="hasTriedLogin" class="info-box">
                    <p><strong>What's happening:</strong> The component has detected a network error and displayed a user-friendly message.</p>
                </div>
                <BravoLoginScreenSSO
                    v-bind="args"
                    :email-check-handler="emailCheckHandler"
                    @submit="onSubmit"
                    @sso-redirect="onSsoRedirect"
                    @forgot-password="onForgotPassword"
                />
                <style>
                    .info-box {
                        margin-top: 20px;
                        margin-bottom: 20px;
                        padding: 10px 15px;
                        background-color: #fff7ed;
                        border: 1px solid #ffedd5;
                        border-radius: 6px;
                        color: #c2410c;
                    }
                </style>
            </div>
        `,
    }),
};