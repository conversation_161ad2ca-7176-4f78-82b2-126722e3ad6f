import type { Meta, StoryObj } from '@storybook/vue3';

import { CxmRelativeDateBox } from '../components/CxmRelativeDateBox';

type BaseProps = {
    modelValue: Date;
    type: string;
};

const defaultBox: BaseProps = {
    modelValue: new Date(Date.now()),
    type: 'date',
};

const meta = {
    title: 'Legacy & Custom/CxmRelativeDateBox',
    component: CxmRelativeDateBox,
    tags: ['autodocs'],
    parameters: {
        layout: 'fullscreen',
    },
    args: defaultBox,
} satisfies Meta<typeof CxmRelativeDateBox>;

export default meta;
type Story = StoryObj<typeof meta>;
/*
 *👇 Render functions are a framework specific feature to allow you control on how the component renders.
 * See https://storybook.js.org/docs/7.0/vue/api/csf
 * to learn how to use render functions.
 */
export const Primary: Story = {
    render: (args) => ({
        components: { CxmRelativeDateBox },
        setup() {
            return { args };
        },
        template: '<CxmRelativeDateBox v-model="args.modelValue" />',
    }),
    args: defaultBox,
};

export const Duration: Story = {
    render: (args) => ({
        components: { CxmRelativeDateBox },
        setup() {
            return { args };
        },
        template: '<CxmRelativeDateBox v-model="args.modelValue" :type="args.type" />',
    }),
    args: { ...defaultBox, type: 'duration', modelValue: 'PT2H' },
};

// export const Default = { args: { defaultBox } }
