import type { Meta, StoryObj } from '@storybook/vue3';
import BravoFieldset from '../components/BravoFieldset.vue';
import Fieldset from 'primevue/fieldset';

const meta = {
    title: 'Panel/Fieldset',
    component: BravoFieldset,
    tags: ['autodocs'],
    argTypes: {
        legend: { control: 'text' },
        toggleable: { control: 'boolean' },
        collapsed: { control: 'boolean' },
        pt: { control: 'object' },
    },
} satisfies Meta<typeof BravoFieldset>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFieldset legend="Basic Fieldset">
    <p class="m-0">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
      Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
    </p>
  </BravoFieldset>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoFieldset },
        template: `
      <BravoFieldset legend="Basic Fieldset" v-bind="args">
        <p class="m-0">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
          Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
        </p>
      </BravoFieldset>
    `,
    }),
};

// Custom Legend story
export const CustomLegend: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <Fieldset toggleable>
    <template #legend>
      <div class="flex align-items-center">
        <span class="pi pi-user mr-2"></span>
        <span>Custom Legend</span>
      </div>
    </template>
    <p class="m-0">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
      Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
    </p>
  </Fieldset>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoFieldset },
        template: `
      <BravoFieldset toggleable v-bind="args">
        <template #legend>
          <div class="flex align-items-center">
            <span class="pi pi-user mr-2"></span>
            <span>Custom Legend</span>
          </div>
        </template>
        <p class="m-0">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
          Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
        </p>
      </BravoFieldset>
    `,
    }),
};
