import type { Meta, StoryObj } from '@storybook/vue3';
import BravoPaginator from '../components/BravoPaginator.vue';
import { ref } from 'vue';

interface BravoPaginatorProps {
    first?: number;
    rows?: number;
    totalRecords?: number;
    pageLinkSize?: number;
    template?: string;
    showFirstLastIcon?: boolean;
}

const meta = {
    title: 'Data/Paginator',
    component: BravoPaginator,
    tags: ['autodocs'],
    argTypes: {
        first: { control: 'number' },
        rows: { control: 'number' },
        totalRecords: { control: 'number' },
        pageLinkSize: { control: 'number' },
        template: { control: 'text' },
        showFirstLastIcon: { control: 'boolean' },
    },
} as Meta<typeof BravoPaginator>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoPaginator
    v-model:first="first"
    v-model:rows="rows"
    :totalRecords="120"
    :rowsPerPageOptions="[10, 20, 30]"
  />
</template>

<script setup>
import { ref } from 'vue';

const first = ref(0);
const rows = ref(10);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoPaginator },
        setup() {
            const first = ref(0);
            const rows = ref(10);

            return { first, rows };
        },
        template: `
      <BravoPaginator
        v-model:first="first"
        v-model:rows="rows"
        :totalRecords="120"
        :rowsPerPageOptions="[10, 20, 30]"
      />
    `,
    }),
};

// Custom template story
export const CustomTemplate: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoPaginator
    v-model:first="first"
    v-model:rows="rows"
    :totalRecords="120"
    template="FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink"
    :showFirstLastIcon="true"
  />
</template>

<script setup>
import { ref } from 'vue';

const first = ref(0);
const rows = ref(10);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoPaginator },
        setup() {
            const first = ref(0);
            const rows = ref(10);

            return { first, rows };
        },
        template: `
      <BravoPaginator
        v-model:first="first"
        v-model:rows="rows"
        :totalRecords="120"
        template="FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink"
        :showFirstLastIcon="true"
      />
    `,
    }),
};
