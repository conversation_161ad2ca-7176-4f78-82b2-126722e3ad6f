import type { Meta, StoryObj } from '@storybook/vue3';
import BravoComment from '../components/BravoComment.vue';
import { action } from '@storybook/addon-actions';

const meta = {
  title: 'Data Display/Comment',
  component: BravoComment,
  tags: ['autodocs'],
  argTypes: {
    commenterName: { control: 'text' },
    commenterAvatar: { control: 'text' },
    commentDate: { control: 'date' },
    articleTitle: { control: 'text' },
    articleUrl: { control: 'text' },
    commentBody: { control: 'text' },
    showArticleURL: { control: 'boolean' },
    showResolveButton: { control: 'boolean' },
    resolve: { action: 'resolved' }
  },
} satisfies Meta<typeof BravoComment>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic comment example
export const Basic: Story = {
  args: {
    commenterName: 'John Doe',
    commenterAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    commentDate: new Date(2023, 5, 15),
    articleTitle: 'Getting Started with Vue 3',
    articleUrl: 'https://example.com/articles/vue3',
    commentBody: 'This article was very helpful for me as a beginner. Thanks for sharing your knowledge!'
  },
};

// Comment without avatar
export const WithoutAvatar: Story = {
  args: {
    commenterName: 'Jane Smith',
    commentDate: new Date(2023, 6, 22),
    articleTitle: 'Advanced Vue Techniques',
    articleUrl: 'https://example.com/articles/advanced-vue',
    commentBody: 'The section on composables was particularly enlightening. I\'ve implemented some of these patterns in my own projects with great success.'
  },
};

// Long comment text
export const LongComment: Story = {
  args: {
    commenterName: 'Robert Johnson',
    commenterAvatar: 'https://randomuser.me/api/portraits/men/75.jpg',
    commentDate: new Date(2023, 7, 5),
    articleTitle: 'State Management in Modern JS Frameworks',
    articleUrl: 'https://example.com/articles/state-management',
    commentBody: 'This article provides an excellent overview of different state management approaches. I particularly appreciated the comparison between Vuex, Pinia, and Redux. The code examples were clear and the explanations were thorough. I\'ve been struggling with deciding which approach to use in my current project, and this article has helped me make an informed decision. Looking forward to more content like this!'
  },
};

// With Resolve Button
export const WithResolveButton: Story = {
  args: {
    commenterName: 'Alex Thompson',
    commenterAvatar: 'https://randomuser.me/api/portraits/men/41.jpg',
    commentDate: new Date(2023, 8, 18),
    articleTitle: 'Handling User Feedback in Applications',
    articleUrl: 'https://example.com/articles/user-feedback',
    commentBody: 'I found a typo in the third paragraph. It says "feeedback" instead of "feedback".',
    showResolveButton: true
  },
  render: (args) => ({
    components: { BravoComment },
    setup() {
      const onResolve = () => {
        action('resolve')('Comment has been resolved');
      };
      
      return { args, onResolve };
    },
    template: `
      <BravoComment 
        v-bind="args"
        @resolve="onResolve" 
      />
    `
  }),
};

// Without Article URL Example
export const WithoutArticleURL: Story = {
  args: {
    commenterName: 'Lisa Rodriguez',
    commenterAvatar: 'https://randomuser.me/api/portraits/women/33.jpg',
    commentDate: new Date(2023, 9, 8),
    commentBody: 'This is a standalone comment without reference to any specific article.',
    articleTitle: 'Hidden Article Title',
    articleUrl: 'https://example.com/hidden-article',
    showArticleURL: false
  },
};

// Multiple Comments Example
export const MultipleComments: Story = {
  args: {
    commenterName: 'Example User',
    commentDate: new Date(),
    articleTitle: 'Example Article',
    articleUrl: 'https://example.com',
    commentBody: 'Example comment text'
  },
  render: () => ({
    components: { BravoComment },
    setup() {
      const onResolve = (commenterName: string) => {
        action('resolve')(`${commenterName}'s comment has been resolved`);
      };
      
      return { onResolve };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 1.5rem; max-width: 300px;">
        <BravoComment
          commenterName="Alice Williams"
          commenterAvatar="https://randomuser.me/api/portraits/women/45.jpg"
          :commentDate="new Date(2023, 8, 10)"
          articleTitle="UI Component Libraries for Vue"
          articleUrl="https://example.com/articles/vue-component-libraries"
          commentBody="Great comparison of different UI libraries. I've been using PrimeVue and it's been fantastic."
        />
        
        <BravoComment
          commenterName="David Chen"
          commenterAvatar="https://randomuser.me/api/portraits/men/22.jpg"
          :commentDate="new Date(2023, 8, 11)"
          articleTitle="UI Component Libraries for Vue"
          articleUrl="https://example.com/articles/vue-component-libraries"
          commentBody="I would also recommend checking out Quasar framework. It has a comprehensive set of components and utilities."
          showResolveButton
          @resolve="onResolve('David Chen')"
        />
        
        <BravoComment
          commenterName="Sarah Johnson"
          :commentDate="new Date(2023, 8, 12)"
          articleTitle="UI Component Libraries for Vue"
          articleUrl="https://example.com/articles/vue-component-libraries"
          commentBody="Has anyone tried Vuetify? I'm curious about its performance with larger applications."
          showResolveButton
          @resolve="onResolve('Sarah Johnson')"
        />
      </div>
    `
  }),
}; 