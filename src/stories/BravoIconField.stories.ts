import type { Meta, StoryObj } from '@storybook/vue3';
import Bravo<PERSON>conField from '../components/BravoIconField.vue';
import InputIcon from 'primevue/inputicon';
import BravoFloatLabel from '../components/BravoFloatLabel.vue';
import BravoInputText from '../components/BravoInputText.vue';
import { ref } from 'vue';

// Define the props interface for IconField component
interface IconFieldProps {
    // IconField doesn't have direct props as it's mainly a wrapper component
}

const meta = {
    title: 'Form/IconField',
    component: BravoIconField,
    tags: ['autodocs'],
    argTypes: {},
} as Meta<typeof BravoIconField>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story with left icon
export const BasicLeftIcon: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoIconField>
    <InputIcon class="pi pi-search" />
    <BravoInputText v-model="value" placeholder="Search" />
  </BravoIconField>
</template>

<script setup>
import { ref } from 'vue';
const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoIconField, InputIcon, BravoInputText },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoIconField>
        <InputIcon class="pi pi-search" />
        <BravoInputText v-model="value" placeholder="Search" />
      </BravoIconField>
    `,
    }),
};

// Story with float label
export const WithFloatLabel: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoFloatLabel>
    <BravoIconField>
      <InputIcon class="pi pi-user" />
      <BravoInputText id="username" v-model="value" />
    </BravoIconField>
    <label for="username">Username</label>
  </BravoFloatLabel>
</template>

<script setup>
import { ref } from 'vue';
const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoIconField, InputIcon, BravoInputText, BravoFloatLabel },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoFloatLabel>
        <BravoIconField>
          <InputIcon class="pi pi-user" />
          <BravoInputText id="username" v-model="value" />
        </BravoIconField>
        <label for="username">Username</label>
      </BravoFloatLabel>
    `,
    }),
};

// Story with different sizes
export const Sizes: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-column gap-3">
    <BravoIconField>
      <InputIcon class="pi pi-search" />
      <BravoInputText v-model="value1" placeholder="Small" size="small" />
    </BravoIconField>

    <BravoIconField>
      <InputIcon class="pi pi-search" />
      <BravoInputText v-model="value2" placeholder="Normal" />
    </BravoIconField>

    <BravoIconField>
      <InputIcon class="pi pi-search" />
      <BravoInputText v-model="value3" placeholder="Large" size="large" />
    </BravoIconField>
  </div>
</template>

<script setup>
import { ref } from 'vue';
const value1 = ref('');
const value2 = ref('');
const value3 = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoIconField, InputIcon, BravoInputText },
        setup() {
            const value1 = ref('');
            const value2 = ref('');
            const value3 = ref('');
            return { value1, value2, value3 };
        },
        template: `
      <div class="flex flex-column gap-3">
        <BravoIconField>
          <InputIcon class="pi pi-search" />
          <BravoInputText v-model="value1" placeholder="Small" size="small" />
        </BravoIconField>

        <BravoIconField>
          <InputIcon class="pi pi-search" />
          <BravoInputText v-model="value2" placeholder="Normal" />
        </BravoIconField>

        <BravoIconField>
          <InputIcon class="pi pi-search" />
          <BravoInputText v-model="value3" placeholder="Large" size="large" />
        </BravoIconField>
      </div>
    `,
    }),
};
