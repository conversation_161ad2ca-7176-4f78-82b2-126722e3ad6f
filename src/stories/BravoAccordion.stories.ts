import type { Meta, StoryObj } from '@storybook/vue3';
import BravoAccordion from '../components/BravoAccordion.vue';
import BravoAccordionPanel from '../components/BravoAccordionPanel.vue';
import BravoAccordionHeader from '../components/BravoAccordionHeader.vue';
import BravoAccordionContent from '../components/BravoAccordionContent.vue';
import BravoAvatar from '../components/BravoAvatar.vue';
import BravoBadge from '../components/BravoBadge.vue';

const meta = {
    title: 'Panel/Accordion',
    component: BravoAccordion,
    tags: ['autodocs'],
    argTypes: {
        multiple: { control: 'boolean' },
        expandIcon: { control: 'text' },
        collapseIcon: { control: 'text' },
        pt: { control: 'object' },
    },
} satisfies Meta<typeof BravoAccordion>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoAccordion :value="0">
    <BravoAccordionPanel value="0">
      <BravoAccordionHeader>Header I</BravoAccordionHeader>
      <BravoAccordionContent>
        <p class="m-0">Lorem ipsum dolor sit amet, consectetur adipiscing elit...</p>
      </BravoAccordionContent>
    </BravoAccordionPanel>
    <BravoAccordionPanel value="1">
      <BravoAccordionHeader>Header II</BravoAccordionHeader>
      <BravoAccordionContent>
        <p class="m-0">Sed ut perspiciatis unde omnis iste natus error sit...</p>
      </BravoAccordionContent>
    </BravoAccordionPanel>
  </BravoAccordion>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoAccordion, BravoAccordionPanel, BravoAccordionHeader, BravoAccordionContent },
        setup() {
            return { args };
        },
        template: `
      <BravoAccordion :value="0">
        <BravoAccordionPanel value="0">
          <BravoAccordionHeader>Header I</BravoAccordionHeader>
          <BravoAccordionContent>
            <p class="m-0">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
          </BravoAccordionContent>
        </BravoAccordionPanel>
        <BravoAccordionPanel value="1">
          <BravoAccordionHeader>Header II</BravoAccordionHeader>
          <BravoAccordionContent>
            <p class="m-0">Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>
          </BravoAccordionContent>
        </BravoAccordionPanel>
      </BravoAccordion>
    `,
    }),
};

// Multiple panels
export const Multiple: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoAccordion :value="['0']" :multiple="true">
    <BravoAccordionPanel value="0">
      <BravoAccordionHeader>Header I</BravoAccordionHeader>
      <BravoAccordionContent>Content I</BravoAccordionContent>
    </BravoAccordionPanel>
    <BravoAccordionPanel value="1">
      <BravoAccordionHeader>Header II</BravoAccordionHeader>
      <BravoAccordionContent>Content II</BravoAccordionContent>
    </BravoAccordionPanel>
  </BravoAccordion>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoAccordion, BravoAccordionPanel, BravoAccordionHeader, BravoAccordionContent },
        template: `
      <BravoAccordion :value="['0']" :multiple="true">
        <BravoAccordionPanel value="0">
          <BravoAccordionHeader>Header I</BravoAccordionHeader>
          <BravoAccordionContent>
            <p class="m-0">Content I</p>
          </BravoAccordionContent>
        </BravoAccordionPanel>
        <BravoAccordionPanel value="1">
          <BravoAccordionHeader>Header II</BravoAccordionHeader>
          <BravoAccordionContent>
            <p class="m-0">Content II</p>
          </BravoAccordionContent>
        </BravoAccordionPanel>
      </BravoAccordion>
    `,
    }),
};

// Custom Template
export const CustomTemplate: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoAccordion :value="0" expandIcon="pi pi-plus" collapseIcon="pi pi-minus">
    <BravoAccordionPanel value="0">
      <BravoAccordionHeader>
        <template #default>
          <div class="flex items-center gap-2">
            <BravoAvatar image="https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png" shape="circle" />
            <span class="font-bold">Amy Elsner</span>
            <BravoBadge value="3" class="ml-auto" />
          </div>
        </template>
      </BravoAccordionHeader>
      <BravoAccordionContent>Content for Amy</BravoAccordionContent>
    </BravoAccordionPanel>
  </BravoAccordion>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: {
            BravoAccordion,
            BravoAccordionPanel,
            BravoAccordionHeader,
            BravoAccordionContent,
            BravoAvatar,
            BravoBadge,
        },
        template: `
      <BravoAccordion :value="0" expandIcon="pi pi-plus" collapseIcon="pi pi-minus">
        <BravoAccordionPanel value="0">
          <BravoAccordionHeader>
            <template #default>
              <div class="flex items-center gap-2">
                <BravoAvatar image="https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png" shape="circle" />
                <span class="font-bold">Amy Elsner</span>
                <BravoBadge value="3" class="ml-auto" />
              </div>
            </template>
          </BravoAccordionHeader>
          <BravoAccordionContent>
            <p class="m-0">Content for Amy</p>
          </BravoAccordionContent>
        </BravoAccordionPanel>
      </BravoAccordion>
    `,
    }),
};

// Disabled Panel
export const DisabledPanel: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoAccordion :value="0">
    <BravoAccordionPanel value="0">
      <BravoAccordionHeader>Enabled Panel</BravoAccordionHeader>
      <BravoAccordionContent>Content I</BravoAccordionContent>
    </BravoAccordionPanel>
    <BravoAccordionPanel value="1" :disabled="true">
      <BravoAccordionHeader>Disabled Panel</BravoAccordionHeader>
      <BravoAccordionContent>Content II</BravoAccordionContent>
    </BravoAccordionPanel>
  </BravoAccordion>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoAccordion, BravoAccordionPanel, BravoAccordionHeader, BravoAccordionContent },
        template: `
      <BravoAccordion :value="0">
        <BravoAccordionPanel value="0">
          <BravoAccordionHeader>Enabled Panel</BravoAccordionHeader>
          <BravoAccordionContent>
            <p class="m-0">Content I</p>
          </BravoAccordionContent>
        </BravoAccordionPanel>
        <BravoAccordionPanel value="1" :disabled="true">
          <BravoAccordionHeader>Disabled Panel</BravoAccordionHeader>
          <BravoAccordionContent>
            <p class="m-0">Content II</p>
          </BravoAccordionContent>
        </BravoAccordionPanel>
      </BravoAccordion>
    `,
    }),
};
