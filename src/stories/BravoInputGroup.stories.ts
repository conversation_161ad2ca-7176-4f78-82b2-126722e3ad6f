import type { Meta, StoryObj } from '@storybook/vue3';
import BravoInputGroup from '../components/BravoInputGroup.vue';
import InputGroupAddon from 'primevue/inputgroupaddon';
import BravoInputText from '../components/BravoInputText.vue';
import BravoInputNumber from '../components/BravoInputNumber.vue';
import Button from 'primevue/button';
import { ref } from 'vue';

const meta = {
    title: 'Form/InputGroup',
    component: BravoInputGroup,
    tags: ['autodocs'],
    argTypes: {
        // The component mainly uses slots, so we don't need many props
        class: { control: 'text' },
    },
} satisfies Meta<typeof BravoInputGroup>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story with icon
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoInputGroup>
    <InputGroupAddon>
      <i class="pi pi-user"></i>
    </InputGroupAddon>
    <BravoInputText v-model="value" placeholder="Username" />
  </BravoInputGroup>
</template>

<script setup>
import { ref } from 'vue';
const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoInputGroup, InputGroupAddon, BravoInputText },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoInputGroup>
        <InputGroupAddon>
          <i class="pi pi-user"></i>
        </InputGroupAddon>
        <BravoInputText v-model="value" placeholder="Username" />
      </BravoInputGroup>
    `,
    }),
};

// Price input with currency symbols
export const WithCurrency: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoInputGroup>
    <InputGroupAddon>$</InputGroupAddon>
    <BravoInputNumber v-model="value" placeholder="Price" />
    <InputGroupAddon>.00</InputGroupAddon>
  </BravoInputGroup>
</template>

<script setup>
import { ref } from 'vue';
const value = ref(null);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoInputGroup, InputGroupAddon, BravoInputNumber },
        setup() {
            const value = ref(null);
            return { value };
        },
        template: `
      <BravoInputGroup>
        <InputGroupAddon>$</InputGroupAddon>
        <BravoInputNumber v-model="value" placeholder="Price" />
        <InputGroupAddon>.00</InputGroupAddon>
      </BravoInputGroup>
    `,
    }),
};

// With buttons
export const WithButtons: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoInputGroup>
    <BravoButton label="Search" />
    <BravoInputText v-model="value" placeholder="Keyword" />
  </BravoInputGroup>
</template>

<script setup>
import { ref } from 'vue';
const value = ref('');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoInputGroup, Button, BravoInputText },
        setup() {
            const value = ref('');
            return { value };
        },
        template: `
      <BravoInputGroup>
        <Button label="Search" />
        <BravoInputText v-model="value" placeholder="Keyword" />
      </BravoInputGroup>
    `,
    }),
};
