import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import BravoBreadcrumb from '../components/BravoBreadcrumb.vue';

const meta = {
    title: 'Menu/Breadcrumb',
    component: BravoBreadcrumb,
    tags: ['autodocs'],
    argTypes: {
        model: { control: 'object' },
        home: { control: 'object' },
        pt: { control: 'object' },
    },
} satisfies Meta<typeof BravoBreadcrumb>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    args: {
        home: { icon: 'pi pi-home', url: '/' },
        model: [
            { label: 'Electronics', url: '/electronics' },
            { label: 'Computer', url: '/electronics/computer' },
            { label: 'Accessories', url: '/electronics/computer/accessories' },
        ],
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoBreadcrumb :home="home" :model="model" />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoBreadcrumb },
        setup() {
            return { args };
        },
        template: `<BravoBreadcrumb v-bind="args" />`,
    }),
};

// With Router Links
export const WithRouter: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoBreadcrumb :home="home" :model="items">
    <template #item="{ item }">
      <router-link :to="item.route">{{ item.label }}</router-link>
    </template>
  </BravoBreadcrumb>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoBreadcrumb },
        setup() {
            const home = { icon: 'pi pi-home', route: '/' };
            const items = [
                { label: 'Components', route: '/components' },
                { label: 'Navigation', route: '/components/navigation' },
                { label: 'Breadcrumb', route: '/components/navigation/breadcrumb' },
            ];
            return { home, items };
        },
        template: `
      <BravoBreadcrumb :home="home" :model="items">
        <template #item="{ item }">
          <a :href="item.route">{{ item.label }}</a>
        </template>
      </BravoBreadcrumb>
    `,
    }),
};
