import type { Meta, StoryObj } from '@storybook/vue3';
import BravoPanelMenuNav from '../components/BravoPanelMenuNav.vue';
import Badge from 'primevue/badge';
import BravoHeadline from '../components/BravoTypography/BravoHeadline.vue';

interface PanelMenuItem {
    label: string;
    icon?: string;
    items?: PanelMenuItem[];
    command?: () => void;
    url?: string;
    expanded?: boolean;
    disabled?: boolean;
    badge?: string | number;
    shortcut?: string;
}

const meta = {
    title: 'Menu/PanelMenuNav',
    component: BravoPanelMenuNav,
    tags: ['autodocs'],
    argTypes: {
        model: { control: 'object' },
        multiple: { control: 'boolean' },
        pt: { control: 'object' },
    },
} satisfies Meta<typeof BravoPanelMenuNav>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    args: {
        multiple: true,
        model: [
            {
                label: 'Cases',
                expanded: true,
                items: [
                    { label: 'My Cases', icon: 'pi pi-fw pi-inbox', badge: '5' },
                    { label: "My Team's Cases", icon: 'pi pi-fw pi-users', badge: '3' },
                    { label: 'All Cases', icon: 'pi pi-fw pi-list', badge: '12' },
                ],
            },
            {
                label: 'Tasks',
                expanded: true,
                items: [
                    { label: 'My Tasks', icon: 'pi pi-inbox', badge: '4' },
                    { label: "My Team's Tasks", icon: 'pi pi-users', badge: '4' },
                    { label: 'All Tasks', icon: 'pi pi-list', badge: '2' },
                ],
            },
        ],
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div style="padding: 16px; width: 300px; background-color: #F1F1F1; border-radius: 8px;">
    <PanelMenu :model="model" multiple>
      <template #item="{ item }">
        <a class="flex items-center justify-between px-4 py-2 cursor-pointer">
          <div class="flex items-center">
            <span :class="[item.icon]"></span>
            <span v-if="!item.items" class="ml-2">{{ item.label }}</span>
            <BravoHeadline v-else class="ml-2 !m-0 !p-0 inline-block">{{ item.label }}</BravoHeadline>
          </div>
          <div class="flex items-center">
            <Badge v-if="item.badge" :value="item.badge" class="custom-badge" />
          </div>
        </a>
      </template>
    </PanelMenu>
  </div>
</template>
`,
            },
        },
        layout: 'padded',
        viewport: {
            defaultViewport: 'desktop',
        },
    },
    render: (args) => ({
        components: { BravoPanelMenuNav, Badge, BravoHeadline },
        setup() {
            return { args };
        },
        template: `
      <div style="padding: 16px; width: 300px; background-color: var(--surface-50); border-radius: 8px;">
        <BravoPanelMenuNav v-bind="args">
          <template #item="{ item }">
            <a class="menu-item flex items-center justify-between px-4 py-2 cursor-pointer">
              <div class="flex items-center">
                <span :class="[item.icon]"></span>
                <span v-if="!item.items" class="ml-2">{{ item.label }}</span>
                <BravoHeadline v-else class="ml-2 !m-0 !p-0 inline-block">{{ item.label }}</BravoHeadline>
              </div>
              <div class="flex items-center">
                <Badge v-if="item.badge" :value="item.badge" class="custom-badge" />
              </div>
            </a>
          </template>
        </BravoPanelMenuNav>
      </div>
    `,
    }),
};
