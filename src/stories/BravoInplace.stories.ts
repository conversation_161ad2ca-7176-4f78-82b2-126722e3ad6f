import type { Meta, StoryObj } from '@storybook/vue3';
import BravoInplace from '../components/BravoInplace.vue';
import Button from 'primevue/button';

const meta = {
    title: 'Misc/Inplace',
    component: BravoInplace,
    tags: ['autodocs'],
    argTypes: {
        active: { control: 'boolean' },
        closable: { control: 'boolean' },
        disabled: { control: 'boolean' },
    },
} satisfies Meta<typeof BravoInplace>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
    render: () => ({
        components: { BravoInplace },
        template: `
      <div class="card">
        <BravoInplace>
          <template #display>
            <div class="inline-flex align-items-center cursor-pointer">
              <span class="pi pi-user mr-2"></span>
              <span class="text-primary">View Content</span>
            </div>
          </template>
          <template #content>
            <div class="surface-100 p-3">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            </div>
          </template>
        </BravoInplace>
      </div>
    `,
    }),
};

export const Closable: Story = {
    render: () => ({
        components: { BravoInplace },
        template: `
      <div class="card">
        <BravoInplace closable>
          <template #display>
            <div class="inline-flex align-items-center cursor-pointer">
              <span class="pi pi-user mr-2"></span>
              <span class="text-primary">View Content</span>
            </div>
          </template>
          <template #content>
            <div class="surface-100 p-3">
              Content can be closed with the close button.
            </div>
          </template>
        </BravoInplace>
      </div>
    `,
    }),
};

export const Image: Story = {
    render: () => ({
        components: { BravoInplace },
        template: `
      <div class="card">
        <BravoInplace>
          <template #display>
            <div class="inline-flex align-items-center cursor-pointer">
              <span class="pi pi-image mr-2"></span>
              <span class="text-primary">Show Image</span>
            </div>
          </template>
          <template #content>
            <img src="https://primefaces.org/cdn/primevue/images/galleria/galleria1.jpg" alt="Nature" />
          </template>
        </BravoInplace>
      </div>
    `,
    }),
};
