import type { Meta, StoryObj } from '@storybook/vue3';
import BravoPickList from '../components/BravoPickList.vue';
import { ref } from 'vue';

interface BravoPickListProps {
    modelValue?: any[];
    dataKey: string;
    listStyle?: object;
    metaKeySelection?: boolean;
    dragdrop?: boolean;
    showSourceControls?: boolean;
    showTargetControls?: boolean;
}

const meta = {
    title: 'Data/PickList',
    component: BravoPickList,
    tags: ['autodocs'],
    argTypes: {
        modelValue: { control: 'object' },
        dataKey: { control: 'text' },
        listStyle: { control: 'object' },
        metaKeySelection: { control: 'boolean' },
        dragdrop: { control: 'boolean' },
        showSourceControls: { control: 'boolean' },
        showTargetControls: { control: 'boolean' },
    },
} as Meta<typeof BravoPickList>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoPickList
    v-model:source="sourceProducts"
    v-model:target="targetProducts"
    dataKey="id"
    :showSourceControls="false"
    :showTargetControls="false"
  >
    <template #item="slotProps">
      <div class="product-item">
        <div class="product-list-detail">
          <h5>{{slotProps.item.name}}</h5>
          <span class="product-category">{{slotProps.item.category}}</span>
        </div>
        <div class="product-list-action">
          <span class="product-price">\${{slotProps.item.price}}</span>
        </div>
      </div>
    </template>
  </BravoPickList>
</template>

<script setup>
import { ref } from 'vue';

const sourceProducts = ref([
  { id: '1', name: 'Product 1', category: 'Electronics', price: 100 },
  { id: '2', name: 'Product 2', category: 'Fashion', price: 80 },
  { id: '3', name: 'Product 3', category: 'Home', price: 90 },
]);

const targetProducts = ref([]);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoPickList },
        setup() {
            const sourceProducts = ref([
                { id: '1', name: 'Product 1', category: 'Electronics', price: 100 },
                { id: '2', name: 'Product 2', category: 'Fashion', price: 80 },
                { id: '3', name: 'Product 3', category: 'Home', price: 90 },
            ]);

            const targetProducts = ref([]);

            return { sourceProducts, targetProducts };
        },
        template: `
      <BravoPickList
        v-model:source="sourceProducts"
        v-model:target="targetProducts"
        dataKey="id"
        :showSourceControls="false"
        :showTargetControls="false"
      >
        <template #item="slotProps">
          <div class="product-item">
            <div class="product-list-detail">
              <h5>{{slotProps.item.name}}</h5>
              <span class="product-category">{{slotProps.item.category}}</span>
            </div>
            <div class="product-list-action">
              <span class="product-price">\${{slotProps.item.price}}</span>
            </div>
          </div>
        </template>
      </BravoPickList>
    `,
    }),
};

// Advanced story with controls and drag-drop
export const Advanced: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoPickList
    v-model:source="sourceProducts"
    v-model:target="targetProducts"
    dataKey="id"
    :dragdrop="true"
    :showSourceControls="true"
    :showTargetControls="true"
    listStyle="height:342px"
  >
    <template #sourceheader>
      Available Products
    </template>
    <template #targetheader>
      Selected Products
    </template>
    <template #item="slotProps">
      <div class="product-item">
        <div class="product-list-detail">
          <h5>{{slotProps.item.name}}</h5>
          <span class="product-category">{{slotProps.item.category}}</span>
        </div>
        <div class="product-list-action">
          <span class="product-price">\${{slotProps.item.price}}</span>
        </div>
      </div>
    </template>
  </BravoPickList>
</template>

<script setup>
import { ref } from 'vue';

const sourceProducts = ref([
  { id: '1', name: 'Product 1', category: 'Electronics', price: 100 },
  { id: '2', name: 'Product 2', category: 'Fashion', price: 80 },
  { id: '3', name: 'Product 3', category: 'Home', price: 90 },
]);

const targetProducts = ref([]);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoPickList },
        setup() {
            const sourceProducts = ref([
                { id: '1', name: 'Product 1', category: 'Electronics', price: 100 },
                { id: '2', name: 'Product 2', category: 'Fashion', price: 80 },
                { id: '3', name: 'Product 3', category: 'Home', price: 90 },
            ]);

            const targetProducts = ref([]);

            return { sourceProducts, targetProducts };
        },
        template: `
      <BravoPickList
        v-model:source="sourceProducts"
        v-model:target="targetProducts"
        dataKey="id"
        :dragdrop="true"
        :showSourceControls="true"
        :showTargetControls="true"
        listStyle="height:342px"
      >
        <template #sourceheader>
          Available Products
        </template>
        <template #targetheader>
          Selected Products
        </template>
        <template #item="slotProps">
          <div class="product-item">
            <div class="product-list-detail">
              <h5>{{slotProps.item.name}}</h5>
              <span class="product-category">{{slotProps.item.category}}</span>
            </div>
            <div class="product-list-action">
              <span class="product-price">\${{slotProps.item.price}}</span>
            </div>
          </div>
        </template>
      </BravoPickList>
    `,
    }),
};
