import type { Meta, StoryObj } from '@storybook/vue3';
import { BravoStyleClass } from '../directives/BravoStyleClass';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';

const meta = {
    title: 'Misc/StyleClass',
    tags: ['autodocs'],
    decorators: [
        (story) => ({
            components: { story },
            directives: { 'bravo-styleclass': BravoStyleClass },
            template: '<story />',
        }),
    ],
} satisfies Meta;

export default meta;
type Story = StoryObj<typeof meta>;

export const ToggleClass: Story = {
    render: () => ({
        components: { Button, InputText },
        template: `
      <div class="card flex flex-column gap-2">
        <div>
          <Button 
            v-bravo-styleclass="{ selector: '@next', toggleClass: 'p-disabled' }" 
            label="Toggle p-disabled" 
          />
          <InputText class="ml-2" placeholder="Input" />
        </div>
      </div>
    `,
    }),
};

export const FadeAnimation: Story = {
    render: () => ({
        components: { Button },
        template: `
      <div class="card">
        <Button 
          v-bravo-styleclass="{ 
            selector: '@next',
            enterFromClass: 'hidden',
            enterActiveClass: 'fade-enter-active',
            enterToClass: 'fade-enter-to',
            leaveActiveClass: 'fade-leave-active',
            leaveToClass: 'hidden'
          }" 
          label="Toggle Fade" 
        />
        <div class="hidden mt-3">
          <div class="surface-card p-3 border-round shadow-2">
            Content with fade animation
          </div>
        </div>

        <style>
          .fade-enter-active,
          .fade-leave-active {
            transition: opacity 0.3s ease;
          }
          .fade-enter-to {
            opacity: 1;
          }
          .fade-enter-from,
          .fade-leave-to {
            opacity: 0;
          }
          .hidden {
            display: none;
          }
        </style>
      </div>
    `,
    }),
};

export const SlideAnimation: Story = {
    render: () => ({
        components: { Button },
        template: `
      <div class="card">
        <Button 
          v-bravo-styleclass="{ 
            selector: '@next',
            enterFromClass: 'hidden',
            enterActiveClass: 'slide-enter-active',
            enterToClass: 'slide-enter-to',
            leaveActiveClass: 'slide-leave-active',
            leaveToClass: 'hidden'
          }" 
          label="Toggle Slide" 
        />
        <div class="hidden mt-3">
          <div class="surface-card p-3 border-round shadow-2">
            Content with slide animation
          </div>
        </div>

        <style>
          .slide-enter-active,
          .slide-leave-active {
            transition: all 0.3s ease-out;
          }
          .slide-enter-to {
            opacity: 1;
            transform: translateY(0);
          }
          .slide-enter-from,
          .slide-leave-to {
            opacity: 0;
            transform: translateY(-20px);
          }
          .hidden {
            display: none;
          }
        </style>
      </div>
    `,
    }),
};
