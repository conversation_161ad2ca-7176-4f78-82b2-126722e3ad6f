import type { Meta, StoryObj } from '@storybook/vue3';
import BravoAutoComplete from '../components/BravoAutoComplete.vue';
import { ref } from 'vue';

// Define the props interface for AutoComplete component
interface BravoAutoCompleteProps {
    placeholder?: string;
    suggestions?: any[];
    modelValue?: string | string[];
    multiple?: boolean;
}

// Meta information for the component
const meta = {
    title: 'Form/AutoComplete',
    component: BravoAutoComplete,
    tags: ['autodocs'],
    argTypes: {
        placeholder: { control: 'text' },
        suggestions: { control: 'object' },
        modelValue: { control: 'text' },
        multiple: { control: 'boolean' },
    },
} as Meta<typeof BravoAutoComplete>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoAutoComplete
    v-model="value"
    :suggestions="items"
    @complete="search"
    placeholder="Type to search fruits..."
  />
</template>

<script setup>
import { ref } from 'vue';

const value = ref('');
const items = ref([]);

const search = (event) => {
  const suggestions = ['Apple', 'Banana', 'Orange', 'Mango', 'Pineapple'];
  items.value = suggestions.filter(item => 
    item.toLowerCase().includes(event.query.toLowerCase())
  );
};
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoAutoComplete },
        setup() {
            const value = ref<string>('');
            const items = ref<string[]>([]);

            const search = (event: { query: string }) => {
                const suggestions = ['Apple', 'Banana', 'Orange', 'Mango', 'Pineapple'];
                items.value = suggestions.filter((item) => item.toLowerCase().includes(event.query.toLowerCase()));
            };

            return { value, items, search };
        },
        template: `
      <BravoAutoComplete
        v-model="value"
        :suggestions="items"
        @complete="search"
        placeholder="Type to search fruits..."
      />
    `,
    }),
};

// Multiple selection story
export const Multiple: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoAutoComplete
    v-model="value"
    :suggestions="items"
    @complete="search"
    placeholder="Select multiple fruits..."
    multiple
  />
</template>

<script setup>
import { ref } from 'vue';

const value = ref([]);
const items = ref([]);

const search = (event) => {
  const suggestions = ['Apple', 'Banana', 'Orange', 'Mango', 'Pineapple'];
  items.value = suggestions.filter(item => 
    item.toLowerCase().includes(event.query.toLowerCase())
  );
};
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoAutoComplete },
        setup() {
            const value = ref<string[]>([]);
            const items = ref<string[]>([]);

            const search = (event: { query: string }) => {
                const suggestions = ['Apple', 'Banana', 'Orange', 'Mango', 'Pineapple'];
                items.value = suggestions.filter((item) => item.toLowerCase().includes(event.query.toLowerCase()));
            };

            return { value, items, search };
        },
        template: `
      <BravoAutoComplete
        v-model="value"
        :suggestions="items"
        @complete="search"
        placeholder="Select multiple fruits..."
        multiple
      />
    `,
    }),
};
