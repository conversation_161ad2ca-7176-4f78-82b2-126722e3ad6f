import { CxmLabel } from '../components/CxmLabel';
import { Meta, StoryObj } from '@storybook/vue3';

type BaseLabelProps = {
    text: string;
    id?: string;
    dataTestId?: string;
    forElement?: string;
    isRequired?: boolean;
    iconName?: string;
    toolTipText?: string;
    toolTipPosition?: string;
    mode?: 'primary' | 'secondary' | 'dark';
    className?: string;
};

const defaultLabelProps: BaseLabelProps = {
    text: 'First Name',
    id: 'cxm-default-label',
    dataTestId: 'cxm-default-label',
    forElement: 'firstName',
    isRequired: false,
    iconName: '',
    toolTipText: '',
    toolTipPosition: '',
};

const requiredLabelProps: BaseLabelProps = {
    text: 'Username',
    id: 'cxm-required-label',
    forElement: 'username',
    isRequired: true,
    iconName: '',
    toolTipText: '',
    toolTipPosition: '',
};

const withInfoIconLabelProps: BaseLabelProps = {
    text: 'Enter your email',
    id: 'cxm-info-icon-label',
    forElement: 'email',
    isRequired: true,
    iconName: 'info',
    toolTipText: 'Tooltip',
    toolTipPosition: 'top',
};

const primaryLabelProps: BaseLabelProps = {
    text: 'Primary Label',
    id: 'primary-label',
    forElement: 'primaryLabel',
    mode: 'primary',
};

const darkLabelProps: BaseLabelProps = {
    text: 'Dark Label',
    id: 'Dark-label',
    forElement: 'darkLabel',
    mode: 'dark',
};

const secondaryLabelProps: BaseLabelProps = {
    text: 'Secondary Label',
    id: 'Secondary-label',
    forElement: 'secondaryLabel',
    mode: 'secondary',
};

export default {
    component: CxmLabel,
    title: 'Legacy & Custom/Label',

    tags: ['autodocs'],

    parameters: {
        docs: {
            description: {
                component: 'Represents a caption for an item in a user interface',
            },
        },
        layout: 'fullscreen',
    },
    argTypes: {
        toolTipPosition: { control: 'select', options: ['top', 'right'] },
        mode: { control: 'select', options: ['primary', 'secondary', 'dark'] },
    },
} as Meta<typeof CxmLabel>;

type Story = StoryObj<typeof CxmLabel>;

export const Default: Story = {
    args: {
        text: defaultLabelProps.text,
        id: defaultLabelProps.id,
        dataTestId: defaultLabelProps.dataTestId,
        forElement: defaultLabelProps.forElement,
        isRequired: defaultLabelProps.isRequired,
        iconName: defaultLabelProps.iconName,
        toolTipText: defaultLabelProps.toolTipText,
        toolTipPosition: defaultLabelProps.toolTipPosition,
    },
};

export const SecondaryLabel: Story = {
    args: {
        text: secondaryLabelProps.text,
        id: secondaryLabelProps.id,
        forElement: secondaryLabelProps.forElement,
        mode: secondaryLabelProps.mode,
    },
};

export const PrimaryLabel: Story = {
    args: {
        text: primaryLabelProps.text,
        id: primaryLabelProps.id,
        forElement: primaryLabelProps.forElement,
        mode: primaryLabelProps.mode,
    },
};

export const DarkLabel: Story = {
    args: {
        text: darkLabelProps.text,
        id: darkLabelProps.id,
        forElement: darkLabelProps.forElement,
        mode: darkLabelProps.mode,
    },
};

export const Required: Story = {
    args: {
        text: requiredLabelProps.text,
        id: requiredLabelProps.id,
        forElement: requiredLabelProps.forElement,
        isRequired: requiredLabelProps.isRequired,
        iconName: requiredLabelProps.iconName,
        toolTipText: requiredLabelProps.toolTipText,
        toolTipPosition: requiredLabelProps.toolTipPosition,
    },
};

export const WithInfoIcon: Story = {
    args: {
        text: withInfoIconLabelProps.text,
        id: withInfoIconLabelProps.id,
        forElement: withInfoIconLabelProps.forElement,
        isRequired: withInfoIconLabelProps.isRequired,
        iconName: withInfoIconLabelProps.iconName,
        toolTipText: withInfoIconLabelProps.toolTipText,
        toolTipPosition: withInfoIconLabelProps.toolTipPosition,
    },
};

export const WithClassName: Story = {
    args: {
        text: 'Label with class name ',
        id: 'label-with-class-name',
        forElement: 'labelWithClassName',
        className: 'label-wrapper',
    },

    render: (args: any) => ({
        components: {
            CxmLabel,
        },
        setup() {
            return { args };
        },
        template: `<CxmLabel :text='args.text' :id='args.id' :forElement='args.forElement' :className='args.className' />`,
    }),
};
