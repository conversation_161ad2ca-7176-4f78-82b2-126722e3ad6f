import type { Meta, StoryObj } from '@storybook/vue3';
import BravoScrollPanel from '../components/BravoScrollPanel.vue';

const meta = {
    title: 'Panel/ScrollPanel',
    component: BravoScrollPanel,
    tags: ['autodocs'],
    argTypes: {
        style: { control: 'text' },
    },
} satisfies Meta<typeof BravoScrollPanel>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoScrollPanel style="width: 100%; height: 200px">
    <div style="padding: 1rem">
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
        Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
      </p>
      <p>
        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. 
        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
      </p>
      <p>
        Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, 
        eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.
      </p>
    </div>
  </BravoScrollPanel>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoScrollPanel },
        template: `
      <BravoScrollPanel style="width: 100%; height: 200px" v-bind="args">
        <div style="padding: 1rem">
          <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
            Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
          </p>
          <p>
            Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. 
            Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
          </p>
          <p>
            Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, 
            eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.
          </p>
        </div>
      </BravoScrollPanel>
    `,
    }),
};

// Customized story
export const Customized: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <ScrollPanel style="width: 100%; height: 200px; border: 1px solid var(--surface-border); border-radius: 4px">
    <div style="padding: 1rem">
      <h4>Custom Styled ScrollPanel</h4>
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
        Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
      </p>
      <p>
        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. 
        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
      </p>
    </div>
  </BravoScrollPanel>
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoScrollPanel },
        template: `
      <BravoScrollPanel style="width: 100%; height: 200px; border: 1px solid var(--surface-border); border-radius: 4px" v-bind="args">
        <div style="padding: 1rem">
          <h4>Custom Styled ScrollPanel</h4>
          <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
            Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
          </p>
          <p>
            Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. 
            Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
          </p>
        </div>
      </BravoScrollPanel>
    `,
    }),
};
