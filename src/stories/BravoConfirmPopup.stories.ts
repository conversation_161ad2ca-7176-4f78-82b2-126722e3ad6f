import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import BravoConfirmPopup from '../components/BravoConfirmPopup.vue';
import BravoButton from '../components/BravoButton.vue';
import { useConfirm } from 'primevue/useconfirm';

const meta = {
    title: 'Overlay/ConfirmPopup',
    component: BravoConfirmPopup,
    tags: ['autodocs'],
    argTypes: {
        group: { control: 'text' },
    },
} satisfies Meta<typeof BravoConfirmPopup>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic popup
export const Basic: Story = {
    render: (args) => ({
        components: { BravoConfirmPopup, BravoButton },
        setup() {
            const confirm = useConfirm();

            const confirmPopup = (event: Event) => {
                confirm.require({
                    target: event.currentTarget,
                    message: 'Are you sure you want to proceed?',
                    icon: 'pi pi-exclamation-triangle',
                    accept: () => {
                        // Actual action
                    },
                    reject: () => {
                        // Cancel action
                    },
                });
            };

            return { confirmPopup };
        },
        template: `
      <div>
        <BravoConfirmPopup />
        <BravoButton @click="confirmPopup" label="Confirm" severity="info" />
      </div>
    `,
    }),
};

// Custom template
export const CustomTemplate: Story = {
    render: (args) => ({
        components: { BravoConfirmPopup, BravoButton },
        setup() {
            const confirm = useConfirm();

            const confirmCustom = (event: Event) => {
                confirm.require({
                    target: event.currentTarget,
                    message: 'Do you want to delete this record?',
                    icon: 'pi pi-info-circle',
                    acceptClass: 'p-button-danger',
                    accept: () => {},
                    reject: () => {},
                });
            };

            return { confirmCustom };
        },
        template: `
      <div>
        <BravoConfirmPopup>
          <template #message="slotProps">
            <div class="flex flex-column align-items-center">
              <i :class="slotProps.message.icon" style="font-size: 2rem" />
              <p>{{ slotProps.message.message }}</p>
            </div>
          </template>
        </BravoConfirmPopup>
        <BravoButton @click="confirmCustom" label="Custom Template" />
      </div>
    `,
    }),
};
