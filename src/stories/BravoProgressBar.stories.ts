import type { Meta, StoryObj } from '@storybook/vue3';
import BravoProgressBar from '../components/BravoProgressBar.vue';

const meta = {
    title: 'Misc/ProgressBar',
    component: BravoProgressBar,
    tags: ['autodocs'],
    argTypes: {
        value: { control: 'number' },
        mode: {
            control: 'select',
            options: ['determinate', 'indeterminate'],
        },
        showValue: { control: 'boolean' },
        unit: { control: 'text' },
        color: { control: 'color' },
    },
} satisfies Meta<typeof BravoProgressBar>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
    args: {
        value: 50,
    },
};

export const Indeterminate: Story = {
    args: {
        mode: 'indeterminate',
    },
};
