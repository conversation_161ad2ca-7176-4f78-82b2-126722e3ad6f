import type { Meta, StoryObj } from '@storybook/vue3';
import BravoTreeSelect from '../components/BravoTreeSelect.vue';
import { ref } from 'vue';

const meta = {
    title: 'Form/TreeSelect',
    component: BravoTreeSelect,
    tags: ['autodocs'],
    argTypes: {
        modelValue: { control: 'object' },
        options: { control: 'object' },
        placeholder: { control: 'text' },
        selectionMode: { control: 'select', options: ['single', 'multiple', 'checkbox'] },
    },
} satisfies Meta<typeof BravoTreeSelect>;

export default meta;
type Story = StoryObj<typeof meta>;

const sampleNodes = [
    {
        key: '0',
        label: 'Documents',
        data: 'Documents Folder',
        icon: 'pi pi-fw pi-folder',
        children: [
            {
                key: '0-0',
                label: 'Work',
                data: 'Work Folder',
                icon: 'pi pi-fw pi-folder',
                children: [
                    { key: '0-0-0', label: 'Expenses.doc', icon: 'pi pi-fw pi-file', data: 'Expenses Document' },
                    { key: '0-0-1', label: 'Resume.doc', icon: 'pi pi-fw pi-file', data: 'Resume Document' },
                ],
            },
            {
                key: '0-1',
                label: 'Home',
                data: 'Home Folder',
                icon: 'pi pi-fw pi-folder',
                children: [
                    { key: '0-1-0', label: 'Invoices.txt', icon: 'pi pi-fw pi-file', data: 'Invoices for this month' },
                ],
            },
        ],
    },
];

export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoTreeSelect
    v-model="selectedNode"
    :options="nodes"
    placeholder="Select Item"
  />
</template>

<script setup>
import { ref } from 'vue';

const selectedNode = ref(null);
const nodes = ref([
  {
    key: '0',
    label: 'Documents',
    children: [
      {
        key: '0-0',
        label: 'Work',
        children: [
          { key: '0-0-0', label: 'Expenses.doc' },
          { key: '0-0-1', label: 'Resume.doc' }
        ]
      }
    ]
  }
]);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoTreeSelect },
        setup() {
            const selectedNode = ref(null);
            const nodes = ref(sampleNodes);
            return { selectedNode, nodes };
        },
        template: `
      <BravoTreeSelect
        v-model="selectedNode"
        :options="nodes"
        placeholder="Select Item"
      />
    `,
    }),
};

export const Multiple: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoTreeSelect
    v-model="selectedNodes"
    :options="nodes"
    selectionMode="multiple"
    placeholder="Select Items"
  />
</template>

<script setup>
import { ref } from 'vue';

const selectedNodes = ref([]);
const nodes = ref([
  {
    key: '0',
    label: 'Documents',
    children: [
      {
        key: '0-0',
        label: 'Work',
        children: [
          { key: '0-0-0', label: 'Expenses.doc' },
          { key: '0-0-1', label: 'Resume.doc' }
        ]
      }
    ]
  }
]);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoTreeSelect },
        setup() {
            const selectedNodes = ref([]);
            const nodes = ref(sampleNodes);
            return { selectedNodes, nodes };
        },
        template: `
      <BravoTreeSelect
        v-model="selectedNodes"
        :options="nodes"
        selectionMode="multiple"
        placeholder="Select Items"
      />
    `,
    }),
};
