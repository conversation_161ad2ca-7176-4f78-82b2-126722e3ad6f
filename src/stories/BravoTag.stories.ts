import type { Meta, StoryObj } from '@storybook/vue3';
import BravoTag from '../components/BravoTag.vue';

const meta = {
    title: 'Misc/Tag',
    component: BravoTag,
    tags: ['autodocs'],
    argTypes: {
        value: { control: 'text' },
        severity: {
            control: 'select',
            options: ['success', 'info', 'warn', 'danger'],
        },
        state: {
            control: 'select',
            options: ['new', 'ready', 'waiting', 'resolved', 'closed', 'draft', 'published', 'archived', undefined],
        },
        rounded: { control: 'boolean' },
        icon: { control: 'text' },
    },
} satisfies Meta<typeof BravoTag>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
    args: {
        value: 'New',
    },
};

export const Severities: Story = {
    render: () => ({
        components: { BravoTag },
        template: `
      <div class="card flex gap-2">
        <BravoTag value="Primary" />
        <BravoTag severity="success" value="Success" />
        <BravoTag severity="info" value="Info" />
        <BravoTag severity="warn" value="Warn" />
        <BravoTag severity="danger" value="Danger" />
      </div>
    `,
    }),
};

export const Pills: Story = {
    render: () => ({
        components: { BravoTag },
        template: `
      <div class="card flex gap-2">
        <BravoTag value="Primary" rounded />
        <BravoTag severity="success" value="Success" rounded />
        <BravoTag severity="info" value="Info" rounded />
        <BravoTag severity="warn" value="Warn" rounded />
        <BravoTag severity="danger" value="Danger" rounded />
      </div>
    `,
    }),
};

export const Icons: Story = {
    render: () => ({
        components: { BravoTag },
        template: `
      <div class="card flex gap-2">
        <BravoTag icon="pi pi-user" value="User" />
        <BravoTag icon="pi pi-check" severity="success" value="Verified" />
        <BravoTag icon="pi pi-info-circle" severity="info" value="Info" />
        <BravoTag icon="pi pi-exclamation-triangle" severity="warn" value="Warn" />
        <BravoTag icon="pi pi-times" severity="danger" value="Error" />
      </div>
    `,
    }),
};

export const CaseStatuses: Story = {
    render: () => ({
        components: { BravoTag },
        template: `
      <div class="card flex gap-2">
        <BravoTag state="new" value="New" />
        <BravoTag state="ready" value="Ready" />
        <BravoTag state="waiting" value="Waiting" />
        <BravoTag state="resolved" value="Resolved" />
        <BravoTag state="closed" value="Closed" />
      </div>
    `,
    }),
};

export const DraftPublishedArchived: Story = {
    render: () => ({
        components: { BravoTag },
        template: `
      <div class="card flex gap-2">
        <BravoTag state="draft" value="Draft" />
        <BravoTag state="published" value="Published" />
        <BravoTag state="unpublished" value="Unpublished" />
        <BravoTag state="previously-published" value="Previously Published" />
        <BravoTag state="archived" value="Archived" />
      </div>
    `,
    }),
};
