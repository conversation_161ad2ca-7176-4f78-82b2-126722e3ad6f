import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import BravoDialog from '../components/BravoDialog.vue';
import BravoButton from '../components/BravoButton.vue';
import { ref } from 'vue';

const meta = {
    title: 'Overlay/Dialog',
    component: BravoDialog,
    tags: ['autodocs'],
    argTypes: {
        header: { control: 'text' },
        visible: { control: 'boolean' },
        modal: { control: 'boolean' },
        position: {
            control: 'select',
            options: ['center', 'top', 'bottom', 'left', 'right', 'topleft', 'topright', 'bottomleft', 'bottomright'],
        },
        breakpoints: { control: 'object' },
        draggable: { control: 'boolean' },
        resizable: { control: 'boolean' },
        maximizable: { control: 'boolean' },
    },
} satisfies Meta<typeof BravoDialog>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Dialog
export const Basic: Story = {
    render: (args) => ({
        components: { <PERSON>Dialog, BravoButton },
        setup() {
            const visible = ref(false);
            return { visible };
        },
        template: `
      <div class="card flex justify-content-center">
        <BravoButton label="Show" icon="pi pi-external-link" @click="visible = true" />
        <BravoDialog v-model:visible="visible" modal header="Header">
          <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
            Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
          </p>
        </BravoDialog>
      </div>
    `,
    }),
};

// Responsive Dialog
export const Responsive: Story = {
    render: (args) => ({
        components: { BravoDialog, BravoButton },
        setup() {
            const visible = ref(false);
            const breakpoints = {
                '960px': '75vw',
                '640px': '100vw',
            };
            return { visible, breakpoints };
        },
        template: `
      <div class="card flex justify-content-center">
        <BravoButton label="Show" icon="pi pi-external-link" @click="visible = true" />
        <BravoDialog v-model:visible="visible" :breakpoints="breakpoints" header="Responsive">
          <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
          </p>
        </BravoDialog>
      </div>
    `,
    }),
};

// Position
export const Position: Story = {
    render: (args) => ({
        components: { BravoDialog, BravoButton },
        setup() {
            const visible = ref(false);
            const position = ref('center');

            const positions = [
                'center',
                'top',
                'bottom',
                'left',
                'right',
                'topleft',
                'topright',
                'bottomleft',
                'bottomright',
            ];

            const showDialog = (pos: string) => {
                position.value = pos;
                visible.value = true;
            };

            return { visible, position, positions, showDialog };
        },
        template: `
      <div class="card">
        <div class="flex flex-wrap gap-2">
          <BravoButton v-for="pos in positions" :key="pos"
            @click="showDialog(pos)"
            :label="pos"
            class="p-button-secondary" />
        </div>
        
        <BravoDialog v-model:visible="visible" :position="position" modal header="Position">
          <p>Dialog position: {{ position }}</p>
        </BravoDialog>
      </div>
    `,
    }),
};

// Advanced Features
export const Advanced: Story = {
    render: (args) => ({
        components: { BravoDialog, BravoButton },
        setup() {
            const visible = ref(false);
            return { visible };
        },
        template: `
      <div class="card flex justify-content-center">
        <BravoButton label="Show" icon="pi pi-external-link" @click="visible = true" />
        <BravoDialog 
          v-model:visible="visible" 
          modal 
          header="Advanced Dialog" 
          :style="{ width: '50vw' }" 
          :maximizable="true"
          :resizable="true"
          :draggable="true"
        >
          <p class="m-0">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
            Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
          </p>
          <template #footer>
            <BravoButton label="Cancel" icon="pi pi-times" @click="visible = false" class="p-button-text"/>
            <BravoButton label="Save" icon="pi pi-check" @click="visible = false" autofocus />
          </template>
        </BravoDialog>
      </div>
    `,
    }),
};
