import type { Meta, StoryObj } from '@storybook/vue3';
import BravoSkeleton from '../components/BravoSkeleton.vue';

const meta = {
    title: 'Misc/Skeleton',
    component: BravoSkeleton,
    tags: ['autodocs'],
    argTypes: {
        shape: {
            control: 'select',
            options: ['rectangle', 'circle'],
        },
        size: { control: 'text' },
        width: { control: 'text' },
        height: { control: 'text' },
        borderRadius: { control: 'text' },
        animation: {
            control: 'select',
            options: ['wave', 'none'],
        },
    },
} satisfies Meta<typeof BravoSkeleton>;

export default meta;
type Story = StoryObj<typeof meta>;

/*Basic*/
export const Basic: Story = {
    args: {
        width: '100%',
        height: '1rem',
        class: 'mb-3',
    },
};

/*Shapes*/
export const Shapes: Story = {
    render: () => ({
        components: { BravoSkeleton },
        template: `
      <div class="card">
        <div class="flex gap-3">
          <BravoSkeleton class="mb-3" width="4rem" height="4rem" />
          <BravoSkeleton class="mb-3" width="4rem" height="4rem" borderRadius="16px" />
          <BravoSkeleton class="mb-3" shape="circle" width="4rem" height="4rem" />
        </div>
      </div>
    `,
    }),
};

export const Card: Story = {
    render: () => ({
        components: { BravoSkeleton },
        template: `
      <div class="card">
        <div class="border-round border-1 surface-border p-3">
          <div class="flex mb-3">
            <BravoSkeleton shape="circle" size="4rem" class="mr-3 mb-3" />
            <div>
              <BravoSkeleton width="10rem" class="mb-3" />
              <BravoSkeleton width="5rem" class="mb-3" />
              <BravoSkeleton height=".5rem" class="mb-3" />
            </div>
          </div>
          <BravoSkeleton width="100%" height="150px" class="mb-3" />
          <div class="flex justify-content-between">
            <BravoSkeleton width="4rem" height="2rem" class="mb-3" />
            <BravoSkeleton width="4rem" height="2rem" class="mb-3" />
          </div>
        </div>
      </div>
    `,
    }),
};

export const DataTable: Story = {
    render: () => ({
        components: { BravoSkeleton },
        template: `
      <div class="card">
        <BravoSkeleton width="250px" height="20px" class="mb-3" />
        <BravoSkeleton width="100%" height="50px" class="mb-3" />
        <BravoSkeleton width="100%" height="40px" class="mb-3" />
        <BravoSkeleton width="100%" height="40px" class="mb-3" />
        <BravoSkeleton width="100%" height="40px" class="mb-3" />
      </div>
    `,
    }),
};
