import type { Meta, StoryObj } from '@storybook/vue3';
import BravoOrderList from '../components/BravoOrderList.vue';
import { ref } from 'vue';

interface BravoOrderListProps {
    modelValue?: any[];
    dataKey: string;
    selection?: any[];
    metaKeySelection?: boolean;
    listStyle?: object;
    dragdrop?: boolean;
}

const meta = {
    title: 'Data/OrderList',
    component: BravoOrderList,
    tags: ['autodocs'],
    argTypes: {
        modelValue: { control: 'object' },
        dataKey: { control: 'text' },
        selection: { control: 'object' },
        metaKeySelection: { control: 'boolean' },
        listStyle: { control: 'object' },
        dragdrop: { control: 'boolean' },
    },
} as Meta<typeof BravoOrderList>;

export default meta;
type Story = StoryObj<typeof meta>;

const sampleProducts = [
    { id: '1', name: 'Product 1', category: 'Electronics', price: 100, image: 'product1.jpg', status: 'In Stock' },
    { id: '2', name: 'Product 2', category: 'Fashion', price: 80, image: 'product2.jpg', status: 'Low Stock' },
    { id: '3', name: 'Product 3', category: 'Home', price: 90, image: 'product3.jpg', status: 'Out of Stock' },
    { id: '4', name: 'Product 4', category: 'Electronics', price: 70, image: 'product4.jpg', status: 'In Stock' },
    { id: '5', name: 'Product 5', category: 'Fashion', price: 120, image: 'product5.jpg', status: 'In Stock' },
];

// Basic story
export const Basic: Story = {
    args: {
        modelValue: sampleProducts,
        dataKey: 'id',
    },
    render: (args) => ({
        components: { BravoOrderList },
        setup() {
            const products = ref([...sampleProducts]);

            return { products };
        },
        template: `
      <BravoOrderList 
        v-model="products" 
        dataKey="id" 
        :listStyle="{ width: '20rem', height: '400px' }"
      >
        <template #option="{ option }">
          {{ option.name }}
        </template>
      </BravoOrderList>
    `,
    }),
};

// Template story with detailed items
export const WithTemplate: Story = {
    args: {
        modelValue: sampleProducts,
        dataKey: 'id',
    },
    render: (args) => ({
        components: { BravoOrderList },
        setup() {
            const products = ref([...sampleProducts]);

            return { products };
        },
        template: `
      <BravoOrderList 
        v-model="products" 
        dataKey="id" 
        :listStyle="{ width: '20rem', height: '400px' }"
      >
        <template #item="slotProps">
          <div class="product-item">
            <div class="product-list-detail">
              <h5 class="mb-2">{{slotProps.item.name}}</h5>
              <i class="pi pi-tag product-category-icon"></i>
              <span class="product-category">{{slotProps.item.category}}</span>
            </div>
            <div class="product-list-action">
              <span class="product-price">\${{slotProps.item.price}}</span>
              <span :class="['product-badge', 'status-' + slotProps.item.status.toLowerCase().replace(' ', '')]">
                {{slotProps.item.status}}
              </span>
            </div>
          </div>
        </template>
      </BravoOrderList>
    `,
        styles: `
      <style>
        .product-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 1rem;
          width: 100%;
        }

        .product-list-detail {
          flex: 1 1 0;
        }

        .product-list-detail h5 {
          margin: 0;
          font-size: 1rem;
          font-weight: 600;
        }

        .product-category-icon {
          margin-right: .5rem;
          vertical-align: middle;
        }

        .product-category {
          font-weight: 600;
          vertical-align: middle;
        }

        .product-list-action {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 0.5rem;
        }

        .product-price {
          font-size: 1.2rem;
          font-weight: 600;
        }

        .product-badge {
          border-radius: 4px;
          padding: .25rem .5rem;
          font-weight: 700;
          font-size: .875rem;
        }

        .status-instock {
          background: #C8E6C9;
          color: #256029;
        }

        .status-outofstock {
          background: #FFCDD2;
          color: #C63737;
        }

        .status-lowstock {
          background: #FEEDAF;
          color: #8A5340;
        }
      </style>
    `,
    }),
};

// Drag and Drop story
export const DragDrop: Story = {
    args: {
        modelValue: sampleProducts,
        dataKey: 'id',
        dragdrop: true,
    },
    render: (args) => ({
        components: { BravoOrderList },
        setup() {
            const products = ref([...sampleProducts]);

            return { products };
        },
        template: `
      <BravoOrderList 
        v-model="products" 
        dataKey="id" 
        :dragdrop="true"
        :listStyle="{ width: '20rem', height: '400px' }"
      >
        <template #item="slotProps">
          <div class="product-item">
            <div class="product-list-detail">
              <h5 class="mb-2">{{slotProps.item.name}}</h5>
              <i class="pi pi-tag product-category-icon"></i>
              <span class="product-category">{{slotProps.item.category}}</span>
            </div>
            <div class="product-list-action">
              <span class="product-price">\${{slotProps.item.price}}</span>
              <span :class="['product-badge', 'status-' + slotProps.item.status.toLowerCase().replace(' ', '')]">
                {{slotProps.item.status}}
              </span>
            </div>
          </div>
        </template>
      </BravoOrderList>
    `,
        styles: `
      <style>
        .product-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 1rem;
          width: 100%;
          cursor: move;
        }

        .product-list-detail {
          flex: 1 1 0;
        }

        .product-list-detail h5 {
          margin: 0;
          font-size: 1rem;
          font-weight: 600;
        }

        .product-category-icon {
          margin-right: .5rem;
          vertical-align: middle;
        }

        .product-category {
          font-weight: 600;
          vertical-align: middle;
        }

        .product-list-action {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 0.5rem;
        }

        .product-price {
          font-size: 1.2rem;
          font-weight: 600;
        }

        .product-badge {
          border-radius: 4px;
          padding: .25rem .5rem;
          font-weight: 700;
          font-size: .875rem;
        }

        .status-instock {
          background: #C8E6C9;
          color: #256029;
        }

        .status-outofstock {
          background: #FFCDD2;
          color: #C63737;
        }

        .status-lowstock {
          background: #FEEDAF;
          color: #8A5340;
        }
      </style>
    `,
    }),
};
