import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import BravoContextMenu from '../components/BravoContextMenu.vue';

interface ContextMenuItem {
    label: string;
    icon?: string;
    items?: ContextMenuItem[];
    command?: () => void;
    url?: string;
    disabled?: boolean;
}

const meta = {
    title: 'Menu/ContextMenu',
    component: BravoContextMenu,
    tags: ['autodocs'],
    argTypes: {
        model: { control: 'object' },
        global: { control: 'boolean' },
        pt: { control: 'object' },
    },
} satisfies Meta<typeof BravoContextMenu>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    args: {
        model: [
            {
                label: 'File',
                items: [
                    {
                        label: 'New',
                        icon: 'pi pi-plus',
                        command: () => console.log('New clicked'),
                    },
                    {
                        label: 'Open',
                        icon: 'pi pi-folder-open',
                        command: () => console.log('Open clicked'),
                    },
                    {
                        label: 'Save',
                        icon: 'pi pi-save',
                        command: () => console.log('Save clicked'),
                    },
                ],
            },
            {
                label: 'Edit',
                items: [
                    {
                        label: 'Cut',
                        icon: 'pi pi-cut',
                        command: () => console.log('Cut clicked'),
                    },
                    {
                        label: 'Copy',
                        icon: 'pi pi-copy',
                        command: () => console.log('Copy clicked'),
                    },
                    {
                        label: 'Paste',
                        icon: 'pi pi-paste',
                        command: () => console.log('Paste clicked'),
                    },
                ],
            },
        ],
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div @contextmenu="onRightClick">
    Right click here to show menu
    <ContextMenu ref="menu" :model="model" />
  </div>
</template>
`,
            },
        },
        layout: 'padded',
        viewport: {
            defaultViewport: 'desktop',
        },
    },
    render: (args) => ({
        components: { BravoContextMenu },
        setup() {
            const menu = ref();
            const targetRef = ref();

            const onRightClick = (event: Event) => {
                menu.value?.show(event);
                event.preventDefault();
            };

            return { args, menu, targetRef, onRightClick };
        },
        template: `
      <div 
        ref="targetRef"
        @contextmenu.prevent="onRightClick"
        style="padding: 2rem; border: 1px solid #ccc; cursor: context-menu; margin: 1rem;"
      >
        Right click here to show menu
        <BravoContextMenu ref="menu" :model="args.model" />
      </div>
    `,
    }),
};

// Global Context Menu
export const Global: Story = {
    args: {
        global: true,
        model: [
            {
                label: 'Save',
                icon: 'pi pi-save',
                command: () => console.log('Save clicked'),
            },
            {
                label: 'Update',
                icon: 'pi pi-refresh',
                command: () => console.log('Update clicked'),
            },
            {
                label: 'Delete',
                icon: 'pi pi-trash',
                command: () => console.log('Delete clicked'),
            },
        ],
    },
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="card">
    <p>Right click anywhere in this section</p>
    <ContextMenu :model="model" global />
  </div>
</template>
`,
            },
        },
        layout: 'padded',
        viewport: {
            defaultViewport: 'desktop',
        },
    },
    render: (args) => ({
        components: { BravoContextMenu },
        setup() {
            const menu = ref();

            return { args, menu };
        },
        template: `
      <div 
        style="
          padding: 2rem; 
          border: 1px solid #ccc; 
          min-height: 200px; 
          margin: 1rem;
          cursor: context-menu;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: var(--surface-ground);
        "
      >
        <p style="margin: 0;">Right click anywhere in this section</p>
        <BravoContextMenu ref="menu" v-bind="args" />
      </div>
    `,
    }),
};
