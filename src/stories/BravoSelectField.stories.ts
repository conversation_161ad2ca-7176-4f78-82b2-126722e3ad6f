import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/vue3';
import BravoSelectField from '../components/BravoSelectField.vue';
import { ref } from 'vue';

const cities = [
    { name: 'New York', value: 'NY' },
    { name: 'London', value: 'LDN' },
    { name: 'Paris', value: 'PRS' },
    { name: 'Tokyo', value: 'TK' },
];

// Sample users for the user select component with PrimeFaces CDN avatar images
const users = [
    { name: '<PERSON>', id: 'user1', avatar: 'https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png' },
    { name: '<PERSON>', id: 'user2', avatar: 'https://primefaces.org/cdn/primevue/images/avatar/asiyajavayant.png' },
    { name: '<PERSON>', id: 'user3', avatar: 'https://primefaces.org/cdn/primevue/images/avatar/onyamalimba.png' },
    { name: '<PERSON>', id: 'user4', avatar: 'https://primefaces.org/cdn/primevue/images/avatar/ionibowcher.png' },
    { name: '<PERSON>', id: 'user5', avatar: 'https://primefaces.org/cdn/primevue/images/avatar/xuxuefeng.png' },
];

// Meta information for the component
const meta = {
    title: 'Form/SelectField',
    component: BravoSelectField,
    tags: ['autodocs'],
    argTypes: {
        placeholder: { control: 'text' },
        options: { control: 'object' },
        modelValue: { control: 'text' },
        filter: { control: 'boolean' },
        showClear: { control: 'boolean' },
        disabled: { control: 'boolean' },
        text: { control: 'boolean' },
        view: { control: 'boolean' },
    },
} satisfies Meta<typeof BravoSelectField>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story with simple options
export const Basic: Story = {
    render: () => ({
        components: { BravoSelectField },
        setup() {
            const selectedCity = ref(null);
            return { selectedCity, cities };
        },
        template: `
      <BravoSelectField
        v-model="selectedCity"
        :options="cities"
        id="text-style-select-field"
        dataTestId="text-style-select-field"
        optionLabel="name"
        placeholder="Select a City"
        class="w-full md:w-80"
      />
    `,
    }),
};

// Text-only style
export const TextStyle: Story = {
    render: () => ({
        components: { BravoSelectField },
        setup() {
            const selectedCity = ref(null);
            return { selectedCity, cities };
        },
        template: `
      <BravoSelectField
        v-model="selectedCity"
        :options="cities"
        id="text-style-select-field"
        dataTestId="text-style-select-field"
        optionLabel="name"
        placeholder="Select a City"
        text
        class="w-full md:w-80"
      />
    `,
    }),
};

// View mode example
export const ViewMode: Story = {
    render: () => ({
        components: { BravoSelectField },
        setup() {
            const selectedCity = ref(null);
            return { selectedCity, cities };
        },
        template: `
      <div class="flex flex-col gap-4">
        <BravoSelectField
          v-model="selectedCity"
          :options="cities"
          optionLabel="name"
          id="view-mode-select-field"
          dataTestId="view-mode-select-field"
          placeholder="Select a City"
          view
          class="w-full md:w-80"
        />
        <div class="text-sm text-surface-600">
          Try clicking to select a value, then click away to see the view state with the selected value.
        </div>
      </div>
    `,
    }),
};

// With filter capability
export const Filterable: Story = {
    render: () => ({
        components: { BravoSelectField },
        setup() {
            const selectedCity = ref(null);
            return { selectedCity, cities };
        },
        template: `
      <BravoSelectField
        v-model="selectedCity"
        :options="cities"
        optionLabel="name"
        id="filterable-select-field"
        dataTestId="filterable-select-field"
        placeholder="Search and select a City"
        filter
        class="w-full md:w-80"
      />
    `,
    }),
};

// With clear button
export const WithClearButton: Story = {
    render: () => ({
        components: { BravoSelectField },
        setup() {
            const selectedCity = ref(null);
            return { selectedCity, cities };
        },
        template: `
      <BravoSelectField
        v-model="selectedCity"
        :options="cities"
        id="with-clear-btn-select-field"
        dataTestId="with-clear-btn-select-field"
        optionLabel="name"
        placeholder="Select a City"
        showClear
        class="w-full md:w-80"
      />
    `,
    }),
};

// Disabled state
export const Disabled: Story = {
    render: () => ({
        components: { BravoSelectField },
        setup() {
            const selectedCity = ref(null);
            return { selectedCity, cities };
        },
        template: `
      <BravoSelectField
        v-model="selectedCity"
        :options="cities"
        optionLabel="name"
        id="disabled-select-field"
        dataTestId="disabled-select-field"
        placeholder="Select a City"
        disabled
        class="w-full md:w-80"
      />
    `,
    }),
};

// User Select with Avatars
export const UserSelectWithAvatars: Story = {
    render: () => ({
        components: { BravoSelectField },
        setup() {
            const selectedUser = ref(null);
            
            const findSelectedUser = (value) => {
                if (!value) return null;
                return users.find(user => user.id === value);
            };
            
            return { selectedUser, users, findSelectedUser };
        },
        template: `
      <BravoSelectField
        v-model="selectedUser"
        :options="users"
        optionLabel="name"
        optionValue="id"
        id="user-select-field"
        dataTestId="user-select-field"
        placeholder="Select a User"
        filter
        showClear
        class="w-full md:w-80"
      >
        <template #option="slotProps">
          <div class="flex items-center">
            <span class="flex justify-center items-center mr-2" style="width: 32px; height: 32px; border-radius: 50%; background-color: #f0f0f0; border: 1px solid #ddd; overflow: hidden;">
              <img :src="slotProps.option.avatar" :alt="slotProps.option.name" style="width: 30px; height: 30px;" onerror="this.src='https://www.gravatar.com/avatar/?d=mp'; this.onerror=null;" />
            </span>
            <div class="font-medium">{{ slotProps.option.name }}</div>
          </div>
        </template>
        <template #value="slotProps">
          <div v-if="slotProps.value" class="flex items-center">
            <!-- Find the full user object based on the ID value -->
            <span v-if="findSelectedUser(slotProps.value)" class="flex justify-center items-center mr-2" style="width: 32px; height: 32px; border-radius: 50%; background-color: #f0f0f0; border: 1px solid #ddd; overflow: hidden;">
              <img :src="findSelectedUser(slotProps.value).avatar" :alt="findSelectedUser(slotProps.value).name" style="width: 30px; height: 30px;" onerror="this.src='https://www.gravatar.com/avatar/?d=mp'; this.onerror=null;" />
            </span>
            <div v-if="findSelectedUser(slotProps.value)" class="font-medium">{{ findSelectedUser(slotProps.value).name }}</div>
            <div v-else class="text-sm text-gray-400">(ID: {{ slotProps.value }})</div>
          </div>
          <span v-else>
            {{ slotProps.placeholder }}
          </span>
        </template>
      </BravoSelectField>
    `,
    }),
};
