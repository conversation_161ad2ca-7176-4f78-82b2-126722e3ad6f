import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import BravoSelectButton from '../components/BravoSelectButton.vue';
import { ref } from 'vue';

const meta = {
    title: 'Form/SelectButton',
    component: BravoSelectButton,
    tags: ['autodocs'],
    argTypes: {
        options: { control: 'object' },
        modelValue: { control: 'text' },
        multiple: { control: 'boolean' },
    },
} satisfies Meta<typeof BravoSelectButton>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <SelectButton v-model="value" :options="options" optionLabel="name" />
</template>

<script setup>
import { ref } from 'vue';

const value = ref(null);
const options = ref([
  { name: 'Option 1', value: 1 },
  { name: 'Option 2', value: 2 },
  { name: 'Option 3', value: 3 }
]);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoSelectButton },
        setup() {
            const value = ref(null);
            const options = ref([
                { name: 'Option 1', value: 1 },
                { name: 'Option 2', value: 2 },
                { name: 'Option 3', value: 3 },
            ]);

            return { value, options };
        },
        template: `
      <BravoSelectButton
        v-model="value"
        :options="options"
        optionLabel="name"
      />
    `,
    }),
};

export const Multiple: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <SelectButton
    v-model="value"
    :options="options"
    optionLabel="name"
    multiple
  />
</template>

<script setup>
import { ref } from 'vue';

const value = ref([]);
const options = ref([
  { name: 'Option 1', value: 1 },
  { name: 'Option 2', value: 2 },
  { name: 'Option 3', value: 3 }
]);
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoSelectButton },
        setup() {
            const value = ref([]);
            const options = ref([
                { name: 'Option 1', value: 1 },
                { name: 'Option 2', value: 2 },
                { name: 'Option 3', value: 3 },
            ]);

            return { value, options };
        },
        template: `
      <BravoSelectButton
        v-model="value"
        :options="options"
        optionLabel="name"
        multiple
      />
    `,
    }),
};
