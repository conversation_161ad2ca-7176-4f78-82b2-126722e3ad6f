import type { Meta, StoryObj } from '@storybook/vue3';
import BravoPasswordResetConfirm from '../components/BravoPasswordResetConfirm.vue';

const meta = {
    title: 'Components/BravoPasswordResetConfirm',
    component: BravoPasswordResetConfirm,
    tags: ['autodocs'],
    parameters: {
        layout: 'fullscreen',
        docs: {
            story: {
                height: '500px',
            },
        },
    },
    argTypes: {
        userEmail: {
            description: 'Email address of the user resetting their password',
            control: 'text',
            table: {
                type: {
                    summary: 'string',
                },
            },
        },
        submitHandler: {
            description: 'Function that handles the password reset submission',
            table: {
                type: {
                    summary: '(password: string) => Promise<void>',
                },
            },
        },
        onSubmit: {
            description: 'Emitted when the form is submitted with a new password',
            table: {
                type: {
                    summary: '(event: { password: string }) => void',
                },
            },
        },
        onBackToLogin: {
            description: 'Emitted when the user clicks the "Back to Login" button on the success screen',
            table: {
                type: {
                    summary: '() => void',
                },
            },
        },
    },
} satisfies Meta<typeof BravoPasswordResetConfirm>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default story with basic functionality
export const Default: Story = {
    args: {
        userEmail: '<EMAIL>',
    },
    render: (args) => ({
        components: { BravoPasswordResetConfirm },
        setup() {
            const onSubmit = (data: { password: string }) => {
                console.log('New password submitted:', data.password);
            };
            
            const onBackToLogin = () => {
                console.log('Back to login clicked');
                alert('Navigating back to login screen');
            };
            
            return { args, onSubmit, onBackToLogin };
        },
        template: '<BravoPasswordResetConfirm v-bind="args" @submit="onSubmit" @back-to-login="onBackToLogin" />',
    }),
};

// Story demonstrating successful password reset
export const SuccessScreen: Story = {
    args: {
        userEmail: '<EMAIL>',
    },
    render: (args) => ({
        components: { BravoPasswordResetConfirm },
        setup() {
            const submitHandler = async (password: string) => {
                console.log('Processing password reset with new password:', password);
                await new Promise(resolve => setTimeout(resolve, 1000));
                return Promise.resolve();
            };
            
            const onSubmit = (data: { password: string }) => {
                console.log('New password submitted:', data.password);
            };
            
            const onBackToLogin = () => {
                console.log('Back to login clicked');
                alert('Navigating back to login screen');
            };
            
            return { args, submitHandler, onSubmit, onBackToLogin };
        },
        template: `
            <div>
                <h3>Success Screen</h3>
                <p>Fill in matching passwords and submit to see the success screen.</p>
                <BravoPasswordResetConfirm 
                    v-bind="args" 
                    :submit-handler="submitHandler"
                    @submit="onSubmit" 
                    @back-to-login="onBackToLogin"
                />
            </div>
        `,
    }),
};

// Story demonstrating validation errors
export const ValidationErrors: Story = {
    args: {
        userEmail: '<EMAIL>',
    },
    render: (args) => ({
        components: { BravoPasswordResetConfirm },
        setup() {
            const onSubmit = (data: { password: string }) => {
                console.log('New password submitted:', data.password);
            };
            
            const onBackToLogin = () => {
                console.log('Back to login clicked');
                alert('Navigating back to login screen');
            };
            
            return { args, onSubmit, onBackToLogin };
        },
        template: `
            <div>
                <h3>Validation Errors</h3>
                <p>Try submitting without a password or with non-matching passwords to see validation errors.</p>
                <BravoPasswordResetConfirm 
                    v-bind="args"
                    @submit="onSubmit" 
                    @back-to-login="onBackToLogin"
                />
            </div>
        `,
    }),
};

// Story demonstrating server error
export const ServerError: Story = {
    args: {
        userEmail: '<EMAIL>',
    },
    render: (args) => ({
        components: { BravoPasswordResetConfirm },
        setup() {
            const submitHandler = async (password: string) => {
                console.log('Processing password reset with new password:', password);
                await new Promise(resolve => setTimeout(resolve, 1000));
                return Promise.reject(new Error('Server error: Unable to reset password. Please try again later.'));
            };
            
            const onSubmit = (data: { password: string }) => {
                console.log('New password submitted:', data.password);
            };
            
            const onBackToLogin = () => {
                console.log('Back to login clicked');
                alert('Navigating back to login screen');
            };
            
            return { args, submitHandler, onSubmit, onBackToLogin };
        },
        template: `
            <div>
                <h3>Server Error</h3>
                <p>This demonstrates how server errors are displayed to the user.</p>
                <BravoPasswordResetConfirm 
                    v-bind="args" 
                    :submit-handler="submitHandler"
                    @submit="onSubmit" 
                    @back-to-login="onBackToLogin"
                />
            </div>
        `,
    }),
}; 