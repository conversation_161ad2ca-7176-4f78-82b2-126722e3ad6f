import { Meta, StoryFn } from '@storybook/vue3';
import BravoZeroStateScreen from '../components/BravoZeroStateScreen.vue';

export default {
  title: 'Components/BravoZeroStateScreen',
  component: BravoZeroStateScreen,
  tags: ['autodocs'],
  argTypes: {
    title: {
      control: 'text',
      description: 'The title displayed in the zero state screen',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'Create your first item' },
      },
    },
    message: {
      control: 'text',
      description: 'The message displayed below the title',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'You haven\'t created any items yet. Click the button below to get started.' },
      },
    },
    buttonLabel: {
      control: 'text',
      description: 'The label for the action button',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'Create New' },
      },
    },
    buttonIcon: {
      control: 'text',
      description: 'The icon for the action button (using PrimeVue icon names)',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'pi pi-plus' },
      },
    },
    imageSrc: {
      control: 'text',
      description: 'URL to the image to display above the title',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'https://cdn.prod.website-files.com/5d4b12a4c03a64ab754f23bd/67f9df4569df927d4c0065e1_content-zero-state.png' },
      },
    },
    imageAlt: {
      control: 'text',
      description: 'Alt text for the image',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'Zero state' },
      },
    },
    actionHandler: {
      action: 'actionHandler',
      description: 'Function to call when the button is clicked',
    },
    showButton: {
      control: 'boolean',
      description: 'Whether to show the action button',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'true' },
      },
    },
    action: {
      action: 'action',
      description: 'Event emitted when the button is clicked'
    }
  },
} as Meta<typeof BravoZeroStateScreen>;

// Template for all stories
const Template: StoryFn<typeof BravoZeroStateScreen> = (args) => ({
  components: { BravoZeroStateScreen },
  setup() {
    return { args };
  },
  template: '<BravoZeroStateScreen v-bind="args" @action="args.action" />',
});

export const Default = Template.bind({});
Default.args = {
  title: 'Create your first item',
  message: 'You haven\'t created any items yet. Click the button below to get started.',
  buttonLabel: 'Create New',
  imageAlt: 'Zero state',
  showButton: true,
  imageSrc: 'https://cdn.prod.website-files.com/5d4b12a4c03a64ab754f23bd/67f9df4569df927d4c0065e1_content-zero-state.png'
};

export const ArticleLibrary = Template.bind({});
ArticleLibrary.args = {
  title: 'Create your first Article',
  message: 'You haven\'t created any Articles yet. Click the button below to start your first Article.',
  buttonLabel: 'New Article',
  buttonIcon: 'pi pi-plus',
  imageSrc: 'https://cdn.prod.website-files.com/5d4b12a4c03a64ab754f23bd/67f9df4569df927d4c0065e1_content-zero-state.png'
};

export const WithImage = Template.bind({});
WithImage.args = {
  title: 'No data available',
  message: 'There is no data to display at this time. Please check back later or add new content.',
  buttonLabel: 'Add Content',
  buttonIcon: 'pi pi-plus',
  imageSrc: 'https://cdn.prod.website-files.com/5d4b12a4c03a64ab754f23bd/67f9df4569df927d4c0065e1_content-zero-state.png',
  imageAlt: 'Content zero state'
};

export const CustomAction: StoryFn<typeof BravoZeroStateScreen> = (args) => ({
  components: { BravoZeroStateScreen },
  setup() {
    const handleAction = (): void => {
      alert('Custom action triggered!');
    };
    
    return { 
      args,
      handleAction
    };
  },
  template: '<BravoZeroStateScreen v-bind="args" :actionHandler="handleAction" @action="args.action" />',
});

CustomAction.args = {
  title: 'No notifications',
  message: 'You don\'t have any notifications yet. When you receive notifications, they will appear here.',
  buttonLabel: 'Check Settings',
  buttonIcon: 'pi pi-cog',
  imageSrc: 'https://cdn.prod.website-files.com/5d4b12a4c03a64ab754f23bd/67f9df4569df927d4c0065e1_content-zero-state.png'
};

export const WithoutButton = Template.bind({});
WithoutButton.args = {
  title: 'Information Only',
  message: 'This is just an informational message with no action needed.',
  showButton: false,
  imageSrc: 'https://cdn.prod.website-files.com/5d4b12a4c03a64ab754f23bd/67f9df4569df927d4c0065e1_content-zero-state.png'
};

export const CustomIcon = Template.bind({});
CustomIcon.args = {
  title: 'Upload your files',
  message: 'Drag and drop files here or click the button below to browse and upload.',
  buttonLabel: 'Browse Files',
  buttonIcon: 'pi pi-upload',
  imageSrc: 'https://cdn.prod.website-files.com/5d4b12a4c03a64ab754f23bd/67f9df4569df927d4c0065e1_content-zero-state.png'
}; 