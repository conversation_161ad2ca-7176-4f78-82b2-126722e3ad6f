import { CxmScale } from '../components/CxmScale';
import { Meta, StoryObj } from '@storybook/vue3';

export default {
    component: CxmScale,
    title: 'Legacy & Custom/Inputs/Scale',
    parameters: {
        docs: {
            description: {
                component: 'Input for collecting a scaled rating.',
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {},
} as Meta<typeof CxmScale>;

type Story = StoryObj<typeof CxmScale>;

export const Default: Story = {
    args: {
        label: 'Howd we do?',
        fromLabel: 'THE WORST',
        toLabel: 'THE BEST',
        options: 6,
    },
    render: (args) => ({
        components: {
            CxmScale,
        },
        data: () => ({ args, value: '', options: 6 }),
        template: `<div>
                    <CxmScale id="cxm-scale" v-bind="args" v-model="value" />
                  </div>
                  `,
    }),
};
