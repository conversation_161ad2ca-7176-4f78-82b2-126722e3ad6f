import type { Meta, StoryObj } from '@storybook/vue3';
import BravoConfirmDialog from '../components/BravoConfirmDialog.vue';
import BravoButton from '../components/BravoButton.vue';
import { useConfirm } from 'primevue/useconfirm';
import { ref, onBeforeUnmount } from 'vue';

const meta = {
    title: 'Overlay/ConfirmDialog',
    component: BravoConfirmDialog,
    tags: ['autodocs'],
    argTypes: {},
} satisfies Meta<typeof BravoConfirmDialog>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic confirmation
export const Basic: Story = {
    render: (args) => ({
        components: { BravoConfirmDialog, BravoButton },
        setup() {
            const confirm = useConfirm();
            const dialogKey = ref(0);

            const confirmDeletion = () => {
                confirm.require({
                    message: 'Are you sure you want to proceed?',
                    header: 'Confirmation',
                    icon: 'pi pi-exclamation-triangle',
                    rejectClass: 'p-button-secondary',
                    accept: () => {
                        console.log('Accepted');
                        confirm.close();
                    },
                    reject: () => {
                        console.log('Rejected');
                        confirm.close();
                    },
                });
            };

            onBeforeUnmount(() => {
                confirm.close();
            });

            return { confirmDeletion, dialogKey };
        },
        template: `
      <div class="card flex justify-content-center">
        <BravoConfirmDialog :key="dialogKey" />
        <BravoButton @click="confirmDeletion" label="Delete" severity="danger" />
      </div>
    `,
    }),
};

// Position examples
export const Positions: Story = {
    render: (args) => ({
        components: { BravoConfirmDialog, BravoButton },
        setup() {
            const confirm = useConfirm();
            const dialogKey = ref(0);

            const showPositioned = (
                position:
                    | 'top'
                    | 'bottom'
                    | 'left'
                    | 'right'
                    | 'topleft'
                    | 'topright'
                    | 'bottomleft'
                    | 'bottomright'
                    | 'center'
            ) => {
                confirm.require({
                    message: 'Would you like to proceed?',
                    header: 'Positioned Confirmation',
                    icon: 'pi pi-info-circle',
                    position: position,
                    rejectClass: 'p-button-secondary',
                    accept: () => {
                        console.log('Accepted');
                        confirm.close();
                    },
                    reject: () => {
                        console.log('Rejected');
                        confirm.close();
                    },
                });
            };

            onBeforeUnmount(() => {
                confirm.close();
            });

            return { showPositioned, dialogKey };
        },
        template: `
      <div class="card">
        <BravoConfirmDialog :key="dialogKey" />
        <div class="flex flex-wrap gap-2">
          <BravoButton @click="showPositioned('left')" label="Left" />
          <BravoButton @click="showPositioned('right')" label="Right" />
          <BravoButton @click="showPositioned('top')" label="Top" />
          <BravoButton @click="showPositioned('bottom')" label="Bottom" />
          <BravoButton @click="showPositioned('topleft')" label="TopLeft" />
          <BravoButton @click="showPositioned('topright')" label="TopRight" />
          <BravoButton @click="showPositioned('bottomleft')" label="BottomLeft" />
          <BravoButton @click="showPositioned('bottomright')" label="BottomRight" />
        </div>
      </div>
    `,
    }),
};

// Custom template
export const CustomTemplate: Story = {
    render: (args) => ({
        components: { BravoConfirmDialog, BravoButton },
        setup() {
            const confirm = useConfirm();
            const dialogKey = ref(0);

            const showTemplate = () => {
                confirm.require({
                    message: 'Are you sure you want to proceed?',
                    header: 'Custom Confirmation',
                    icon: 'pi pi-exclamation-triangle',
                    acceptClass: 'p-button-danger',
                    rejectClass: 'p-button-secondary',
                    accept: () => {
                        console.log('Accepted');
                        confirm.close();
                    },
                    reject: () => {
                        console.log('Rejected');
                        confirm.close();
                    },
                });
            };

            onBeforeUnmount(() => {
                confirm.close();
            });

            return { showTemplate, dialogKey };
        },
        template: `
      <div class="card">
        <BravoConfirmDialog :key="dialogKey">
          <template #message="slotProps">
            <div class="flex flex-column align-items-center">
              <i :class="slotProps.message.icon" style="font-size: 3rem" />
              <p>{{ slotProps.message.message }}</p>
            </div>
          </template>
        </BravoConfirmDialog>
        <BravoButton @click="showTemplate" label="Custom Template" />
      </div>
    `,
    }),
};
