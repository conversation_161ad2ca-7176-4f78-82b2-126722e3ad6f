import { Meta, Typeset } from '@storybook/addon-docs/blocks';

<Meta title="Styles/Typography" />

export const primaryFont = 'Inter';

# Typography System

This page outlines the typography for the design system. We will add specific tokens that you can use for each of the typography styles.

Note that the primary text color should be used for all font except the following:
Underline: link color
Caption 1, Caption 2, Small Caption: Seconary Color

## Title Page

<Typeset fontSizes={[24]} fontWeight="600" sampleText="Used for Page Titles" fontFamily={primaryFont} />

## Large Title

<Typeset fontSizes={[20]} fontWeight="600" sampleText="Used for Modal Titles" fontFamily={primaryFont} />

## Title 1

<Typeset
    fontSizes={[18]}
    fontWeight="500"
    sampleText="Used for Card Titles and Alert Titles"
    fontFamily={primaryFont}
/>

## Title 2

<Typeset fontSizes={[16]} fontWeight="500" sampleText="Used for Page Titles" fontFamily={primaryFont} />

## Title 3

<Typeset fontSizes={[15]} fontWeight="500" sampleText="Used for Tabs" fontFamily={primaryFont} />

## Headline

<Typeset fontSizes={[13]} fontWeight="500" sampleText="USED FOR SECTION HEADINGS" fontFamily={primaryFont} />

## Large Body

<Typeset fontSizes={[15]} fontWeight="500" sampleText="Used for Body Text. Currently Unused" fontFamily={primaryFont} />

## Subhead

<Typeset
    fontSizes={[13]}
    fontWeight="500"
    sampleText="Used for Side Navigation, Buttons, Chips"
    fontFamily={primaryFont}
/>

## Body

<Typeset fontSizes={[14]} fontWeight="400" sampleText="Used for Body Text and Links" fontFamily={primaryFont} />

## Body Bold

<Typeset fontSizes={[14]} fontWeight="700" sampleText="Used for Color Picker" fontFamily={primaryFont} />

## Body Underline

<Typeset
    fontSizes={[14]}
    fontWeight="500"
    sampleText="Used for Links (Hover/Pressed)"
    style={{ textDecoration: 'underline' }}
    fontFamily={primaryFont}
/>

## Paragraph

<Typeset fontSizes={[14]} fontWeight="400" sampleText="Used for Paragraph Text" fontFamily={primaryFont} />

## Caption 1

<Typeset fontSizes={[13]} fontWeight="500" sampleText="Used for Number Bubble" fontFamily={primaryFont} />

## Caption 2

<Typeset fontSizes={[13]} fontWeight="400" sampleText="Used for Time Stamps and Dates" fontFamily={primaryFont} />

## Caption 3

<Typeset fontSizes={[12]} fontWeight="500" sampleText="USED FOR NAVIGATION AND CATEGORIES" fontFamily={primaryFont} />

## Small Caption

<Typeset fontSizes={[11]} fontWeight="400" sampleText="Used for Subtext and Error Message" fontFamily={primaryFont} />
