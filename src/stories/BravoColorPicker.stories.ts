import type { Meta, StoryObj } from '@storybook/vue3';
import BravoColorPicker from '../components/BravoColorPicker.vue';
import { ref } from 'vue';

const meta = {
    title: 'Form/ColorPicker',
    component: BravoColorPicker,
    tags: ['autodocs'],
    argTypes: {
        modelValue: {
            control: 'text',
            description: 'Value of the component',
        },
        defaultColor: {
            control: 'color',
            description: 'Initial color to display when value is not defined',
        },
        inline: {
            control: 'boolean',
            description: 'Whether to display as an overlay or not',
        },
        format: {
            control: 'select',
            options: ['hex', 'rgb', 'hsb'],
            description: 'Format to use in value binding',
        },
        disabled: {
            control: 'boolean',
            description: 'When present, it specifies that the component should be disabled',
        },
    },
} satisfies Meta<typeof BravoColorPicker>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoColorPicker v-model="color" />
</template>

<script setup>
import { ref } from 'vue';

const color = ref('#6466f1');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoColorPicker },
        setup() {
            const color = ref('#6466f1');
            return { color };
        },
        template: `<BravoColorPicker v-model="color" />`,
    }),
};

// Inline mode
export const Inline: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoColorPicker v-model="color" inline />
</template>

<script setup>
import { ref } from 'vue';

const color = ref('#6466f1');
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoColorPicker },
        setup() {
            const color = ref('#6466f1');
            return { color };
        },
        template: `<BravoColorPicker v-model="color" inline />`,
    }),
};

// Different formats
export const Formats: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-col gap-4">
    <div>
      <label>HEX</label>
      <BravoColorPicker v-model="colorHEX" format="hex" />
      <span class="ml-2">{{ colorHEX }}</span>
    </div>
    <div>
      <label>RGB</label>
      <BravoColorPicker v-model="colorRGB" format="rgb" />
      <span class="ml-2">{{ JSON.stringify(colorRGB) }}</span>
    </div>
    <div>
      <label>HSB</label>
      <BravoColorPicker v-model="colorHSB" format="hsb" />
      <span class="ml-2">{{ JSON.stringify(colorHSB) }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const colorHEX = ref('#6466f1');
const colorRGB = ref({ r: 100, g: 102, b: 241 });
const colorHSB = ref({ h: 239, s: 59, b: 95 });
</script>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoColorPicker },
        setup() {
            const colorHEX = ref('#6466f1');
            const colorRGB = ref({ r: 100, g: 102, b: 241 });
            const colorHSB = ref({ h: 239, s: 59, b: 95 });
            return { colorHEX, colorRGB, colorHSB };
        },
        template: `
      <div class="flex flex-col gap-4">
        <div>
          <label>HEX</label>
          <BravoColorPicker v-model="colorHEX" format="hex" />
          <span class="ml-2">{{ colorHEX }}</span>
        </div>
        <div>
          <label>RGB</label>
          <BravoColorPicker v-model="colorRGB" format="rgb" />
          <span class="ml-2">{{ JSON.stringify(colorRGB) }}</span>
        </div>
        <div>
          <label>HSB</label>
          <BravoColorPicker v-model="colorHSB" format="hsb" />
          <span class="ml-2">{{ JSON.stringify(colorHSB) }}</span>
        </div>
      </div>
    `,
    }),
};

// Disabled state
export const Disabled: Story = {
    render: (args) => ({
        components: { BravoColorPicker },
        setup() {
            const color = ref('#6466f1');

            return { color };
        },
        template: `
      <BravoColorPicker v-model="color" disabled />
    `,
    }),
};

// With default color
export const WithDefaultColor: Story = {
    render: (args) => ({
        components: { BravoColorPicker },
        setup() {
            const color = ref();

            return { color };
        },
        template: `
      <BravoColorPicker v-model="color" defaultColor="#ff0000" />
    `,
    }),
};

// Form validation
export const Invalid: Story = {
    render: (args) => ({
        components: { BravoColorPicker },
        setup() {
            const color = ref();

            return { color };
        },
        template: `
      <BravoColorPicker 
        v-model="color" 
        :invalid="!color"
        aria-label="Color picker"
      />
    `,
    }),
};
