import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import BravoFieldPanel, { type Field } from '../components/BravoFieldPanel.vue';
import BravoInputText from '../components/BravoInputText.vue';
import Bravo<PERSON>electField from '../components/BravoSelectField.vue';
import BravoMultiSelect from '../components/BravoMultiSelect.vue';

type City = {
    label: string;
    value: string;
};

const meta = {
    title: 'Components/BravoFieldPanel',
    component: BravoFieldPanel,
    tags: ['autodocs'],
} satisfies Meta<typeof BravoFieldPanel>;

export default meta;
type Story = StoryObj<typeof meta>;

// Sample data for select and multi-select
const cities: City[] = [
    { label: 'New York', value: 'NY' },
    { label: 'London', value: 'LDN' },
    { label: 'Paris', value: 'PRS' },
    { label: 'Tokyo', value: 'TK' },
];

export const Default: Story = {
    args: {
        fields: [],
    },
    render: () => ({
        components: {
            BravoFieldPanel,
            BravoInputText,
            BravoSelectField,
            BravoMultiSelect,
        },
        setup() {
            const name = ref('');
            const selectedCity = ref<string | null>(null);
            const selectedCities = ref<string[]>([]);

            const updateName = (val: string) => {
                name.value = val;
            };

            const updateCity = (val: string | null) => {
                selectedCity.value = val;
            };

            const updateCities = (val: string[]) => {
                selectedCities.value = val;
            };

            return {
                name,
                selectedCity,
                selectedCities,
                updateName,
                updateCity,
                updateCities,
                cities,
                fields: [
                    {
                        label: 'Name',
                        component: BravoInputText,
                        props: {
                            modelValue: name.value,
                            'onUpdate:modelValue': updateName,
                            placeholder: 'Enter your name',
                        },
                    },
                    {
                        label: 'City',
                        component: BravoSelectField,
                        props: {
                            modelValue: selectedCity.value,
                            'onUpdate:modelValue': updateCity,
                            options: cities,
                            optionLabel: 'label',
                            placeholder: 'Select a city',
                        },
                    },
                    {
                        label: 'Favorite Cities',
                        component: BravoMultiSelect,
                        props: {
                            modelValue: selectedCities.value,
                            'onUpdate:modelValue': updateCities,
                            options: cities,
                            placeholder: 'Select multiple cities',
                            filter: true,
                        },
                    },
                ] as Field[],
            };
        },
        template: `
      <div>
        <BravoFieldPanel :fields="fields" />
        <div class="mt-4 p-3 border rounded bg-gray-50">
          <h3 class="text-lg font-medium mb-2">Current Values:</h3>
          <p><strong>Name:</strong> {{ name || 'Not set' }}</p>
          <p><strong>City:</strong> {{ selectedCity || 'Not set' }}</p>
          <p><strong>Favorite Cities:</strong> {{ selectedCities.length ? selectedCities.join(', ') : 'Not set' }}</p>
        </div>
      </div>
    `,
    }),
};

export const WithViewMode: Story = {
    args: {
        fields: [],
    },
    render: () => ({
        components: {
            BravoFieldPanel,
            BravoInputText,
            BravoSelectField,
            BravoMultiSelect,
        },
        setup() {
            const name = ref('');
            const selectedCity = ref<string | null>(null);
            const selectedCities = ref<string[]>([]);

            const updateName = (val: string) => {
                name.value = val;
            };

            const updateCity = (val: string | null) => {
                selectedCity.value = val;
            };

            const updateCities = (val: string[]) => {
                selectedCities.value = val;
            };

            return {
                name,
                selectedCity,
                selectedCities,
                updateName,
                updateCity,
                updateCities,
                cities,
                fields: [
                    {
                        label: 'Name',
                        component: BravoInputText,
                        props: {
                            modelValue: name.value,
                            'onUpdate:modelValue': updateName,
                            placeholder: 'Enter your name',
                            view: true,
                        },
                    },
                    {
                        label: 'City',
                        component: BravoSelectField,
                        props: {
                            modelValue: selectedCity.value,
                            'onUpdate:modelValue': updateCity,
                            options: cities,
                            optionLabel: 'label',
                            placeholder: 'Select a city',
                            view: true,
                        },
                    },
                    {
                        label: 'Favorite Cities',
                        component: BravoMultiSelect,
                        props: {
                            modelValue: selectedCities.value,
                            'onUpdate:modelValue': updateCities,
                            options: cities,
                            placeholder: 'Select multiple cities',
                            filter: true,
                            view: true,
                        },
                    },
                ] as Field[],
            };
        },
        template: `
      <div>
        <BravoFieldPanel :fields="fields" />
        <div class="mt-4 p-3 border rounded bg-gray-50">
          <h3 class="text-lg font-medium mb-2">Current Values:</h3>
          <p><strong>Name:</strong> {{ name || 'Not set' }}</p>
          <p><strong>City:</strong> {{ selectedCity || 'Not set' }}</p>
          <p><strong>Favorite Cities:</strong> {{ selectedCities.length ? selectedCities.join(', ') : 'Not set' }}</p>
        </div>
        <div class="mt-2 text-sm text-surface-600">
          Try clicking on a field to edit it, then click away to see the view state.
        </div>
      </div>
    `,
    }),
};
