import type { Meta, StoryObj } from '@storybook/vue3';
import BravoMultiSelect from '../components/BravoMultiSelect.vue';

// Sample data
const cities = [
    { name: 'New York', code: 'NY' },
    { name: 'Rome', code: 'RM' },
    { name: 'London', code: 'LDN' },
    { name: 'Istanbul', code: 'IST' },
    { name: 'Paris', code: 'PRS' },
    { name: 'Madrid', code: 'MAD' },
    { name: 'Chicago', code: 'CHI' },
];

const groupedCities = [
    {
        label: 'USA',
        code: 'US',
        items: [
            { label: 'New York', value: 'NY' },
            { label: 'Chicago', value: 'CHI' },
            { label: 'San Francisco', value: 'SF' },
        ],
    },
    {
        label: 'Europe',
        code: 'EU',
        items: [
            { label: 'London', value: 'LDN' },
            { label: 'Paris', value: 'PRS' },
            { label: 'Rome', value: 'RM' },
            { label: 'Madrid', value: 'MAD' },
        ],
    },
];

const meta = {
    title: 'Form/MultiSelect',
    component: BravoMultiSelect,
    tags: ['autodocs'],
} satisfies Meta<typeof BravoMultiSelect>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  < BravoMultiSelect v-model="selectedCities" :options="cities" optionLabel="name" filter placeholder="Select Cities" />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoMultiSelect },
        setup() {
            return { cities };
        },
        template: `<BravoMultiSelect :options="cities" optionLabel="name" filter placeholder="Select Cities" />`,
    }),
};

// With Filter
export const WithFilter: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoMultiSelect v-model="selectedCities" :options="cities" optionLabel="name" filter placeholder="Select Cities" :maxSelectedLabels="3" />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoMultiSelect },
        setup() {
            return { cities };
        },
        template: `<BravoMultiSelect :options="cities" optionLabel="name" filter placeholder="Select Cities" :maxSelectedLabels="3" />`,
    }),
};

// Chip Display
export const ChipDisplay: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoMultiSelect v-model="selectedCities" display="chip" :options="cities" optionLabel="name" filter placeholder="Select Cities" :maxSelectedLabels="3" />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoMultiSelect },
        setup() {
            return { cities };
        },
        template: `<BravoMultiSelect display="chip" :options="cities" optionLabel="name" filter placeholder="Select Cities" :maxSelectedLabels="3" />`,
    }),
};

// Grouped Options
export const GroupedOptions: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoMultiSelect v-model="selectedCities" :options="groupedCities" optionLabel="label" filter optionGroupLabel="label" optionGroupChildren="items" display="chip" placeholder="Select Cities" />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoMultiSelect },
        setup() {
            return { groupedCities };
        },
        template: `<BravoMultiSelect :options="groupedCities" optionLabel="label" filter optionGroupLabel="label" optionGroupChildren="items" display="chip" placeholder="Select Cities" />`,
    }),
};

// Filled Variant
export const FilledVariant: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoMultiSelect v-model="selectedCities" variant="filled" :options="cities" optionLabel="name" filter placeholder="Select Cities" :maxSelectedLabels="3" />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoMultiSelect },
        setup() {
            return { cities };
        },
        template: `<BravoMultiSelect variant="filled" :options="cities" optionLabel="name" filter placeholder="Select Cities" :maxSelectedLabels="3" />`,
    }),
};

// Loading State
export const LoadingState: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoMultiSelect placeholder="Loading..." loading />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoMultiSelect },
        template: `<BravoMultiSelect placeholder="Loading..." :loading="true" />`,
    }),
};

// Disabled State
export const DisabledState: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoMultiSelect v-model="selectedCities" :options="cities" optionLabel="name" placeholder="Select Cities" disabled />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoMultiSelect },
        setup() {
            return { cities };
        },
        template: `<BravoMultiSelect :options="cities" optionLabel="name" placeholder="Select Cities" :disabled="true" />`,
    }),
};

// With Clear Button
export const WithClearButton: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoMultiSelect v-model="selectedCities" :options="cities" optionLabel="name" filter placeholder="Select Cities" showClear />
</template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoMultiSelect },
        setup() {
            return { cities };
        },
        template: `<BravoMultiSelect :options="cities" optionLabel="name" filter placeholder="Select Cities" :showClear="true" />`,
    }),
};
