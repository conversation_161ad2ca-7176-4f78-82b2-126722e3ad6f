import { Meta, StoryObj } from '@storybook/vue3';
import { CxmLoaderCustomerSummary } from '../components';

type BaseLoaderCustomerSummaryProps = {
    isLoading?: boolean;
};

const defaultLoaderCustomerSummary: BaseLoaderCustomerSummaryProps = {
    isLoading: true,
};

export default {
    component: CxmLoaderCustomerSummary,
    title: 'Legacy & Custom/Loaders/LoaderCustomerSummary',
    tags: ['autodocs'],
    parameters: {
        docs: {
            description: {
                component: `LoaderCustomerSummary is a skeleton loader for the Customer Summary. It is static today but will be customizable in the future.`,
            },
        },
        layout: 'fullscreen',
    },
} as Meta<typeof CxmLoaderCustomerSummary>;

type Story = StoryObj<typeof CxmLoaderCustomerSummary>;

export const Default: Story = {
    args: { isLoading: defaultLoaderCustomerSummary.isLoading },
};
