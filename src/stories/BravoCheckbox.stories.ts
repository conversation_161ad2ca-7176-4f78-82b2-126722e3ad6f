import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3';
import BravoCheckbox from '../components/BravoCheckbox.vue';
import { ref } from 'vue';

// Define the props interface for Checkbox component
interface CheckboxProps {
    modelValue?: any;
    value?: any;
    binary?: boolean;
    disabled?: boolean;
    invalid?: boolean;
    variant?: 'outlined' | 'filled';
    size?: 'small' | 'large';
    indeterminate?: boolean;
    inputId?: string;
    inputClass?: string;
    inputStyle?: object;
    'aria-label'?: string;
    'aria-labelledby'?: string;
}

const meta = {
    title: 'Form/Checkbox',
    component: BravoCheckbox,
    tags: ['autodocs'],
    argTypes: {
        modelValue: { control: 'text' },
        value: { control: 'text' },
        binary: { control: 'boolean' },
        disabled: { control: 'boolean' },
        invalid: { control: 'boolean' },
        variant: {
            control: 'select',
            options: ['outlined', 'filled'],
        },
        size: {
            control: 'select',
            options: ['small', undefined, 'large'],
        },
        indeterminate: { control: 'boolean' },
        inputId: { control: 'text' },
        withLabel: { control: 'boolean' },
        forElement: { control: 'text' },
        isRequired: { control: 'boolean' },
        toolTipText: { control: 'text' },
        toolTipPosition: { control: 'select', options: ['top', 'bottom', 'left', 'right'] },
        dataTestId: { control: 'text' },
        id: { control: 'text' },
        label: { control: 'text' },
        inputClass: { control: 'text' },
        inputStyle: { control: 'object' },
        'aria-label': { control: 'text' },
        'aria-labelledby': { control: 'text' },
    },
} satisfies Meta<typeof BravoCheckbox>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story with binary checkbox
export const Basic: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <Checkbox v-model="checked" binary />
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoCheckbox },
        setup() {
            const checked = ref(false);
            return { checked };
        },
        template: '<BravoCheckbox v-model="checked" binary />',
    }),
};

// Group checkboxes story
export const Group: Story = {
    parameters: {
        docs: {
            source: {
                code: `
    <template>
    <div class="flex flex-wrap gap-4">
      <BravoCheckbox v-model="checked" :withLabel="true" label="Cheese" binary id="group-checkbox-1" dataTestId="group-checkbox-1" forElement="cheese" />
      <BravoCheckbox v-model="checked" :withLabel="true" label="Mushroom" binary id="group-checkbox-2" dataTestId="group-checkbox-2" forElement="mushroom" />
      <BravoCheckbox v-model="checked" :withLabel="true" label="Pepper" binary id="group-checkbox-3" dataTestId="group-checkbox-3" forElement="pepper" />
    </div>
    </template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoCheckbox },
        setup() {
            const selectedItems = ref([]);
            const withLabel = ref(true);
            return { selectedItems, withLabel };
        },
        template: `
    <div class="flex flex-wrap gap-4">
      <BravoCheckbox v-model="checked" :withLabel="withLabel" label="Cheese" binary id="group-checkbox-1" dataTestId="group-checkbox-1" forElement="cheese"/>
      <BravoCheckbox v-model="checked" :withLabel="withLabel" label="Mushroom" binary id="group-checkbox-2" dataTestId="group-checkbox-2" forElement="mushroom" />
      <BravoCheckbox v-model="checked" :withLabel="withLabel" label="Pepper" binary id="group-checkbox-3" dataTestId="group-checkbox-3" forElement="pepper"/>
    </div>
    `,
    }),
};

// Sizes story
export const Sizes: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex flex-wrap gap-4">
    <BravoCheckbox v-model="checked" size="small" binary />
    <BravoCheckbox v-model="checked" binary />
    <BravoCheckbox v-model="checked" size="large" binary />
  </div>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoCheckbox },
        setup() {
            const checked = ref(false);
            return { checked };
        },
        template: `
      <div class="flex flex-wrap gap-4">
        <BravoCheckbox v-model="checked" size="small" binary />
        <BravoCheckbox v-model="checked" binary />
        <BravoCheckbox v-model="checked" size="large" binary />
      </div>
    `,
    }),
};

// Invalid state story
export const Invalid: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <BravoCheckbox v-model="checked" :invalid="!checked" binary />
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoCheckbox },
        setup() {
            const checked = ref(false);
            return { checked };
        },
        template: '<BravoCheckbox v-model="checked" :invalid="!checked" binary />',
    }),
};

export const WithLabel: Story = {
    args: {
        withLabel: true,
        label: 'Label',
        id: 'checkbox-label',
        dataTestId: 'checkbox-testid',
        forElement: 'checkbox-label',
    },
    parameters: {
        docs: {
            source: {
                code: `
    <template>
      <BravoCheckbox v-model="checked" :withLabel="args.withLabel" :label="args.label" binary :id="args.id" :dataTestId="args.dataTestId" :forElement="args.forElement" />
    </template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoCheckbox },
        setup() {
            const checked = ref(false);
            const withLabel = ref(true);
            const label = 'Label';
            const id = 'checkbox-label';
            const dataTestId = 'checkbox-testid';

            return { checked, withLabel, label, id, dataTestId };
        },
        template:
            '<BravoCheckbox v-model="checked" :withLabel="withLabel" :label="label" binary :id="id" :dataTestId="dataTestId" forElement="Label"/>',
    }),
};

export const WithLabelAndToolTip: Story = {
    args: {
        withLabel: true,
        label: 'Label',
        isRequired: false,
        toolTipText: 'Tooltip text',
        toolTipPosition: 'top',
        id: 'checkbox-label',
        iconName: 'info',
        dataTestId: 'checkbox-testid',
        forElement: 'checkbox-label',
    },
    parameters: {
        docs: {
            source: {
                code: `
    <template>
      <BravoCheckbox v-model="checked" :withLabel="args.withLabel" :label="args.label" binary :id="args.id" :dataTestId="args.dataTestId" :forElement="args.forElement" :toolTipText="args.toolTipText" :toolTipPosition="args.toolTipPosition"/>
    </template>
`,
            },
        },
    },
    render: (args) => ({
        components: { BravoCheckbox },
        setup() {
            return { args };
        },
        template: '<BravoCheckbox v-bind="args"/>',
    }),
};

// Disabled state story
export const Disabled: Story = {
    parameters: {
        docs: {
            source: {
                code: `
<template>
  <div class="flex gap-4">
    <BravoCheckbox v-model="checked1" binary disabled />
    <BravoCheckbox v-model="checked2" binary disabled />
  </div>
</template>
`,
            },
        },
    },
    render: () => ({
        components: { BravoCheckbox },
        setup() {
            const checked1 = ref(true);
            const checked2 = ref(false);
            return { checked1, checked2 };
        },
        template: `
      <div class="flex gap-4">
        <BravoCheckbox v-model="checked1" binary disabled />
        <BravoCheckbox v-model="checked2" binary disabled />
      </div>
    `,
    }),
};
