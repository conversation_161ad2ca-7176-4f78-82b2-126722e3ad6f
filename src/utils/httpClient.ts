import { v4 as uuidv4 } from 'uuid';

interface LoginCredentials {
    email: string;
    password: string;
}

interface LoginResponse {
    success: boolean;
    message?: string;
    csrf_token?: string;
}

interface RequestOptions {
    params?: Record<string, string>;
    body?: any;
    headers?: Record<string, string>;
}

class HttpClient {
    private baseUrl: string;
    private clientInstanceId: string;
    private csrfToken: string = '';
    private userEmail: string = '';
    private refreshInterval: number | null = null;
    private onAuthChange?: (isAuthenticated: boolean) => void;
    private initialized: boolean = false;
    private initializing: Promise<void> | null = null;
    private userStatusData: any = null;
    private userStatusPromise: Promise<any> | null = null;

    constructor(baseUrl: string, clientInstanceId: string, onAuthChange?: (isAuthenticated: boolean) => void) {
        this.baseUrl = baseUrl;
        this.clientInstanceId = clientInstanceId;
        this.onAuthChange = onAuthChange;
    }

    private getCommonHeaders(): Record<string, string> {
        const headers: Record<string, string> = {
            'x-boomtown-client-instance-id': this.clientInstanceId,
            'x-request-id': uuidv4(),
            'content-type': 'application/json',
        };

        if (this.csrfToken) {
            headers['x-boomtown-csrf-token'] = this.csrfToken;
        }

        return headers;
    }

    private hasRelayCookie(): boolean {
        return document.cookie.split(';').some((cookie) => cookie.trim().startsWith('relay='));
    }

    async initialize(): Promise<void> {
        // If already initialized or initializing, return existing promise
        if (this.initialized) return Promise.resolve();
        if (this.initializing) return this.initializing;

        this.initializing = new Promise<void>(async (resolve, reject) => {
            try {
                // Always try to get user status on initialize
                // This will tell us if we're authenticated
                console.debug('Checking authentication status...');
                await this.refreshCsrfToken();

                if (this.csrfToken) {
                    console.debug('Successfully got CSRF token, user is authenticated');
                    this.startTokenRefresh();
                    this.onAuthChange?.(true);
                } else {
                    console.debug('No CSRF token, user is not authenticated');
                    this.onAuthChange?.(false);
                }

                this.initialized = true;
                resolve();
            } catch (error) {
                console.debug('Not authenticated:', error);
                this.onAuthChange?.(false);
                this.initialized = true;
                resolve(); // Resolve anyway to allow the app to continue
            } finally {
                this.initializing = null;
            }
        });

        return this.initializing;
    }

    private async refreshCsrfToken(): Promise<void> {
        try {
            console.debug('Fetching user status...');

            // If we already have a pending userStatus request, reuse it
            if (this.userStatusPromise) {
                console.debug('Reusing existing userStatus request');
                const data = await this.userStatusPromise;
                return;
            }

            // Create a new promise for the userStatus request
            this.userStatusPromise = (async () => {
                const response = await fetch(`${this.baseUrl}/api/core/?sAction=userStatus`, {
                    method: 'GET',
                    headers: this.getCommonHeaders(),
                    credentials: 'include',
                    mode: 'cors',
                });

                if (!response.ok) {
                    throw new Error(`Failed to refresh token: ${response.status}`);
                }

                const data = await response.json();
                console.debug('User status response:', data);
                this.userStatusData = data;

                if (data.csrf_token) {
                    this.csrfToken = data.csrf_token;
                    console.debug('CSRF token refreshed');

                    // Set the email from the userStatus response
                    if (data.users?.email) {
                        this.userEmail = data.users.email;
                        console.debug('User email set:', this.userEmail);
                    }
                } else {
                    throw new Error('No CSRF token in response');
                }

                return data;
            })();

            // Wait for the promise to resolve
            await this.userStatusPromise;

            // Clear the promise reference after a short delay to allow other callers to use it
            setTimeout(() => {
                this.userStatusPromise = null;
            }, 100);
        } catch (error) {
            console.error('Token refresh failed:', error);
            this.csrfToken = '';
            this.userEmail = '';
            this.userStatusPromise = null;
            throw error;
        }
    }

    private startTokenRefresh(): void {
        this.stopTokenRefresh(); // Clear any existing interval
        this.refreshInterval = window.setInterval(() => {
            this.refreshCsrfToken().catch(console.error);
        }, 60 * 1000); // Refresh every minute
    }

    private stopTokenRefresh(): void {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    async login(credentials: LoginCredentials): Promise<LoginResponse> {
        const formData = new URLSearchParams({
            email: credentials.email,
            password: credentials.password,
        });

        const response = await fetch(`${this.baseUrl}/api/core/?sAction=userLogin`, {
            method: 'POST',
            headers: {
                'x-boomtown-client-instance-id': this.clientInstanceId,
                'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            },
            body: formData,
            credentials: 'include',
            mode: 'cors',
        });

        if (!response.ok) {
            throw new Error('Login request failed');
        }

        const data = (await response.json()) as LoginResponse;

        if (data.csrf_token) {
            console.log('CSRF token:', data.csrf_token);
            this.csrfToken = data.csrf_token;
        }

        if (data.success) {
            this.startTokenRefresh();
            this.onAuthChange?.(true);
        }

        return data;
    }

    async logout(): Promise<void> {
        try {
            console.log('HttpClient: logging out');
            // Call logout endpoint to clear server session
            await fetch(`${this.baseUrl}/api/core/?sAction=userLogout`, {
                method: 'GET', // Changed to GET since it's a sAction parameter
                headers: this.getCommonHeaders(),
                credentials: 'include',
                mode: 'cors',
            });
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            // Clear client-side state regardless of server response
            this.csrfToken = '';
            this.userEmail = '';
            this.userStatusData = null; // Clear cached user status
            this.userStatusPromise = null; // Clear any pending requests
            this.initialized = false; // Reset initialization state
            this.stopTokenRefresh();
            this.onAuthChange?.(false);

            // Clear all cookies in the boomtown domain
            document.cookie.split(';').forEach((cookie) => {
                const [name] = cookie.split('=');
                document.cookie = `${name.trim()}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=.boomtown.com`;
                document.cookie = `${name.trim()}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
            });

            console.log('HttpClient: logout complete, auth state cleared');
        }
    }

    private async request<T>(method: string, path: string, options: RequestOptions = {}): Promise<T> {
        // Ensure client is initialized before making requests
        // Skip initialization for login request to avoid loops
        if (!path.includes('userLogin')) {
            await this.initialize();
        }

        // Build the query string if we have params
        let queryString = '';
        if (options.params) {
            const searchParams = new URLSearchParams();
            Object.entries(options.params).forEach(([key, value]) => {
                searchParams.append(key, value);
            });
            queryString = `?${searchParams.toString()}`;
        }

        // Combine base URL (if relative path) or use absolute path
        const fullUrl = path.startsWith('http')
            ? `${path}${queryString}`
            : `${this.baseUrl}/${path.replace(/^\//, '')}${queryString}`;

        const response = await fetch(fullUrl, {
            method,
            headers: {
                ...this.getCommonHeaders(),
                ...options.headers,
            },
            body: options.body ? JSON.stringify(options.body) : undefined,
            credentials: 'include',
            mode: 'cors',
        });

        if (!response.ok) {
            if (response.status === 401) {
                this.logout();
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.json();
    }

    async get<T>(path: string, params?: Record<string, string>): Promise<T> {
        return this.request<T>('GET', path, { params });
    }

    async post<T>(path: string, body: any, params?: Record<string, string>): Promise<T> {
        return this.request<T>('POST', path, { body, params });
    }

    async put<T>(path: string, body: any, params?: Record<string, string>): Promise<T> {
        return this.request<T>('PUT', path, { body, params });
    }

    async patch<T>(path: string, body: any, params?: Record<string, string>): Promise<T> {
        return this.request<T>('PATCH', path, { body, params });
    }

    async delete<T>(path: string, params?: Record<string, string>): Promise<T> {
        return this.request<T>('DELETE', path, { params });
    }

    // Getter for checking if we have a CSRF token (and thus are probably authenticated)
    get isAuthenticated(): boolean {
        return Boolean(this.csrfToken);
    }

    // Simplified getter for the client instance ID
    get instanceId(): string {
        return this.clientInstanceId;
    }

    // Add isInitialized getter
    get isInitialized(): boolean {
        return this.initialized;
    }

    // Add getter for email
    get email(): string {
        return this.userEmail;
    }

    async getUserStatus(): Promise<any> {
        // If we have cached data and it's still valid, return it
        if (this.userStatusData) {
            return this.userStatusData;
        }

        // Otherwise refresh the token which will update the cache
        await this.refreshCsrfToken();
        return this.userStatusData;
    }
}

// Export the class for creating instances
export { HttpClient };
