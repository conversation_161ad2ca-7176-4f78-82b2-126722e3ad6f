import { DateTime } from 'luxon';

/**
 * Formats a timestamp (ISO string or unix timestamp) relative to the current time
 * Adds a (time since) or (time until) suffix based on the time difference
 *
 * @param timestamp - ISO string or unix timestamp
 * @returns Formatted timestamp string
 */
export const formatRelativeTimestamp = (timestamp: string | number) => {
    if (!timestamp) return '';

    let formattedDiff: string;
    let datetimeFormat = 'h:mm a';
    const start = DateTime.fromJSDate(new Date(timestamp));
    const end = DateTime.now();

    const minutesDiffObj = end.diff(start, 'minutes').toObject();
    const diffInMinutes = minutesDiffObj.minutes || 0;
    const diffInHours = diffInMinutes / 60;
    const diffInDays = diffInMinutes / 60 / 24;

    if (Math.abs(diffInMinutes) <= 60) {
        formattedDiff = Math.round(diffInMinutes) + 'm';
    } else if (Math.abs(diffInHours) <= 24) {
        formattedDiff = Math.round(diffInHours) + 'h';
    } else if (Math.abs(diffInDays) <= 30) {
        formattedDiff = Math.round(diffInDays) + 'd';
        datetimeFormat = 'MMM d, h:mm a';
    } else {
        formattedDiff = '';
        datetimeFormat = 'MMM d, y, h:mm a';
    }

    const formattedTimestamp = start.toFormat(datetimeFormat);
    return formattedDiff
        ? formattedTimestamp +
              (diffInMinutes >= 0 ? ` (${formattedDiff} ago)` : ` (in ${formattedDiff.replace('-', '')})`)
        : formattedTimestamp;
};
