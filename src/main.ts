import { createApp } from 'vue';
import './style.css';
import App from './App.vue';
import PrimeVue from 'primevue/config';
import ToastService from 'primevue/toastservice';
import ConfirmationService from 'primevue/confirmationservice';
import Ripple from 'primevue/ripple';
import Tooltip from 'primevue/tooltip';
import MyAuraPreset from './themes/theme-v4';

// Import PrimeVue styles
import './themes/global.css';
import './themes/primevue-variables.css';
import 'primeicons/primeicons.css';
import 'quill/dist/quill.core.css';
import 'quill/dist/quill.snow.css';

const app = createApp(App);

app.use(PrimeVue, {
    theme: {
        preset: MyAuraPreset,
        options: {
            prefix: 'p',
            darkModeSelector: 'system',
            cssLayer: false,
        },
    },
    ripple: true,
    unstyled: false,
    pt: {},
});

app.use(ToastService);
app.use(ConfirmationService);
app.directive('ripple', Ripple);
app.directive('tooltip', Tooltip);

app.mount('#app');
