/* Import Inter font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer bravo-base {
    @tailwind base;
}

/* Set Inter as the default font for the entire application */
html,
body {
    font-family:
        'Inter',
        system-ui,
        -apple-system,
        BlinkMacSystemFont,
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

html {
    font-size: 14px !important;
    line-height: 1.5rem !important;
}

radio,
select,
button,
input {
  -webkit-appearance: none;
  appearance: none;
  line-height: 1.5rem !important;
}

/* Global Scrollbar Styles */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--surface-300) var(--surface-0);
}

/* Webkit browsers (Chrome, Safari, Edge) */
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: var(--surface-0);
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb {
  background-color: var(--surface-300);
  border-radius: 8px;
  border: 1px solid var(--surface-0);
}

*::-webkit-scrollbar-thumb:hover {
  background-color: var(--surface-400);
}

*::-webkit-scrollbar-corner {
  background: var(--surface-0);
}

/* For horizontal scrollbars */
*::-webkit-scrollbar:horizontal {
  height: 8px;
}

@layer bravo-base {
    @tailwind components;
    @tailwind utilities;
}
