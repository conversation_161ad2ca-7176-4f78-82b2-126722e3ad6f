@layer bravo-variables;

:root {
    /* Border Radius */
    --border-radius-none: 0;
    --border-radius-xs: 2px;
    --border-radius-sm: 4px;
    --border-radius-md: 6px;
    --border-radius-lg: 8px;
    --border-radius-xl: 12px;
    --border-radius-pill: 500px;

    /* Core Semantic Variables */
    --transition-duration: 0.2s;
    --disabled-opacity: 0.6;
    --icon-size: 1rem;
    --anchor-gutter: 2px;

    /* Focus Ring */
    --focus-ring-width: 0;
    --focus-ring-style: none;
    --focus-ring-color: transparent;
    --focus-ring-offset: 0;

    /* Form Fields */
    --form-field-padding-x: 0.75rem;
    --form-field-padding-y: 0.5rem;
    --select-field-padding-x: 0.75rem;
    --select-field-padding-y: 0.5rem;
    --form-field-sm-font-size: 0.875rem;
    --form-field-sm-padding-x: 0.625rem;
    --form-field-sm-padding-y: 0.5rem;
    --form-field-lg-font-size: 1.125rem;
    --form-field-lg-padding-x: 0.875rem;
    --form-field-lg-padding-y: 0.75rem;

    /* Lists and Navigation */
    --list-padding: 0.5rem 0;
    --list-gap: 0;
    --list-header-padding: 0.625rem 1rem 0 1rem;
    --list-option-padding: 0.625rem 1rem;
    --list-option-border-radius: 0;
    --list-option-group-padding: 0.625rem 1rem;
    --list-option-group-font-weight: 600;

    /* Navigation */
    --navigation-list-padding: 0.5rem 0;
    --navigation-list-gap: 0;
    --navigation-item-padding: 0.625rem 1rem;
    --navigation-item-border-radius: 0;
    --navigation-item-gap: 0.5rem;
    --navigation-submenu-label-padding: 0.625rem 1rem;
    --navigation-submenu-label-font-weight: 600;
    --navigation-submenu-icon-size: 0.875rem;

    /* Overlays */
    --overlay-select-border-radius: var(--border-radius-lg);
    --overlay-select-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    --overlay-popover-border-radius: var(--border-radius-lg);
    --overlay-popover-padding: 1rem;
    --overlay-popover-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --overlay-modal-border-radius: var(--border-radius-xl);
    --overlay-modal-padding: 1.5rem;
    --overlay-modal-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    --overlay-navigation-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    --text-color-primary: var(--surface-850);
    --text-color-secondary: var(--surface-600);
    --icon-color-primary: var(--surface-600);
    --icon-color-secondary: var(--surface-850);
    --border-color: var(--surface-100);
   

    --primary-50: var(--blue-50);
    --primary-100: var(--blue-100);
    --primary-200: var(--blue-200);
    --primary-300: var(--blue-300);
    --primary-400: var(--blue-400);
    --primary-500: var(--blue-500);
    --primary-550: var(--blue-550);
    --primary-600: var(--blue-600);
    --primary-650: var(--blue-650);
    --primary-700: var(--blue-700);
    --primary-800: var(--blue-800);
    --primary-900: var(--blue-900);
    --primary-950: var(--blue-950);

    /* Emerald Colors */
    --emerald-50: #ecfdf5;
    --emerald-100: #d1fae5;
    --emerald-200: #a7f3d0;
    --emerald-300: #6ee7b7;
    --emerald-400: #34d399;
    --emerald-500: #10b981;
    --emerald-600: #059669;
    --emerald-700: #047857;
    --emerald-800: #065f46;
    --emerald-900: #064e3b;
    --emerald-950: #022c22;

    /* Green Colors */
    --green-50: #ecfaf0;
    --green-100: #d8f4e0;
    --green-200: #b2e9c1;
    --green-300: #8bdea3;
    --green-400: #65d384;
    --green-500: #3ec865;
    --green-600: #32a051;
    --green-700: #25783b;
    --green-800: #195028;
    --green-900: #0c2814;
    --green-950: #06140a;
    /* Red Colors */
    --red-50: #fbe9eb;
    --red-100: #f7d4d7;
    --red-200: #efa8af;
    --red-300: #e77d88;
    --red-400: #df5160;
    --red-500: #d72638;
    --red-600: #ac1e2d;
    --red-700: #811722;
    --red-800: #560f16;
    --red-900: #2b080b;
    --red-950: #150407;

    /* Blue Colors */
    --blue-50: #e6f3ff;
    --blue-100: #cce7ff;
    --blue-200: #99ceff;
    --blue-300: #66b6ff;
    --blue-400: #339dff;
    --blue-500: #0085ff;
    --blue-550: #0078e6;
    --blue-600: #006acc;
    --blue-650: #005db3;
    --blue-700: #005099;
    --blue-800: #003566;
    --blue-900: #001b33;
    --blue-950: #000d19;

    /* Blue/Gray Colors */
    --bluegray-50: #f4f9fd;
    --bluegray-100: #e9f2fb;
    --bluegray-150: #deecf9;
    --bluegray-200: #d3e6f7;
    --bluegray-300: #bed9f3;
    --bluegray-400: #a8cdef;
    --bluegray-500: #92c0eb;
    --bluegray-600: #759abc;
    --bluegray-700: #58738d;
    --bluegray-800: #3a4d5e;
    --bluegray-900: #1d262f;
    --bluegray-950: #0d141a;

    /* Orange Colors */
    --orange-50: #fef1eb;
    --orange-100: #fde3d7;
    --orange-200: #fbc8af;
    --orange-300: #f8ac86;
    --orange-400: #f6915d;
    --orange-500: #f47536;
    --orange-600: #c35d2b;
    --orange-700: #924620;
    --orange-800: #622e17;
    --orange-900: #31180b;
    --orange-950: #150407;

    /* Purple Colors */
    --purple-50: #f0f0f9;
    --purple-100: #e2e2f4;
    --purple-200: #c5c4e8;
    --purple-300: #a7a7dd;
    --purple-400: #8a89d1;
    --purple-500: #6d6cc6;
    --purple-600: #57569e;
    --purple-700: #414177;
    --purple-800: #2c2b4f;
    --purple-900: #161628;
    --purple-950: #0d0d14;

    /* Gray Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --gray-950: #030712;

    /* Slate Colors */
    --slate-50: #f8fafc;
    --slate-100: #f1f5f9;
    --slate-200: #e2e8f0;
    --slate-300: #cbd5e1;
    --slate-400: #94a3b8;
    --slate-500: #64748b;
    --slate-600: #475569;
    --slate-700: #334155;
    --slate-800: #1e293b;
    --slate-900: #0f172a;
    --slate-950: #020617;

    /* Zinc Colors */
    --zinc-50: #fafafa;
    --zinc-100: #f4f4f5;
    --zinc-200: #e4e4e7;
    --zinc-300: #d4d4d8;
    --zinc-400: #a1a1aa;
    --zinc-500: #71717a;
    --zinc-600: #52525b;
    --zinc-700: #3f3f46;
    --zinc-800: #27272a;
    --zinc-900: #18181b;
    --zinc-950: #09090b;

    /* Neutral Colors */
    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #e5e5e5;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #171717;
    --neutral-950: #0a0a0a;

    /* Cyan Colors */
    --cyan-50: #ecfeff;
    --cyan-100: #cffafe;
    --cyan-200: #a5f3fc;
    --cyan-300: #67e8f9;
    --cyan-400: #22d3ee;
    --cyan-500: #06b6d4;
    --cyan-600: #0891b2;
    --cyan-700: #0e7490;
    --cyan-800: #155e75;
    --cyan-900: #164e63;
    --cyan-950: #083344;

    /* Indigo Colors */
    --indigo-50: #eef2ff;
    --indigo-100: #e0e7ff;
    --indigo-200: #c7d2fe;
    --indigo-300: #a5b4fc;
    --indigo-400: #818cf8;
    --indigo-500: #6366f1;
    --indigo-600: #4f46e5;
    --indigo-700: #4338ca;
    --indigo-800: #3730a3;
    --indigo-900: #312e81;
    --indigo-950: #1e1b4b;

    /* Pink Colors */
    --pink-50: #fdf2f8;
    --pink-100: #fce7f3;
    --pink-200: #fbcfe8;
    --pink-300: #f9a8d4;
    --pink-400: #f472b6;
    --pink-500: #ec4899;
    --pink-600: #db2777;
    --pink-700: #be185d;
    --pink-800: #9d174d;
    --pink-900: #831843;
    --pink-950: #500724;

    /* Rose Colors */
    --rose-50: #fff1f2;
    --rose-100: #ffe4e6;
    --rose-200: #fecdd3;
    --rose-300: #fda4af;
    --rose-400: #fb7185;
    --rose-500: #f43f5e;
    --rose-600: #e11d48;
    --rose-700: #be123c;
    --rose-800: #9f1239;
    --rose-900: #881337;
    --rose-950: #4c0519;

    /* Yellow Colors */
    --yellow-50: #fef6e6;
    --yellow-100: #fceccc;
    --yellow-200: #f9da9a;
    --yellow-300: #f6c767;
    --yellow-400: #f3b535;
    --yellow-500: #f0a202;
    --yellow-600: #c08202;
    --yellow-700: #906101;
    --yellow-800: #604101;
    --yellow-900: #302000;
    --yellow-950: #150407;

    /* Amber Colors */
    --amber-50: #fffbeb;
    --amber-100: #fef3c7;
    --amber-200: #fde68a;
    --amber-300: #fcd34d;
    --amber-400: #fbbf24;
    --amber-500: #f59e0b;
    --amber-600: #d97706;
    --amber-700: #b45309;
    --amber-800: #92400e;
    --amber-900: #78350f;
    --amber-950: #451a03;

    /* Teal Colors */
    --teal-50: #f0fdfa;
    --teal-100: #ccfbf1;
    --teal-200: #99f6e4;
    --teal-300: #5eead4;
    --teal-400: #2dd4bf;
    --teal-500: #14b8a6;
    --teal-600: #0d9488;
    --teal-700: #0f766e;
    --teal-800: #115e59;
    --teal-900: #134e4a;
    --teal-950: #042f2e;

    /* Sky Colors */
    --sky-50: #f0f9ff;
    --sky-100: #e0f2fe;
    --sky-200: #bae6fd;
    --sky-300: #7dd3fc;
    --sky-400: #38bdf8;
    --sky-500: #0ea5e9;
    --sky-600: #0284c7;
    --sky-700: #0369a1;
    --sky-800: #075985;
    --sky-900: #0c4a6e;
    --sky-950: #082f49;

    /* Violet Colors */
    --violet-50: #f5f3ff;
    --violet-100: #ede9fe;
    --violet-200: #ddd6fe;
    --violet-300: #c4b5fd;
    --violet-400: #a78bfa;
    --violet-500: #8b5cf6;
    --violet-600: #7c3aed;
    --violet-700: #6d28d9;
    --violet-800: #5b21b6;
    --violet-900: #4c1d95;
    --violet-950: #2e1065;

    /* Fuchsia Colors */
    --fuchsia-50: #fdf4ff;
    --fuchsia-100: #fae8ff;
    --fuchsia-200: #f5d0fe;
    --fuchsia-300: #f0abfc;
    --fuchsia-400: #e879f9;
    --fuchsia-500: #d946ef;
    --fuchsia-600: #c026d3;
    --fuchsia-700: #a21caf;
    --fuchsia-800: #86198f;
    --fuchsia-900: #701a75;
    --fuchsia-950: #4a044e;

    /* Stone Colors */
    --stone-50: #fafaf9;
    --stone-100: #f5f5f4;
    --stone-200: #e7e5e4;
    --stone-300: #d6d3d1;
    --stone-400: #a8a29e;
    --stone-500: #78716c;
    --stone-600: #57534e;
    --stone-700: #44403c;
    --stone-800: #292524;
    --stone-900: #1c1917;
    --stone-950: #0c0a09;

    --surface-0: #ffffff;
    --surface-25: #f9fafa;
    --surface-50: #f3f4f4;
    --surface-100: #e7e8e9;
    --surface-150: #dbddde;
    --surface-200: #cfd1d3;
    --surface-300: #b7babe;
    --surface-400: #9fa3a8;
    --surface-500: #878c92;
    --surface-600: #6c7075;
    --surface-650: #5f6266;
    --surface-700: #515458;
    --surface-800: #36383a;
    --surface-850: #282a2c;
    --surface-900: #1b1c1b;
    --surface-950: #0d0e0f;

    /* Button Styles */
    --p-button-border-radius: var(--border-radius-lg);
    --p-button-rounded-border-radius: 2rem;
    --p-button-gap: 0.5rem;
    --p-button-padding-x: 1rem;
    --p-button-padding-y: 0.5rem;
    --p-button-icon-only-width: 2.375rem;
    /*--p-button-icon-only-height: 2.375rem; this doesn't do anything */

    /* Button Sizes */
    --p-button-sm-font-size: .929rem;
    --p-button-sm-padding-x: 0.625rem;
    --p-button-sm-padding-y: 0.275rem;
    --p-button-lg-font-size: 1.125rem;
    --p-button-lg-padding-x: 0.875rem;
    --p-button-lg-padding-y: 0.75rem;

    /* Button Label */
    --p-button-label-font-weight: 500;

    /* Button Shadow */
    --p-button-raised-shadow:
        0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);

    /* Button Focus */
    --p-button-focus-ring-width: 2px;
    --p-button-focus-ring-style: solid;
    --p-button-focus-ring-color: var(--primary-500);
    --p-button-focus-ring-offset: 1px;
    --p-button-focus-shadow: 0 0 0 0.2rem var(--blue-200);

    /* Button Badge */
    --p-button-badge-size: 1rem;

    /* Button Transition */
    --p-button-transition-duration: 0.2s;

    /* Primary Button */
    --p-button-primary-background: var(--blue-600);
    --p-button-primary-hover-background: var(--blue-700);
    --p-button-primary-active-background: var(--blue-800);
    --p-button-primary-border-color: var(--blue-600);
    --p-button-primary-hover-border-color: var(--blue-700);
    --p-button-primary-active-border-color: var(--blue-800);
    --p-button-primary-color: #ffffff;
    --p-button-primary-hover-color: #ffffff;
    --p-button-primary-active-color: #ffffff;
    --p-button-primary-focus-shadow: 0 0 0 0.2rem var(--blue-200);
    --p-button-primary-focus-ring-color: var(--primary-600);

    /* Secondary Button */
    --p-button-secondary-background: var(--surface-0);
    --p-button-secondary-hover-background: var(--surface-100);
    --p-button-secondary-active-background: var(--bluegray-150);
    --p-button-secondary-border-color: var(--surface-500);
    --p-button-secondary-hover-border-color: var(--surface-500);
    --p-button-secondary-active-border-color: var(--primary-600);
    --p-button-secondary-color: var(--surface-600);
    --p-button-secondary-hover-color: var(--surface-700);
    --p-button-secondary-active-color: var(--surface-850);
    --p-button-secondary-focus-shadow: 0 0 0 0.2rem var(--surface-200);
    --p-button-secondary-focus-ring-color: var(--surface-600);

    /* Info Button */
    --p-button-info-background: var(--surface-100);
    --p-button-info-hover-background: var(--surface-300);
    --p-button-info-active-background: var(--primary-600);
    --p-button-info-border-color: var(--surface-100);
    --p-button-info-hover-border-color: var(--surface-300);
    --p-button-info-active-border-color: var(--primary-600);
    --p-button-info-color: var(--surface-650);
    --p-button-info-hover-color: var(--surface-900);
    --p-button-info-active-color: var(--surface-0);
    --p-button-info-focus-shadow: 0 0 0 0.2rem var(--surface-200);
    --p-button-info-focus-ring-color: var(--surface-600);

    /* Success Button */
    --p-button-success-background: var(--green-500);
    --p-button-success-hover-background: var(--green-600);
    --p-button-success-active-background: var(--green-700);
    --p-button-success-border-color: var(--green-500);
    --p-button-success-hover-border-color: var(--green-600);
    --p-button-success-active-border-color: var(--green-700);
    --p-button-success-color: #ffffff;
    --p-button-success-hover-color: #ffffff;
    --p-button-success-active-color: #ffffff;
    --p-button-success-focus-shadow: 0 0 0 0.2rem var(--green-200);

    /* Warning Button */
    --p-button-warning-background: var(--orange-500);
    --p-button-warning-hover-background: var(--orange-600);
    --p-button-warning-active-background: var(--orange-700);
    --p-button-warning-border-color: var(--orange-500);
    --p-button-warning-hover-border-color: var(--orange-600);
    --p-button-warning-active-border-color: var(--orange-700);
    --p-button-warning-color: #ffffff;
    --p-button-warning-hover-color: #ffffff;
    --p-button-warning-active-color: #ffffff;
    --p-button-warning-focus-shadow: 0 0 0 0.2rem var(--orange-200);

    /* Help Button */
    --p-button-help-background: var(--purple-500);
    --p-button-help-hover-background: var(--purple-600);
    --p-button-help-active-background: var(--purple-700);
    --p-button-help-border-color: var(--purple-500);
    --p-button-help-hover-border-color: var(--purple-600);
    --p-button-help-active-border-color: var(--purple-700);
    --p-button-help-color: #ffffff;
    --p-button-help-hover-color: #ffffff;
    --p-button-help-active-color: #ffffff;
    --p-button-help-focus-shadow: 0 0 0 0.2rem var(--purple-200);

    /* Danger Button */
    --p-button-danger-background: var(--red-500);
    --p-button-danger-hover-background: var(--red-600);
    --p-button-danger-active-background: var(--red-700);
    --p-button-danger-border-color: var(--red-500);
    --p-button-danger-hover-border-color: var(--red-600);
    --p-button-danger-active-border-color: var(--red-700);
    --p-button-danger-color: #ffffff;
    --p-button-danger-hover-color: #ffffff;
    --p-button-danger-active-color: #ffffff;
    --p-button-danger-focus-shadow: 0 0 0 0.2rem var(--red-200);

    /* Contrast Button */
    --p-button-contrast-background: var(--surface-950);
    --p-button-contrast-hover-background: var(--surface-900);
    --p-button-contrast-active-background: var(--surface-800);
    --p-button-contrast-border-color: var(--surface-950);
    --p-button-contrast-hover-border-color: var(--surface-900);
    --p-button-contrast-active-border-color: var(--surface-800);
    --p-button-contrast-color: var(--surface-0);
    --p-button-contrast-hover-color: var(--surface-0);
    --p-button-contrast-active-color: var(--surface-0);
    --p-button-contrast-focus-shadow: 0 0 0 0.2rem var(--surface-400);

    /* Outlined Button Variants */
    --p-button-outlined-primary-hover-background: var(--bluegray-50);
    --p-button-outlined-primary-active-background: var(--primary-100);
    --p-button-outlined-primary-border-color: var(--surface-500);
    --p-button-outlined-primary-color: var(--surface-600);

    --p-button-outlined-secondary-hover-background: var(--surface-50);
    --p-button-outlined-secondary-active-background: var(--surface-100);
    --p-button-outlined-secondary-border-color: var(--surface-500);
    --p-button-outlined-secondary-color: var(--surface-500);

    --p-button-outlined-success-hover-background: var(--green-50);
    --p-button-outlined-success-active-background: var(--green-100);
    --p-button-outlined-success-border-color: var(--green-200);
    --p-button-outlined-success-color: var(--green-500);

    --p-button-outlined-info-hover-background: var(--sky-50);
    --p-button-outlined-info-active-background: var(--sky-100);
    --p-button-outlined-info-border-color: var(--sky-200);
    --p-button-outlined-info-color: var(--surface-500);

    --p-button-outlined-warning-hover-background: var(--orange-50);
    --p-button-outlined-warning-active-background: var(--orange-100);
    --p-button-outlined-warning-border-color: var(--orange-200);
    --p-button-outlined-warning-color: var(--orange-500);

    --p-button-outlined-help-hover-background: var(--purple-50);
    --p-button-outlined-help-active-background: var(--purple-100);
    --p-button-outlined-help-border-color: var(--purple-200);
    --p-button-outlined-help-color: var(--purple-500);

    --p-button-outlined-danger-hover-background: var(--red-50);
    --p-button-outlined-danger-active-background: var(--red-100);
    --p-button-outlined-danger-border-color: var(--red-200);
    --p-button-outlined-danger-color: var(--red-500);

    --p-button-outlined-contrast-hover-background: var(--surface-50);
    --p-button-outlined-contrast-active-background: var(--surface-100);
    --p-button-outlined-contrast-border-color: var(--surface-200);
    --p-button-outlined-contrast-color: var(--surface-500);

    /* Text Button Variants */
    --p-button-text-primary-color: var(--primary-600);
    --p-button-text-primary-hover-color: var(--primary-700);
    --p-button-text-primary-active-color: var(--primary-800);
    --p-button-text-primary-hover-background: var(--bluegray-150);

    --p-button-text-secondary-color: var(--surface-600);
    --p-button-text-secondary-hover-color: var(--surface-700);
    --p-button-text-secondary-active-color: var(--surface-800);
    --p-button-text-secondary-hover-background: var(--surface-100);

    --p-button-text-success-color: var(--green-600);
    --p-button-text-success-hover-color: var(--green-700);
    --p-button-text-success-active-color: var(--green-800);

    --p-button-text-info-color: var(--sky-600);
    --p-button-text-info-hover-color: var(--sky-700);
    --p-button-text-info-active-color: var(--sky-800);

    --p-button-text-warning-color: var(--orange-600);
    --p-button-text-warning-hover-color: var(--orange-700);
    --p-button-text-warning-active-color: var(--orange-800);

    --p-button-text-help-color: var(--purple-600);
    --p-button-text-help-hover-color: var(--purple-700);
    --p-button-text-help-active-color: var(--purple-800);

    --p-button-text-danger-color: var(--red-600);
    --p-button-text-danger-hover-color: var(--red-700);
    --p-button-text-danger-active-color: var(--red-800);

    --p-button-text-contrast-color: var(--surface-800);
    --p-button-text-contrast-hover-color: var(--surface-900);
    --p-button-text-contrast-active-color: var(--surface-950);

    /* Input Text */
    --p-inputtext-background: var(--surface-0);
    --p-inputtext-disabled-background: var(--surface-100);
    --p-inputtext-filled-background: var(--surface-100);
    --p-inputtext-filled-hover-background: var(--surface-200);
    --p-inputtext-filled-focus-background: var(--surface-100);
    --p-inputtext-border-color: var(--surface-500);
    --p-inputtext-hover-border-color: var(--surface-600);
    --p-inputtext-focus-border-color: var(--primary-600);
    --p-inputtext-invalid-border-color: var(--red-500);
    --p-inputtext-color: var(--surface-850);
    --p-inputtext-disabled-color: var(--surface-500);
    --p-inputtext-placeholder-color: var(--surface-600);
    --p-inputtext-invalid-placeholder-color: var(--red-500);
    --p-inputtext-border-radius: var(--border-radius-lg);
    --p-inputtext-shadow: none;
    --p-inputtext-padding-x: var(--form-field-padding-x);
    --p-inputtext-padding-y: var(--form-field-padding-y);


    /* Input Text Focus */
    --p-inputtext-focus-ring-width: 0;
    --p-inputtext-focus-ring-style: none;
    --p-inputtext-focus-ring-color: transparent;
    --p-inputtext-focus-ring-offset: 0;
    --p-inputtext-focus-ring-shadow: 0 0 0 0.2rem var(--primary-200);

    /* Input Text Sizes */
    --p-inputtext-sm-font-size: 0.875rem;
    --p-inputtext-sm-padding-x: 0.625rem;
    --p-inputtext-sm-padding-y: 0.5rem;
    --p-inputtext-lg-font-size: 1.125rem;
    --p-inputtext-lg-padding-x: 0.875rem;
    --p-inputtext-lg-padding-y: 0.75rem;

    /* Input Text Transition */
    --p-inputtext-transition-duration: 0.2s;

    /* Dialog */
    --p-dialog-background: var(--surface-0);
    --p-dialog-border-color: transparent;
    --p-dialog-color: var(--surface-850);
    --p-dialog-border-radius: var(--border-radius-xl);
    --p-dialog-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

    /* Dialog Header */
    --p-dialog-header-padding: 1.5rem;
    --p-dialog-header-gap: 0.5rem;

    /* Dialog Title */
    --p-dialog-title-font-size: 1.5rem;
    --p-dialog-title-font-weight: 600;

    /* Dialog Content */
    --p-dialog-content-padding: 0 1.5rem 1.5rem 1.5rem;

    /* Dialog Footer */
    --p-dialog-footer-padding: 0 1.5rem 1.5rem 1.5rem;
    --p-dialog-footer-gap: 0.5rem;

    /* Panel */
    --p-panel-background: var(--surface-0);
    --p-panel-border-color: var(--surface-150);
    --p-panel-border-radius: var(--border-radius-lg);
    --p-panel-header-padding: 1rem;
    --p-panel-header-background: var(--surface-0);
    --p-panel-header-border-color: var(--surface-150);
    --p-panel-header-color: var(--surface-850);
    --p-panel-content-padding: 1rem;
    --p-panel-content-background: var(--surface-0);
    --p-panel-content-color: var(--surface-850);
    --p-panel-footer-padding: 1rem;
    --p-panel-footer-background: var(--surface-0);
    --p-panel-footer-border-color: var(--surface-150);

    /* Card */
    --p-card-background: var(--surface-0);
    --p-card-border-color: var(--surface-150);
    --p-card-border-radius: var(--border-radius-lg);
    --p-card-padding: 1.5rem;
    --p-card-shadow:
        0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 1px 3px 0 rgba(0, 0, 0, 0.12);
    --p-card-title-color: var(--surface-850);
    --p-card-title-font-size: 1.5rem;
    --p-card-title-font-weight: 600;
    --p-card-subtitle-color: var(--surface-600);
    --p-card-subtitle-font-weight: 400;
    --p-card-content-padding: 1rem 0;
    --p-card-footer-padding: 1rem 0 0 0;

    /* Table */
    --p-datatable-background: var(--surface-0);
    --p-datatable-border-color: var(--surface-150);
    --p-datatable-border-radius: var(--border-radius-md);
    --p-datatable-header-padding: 1rem;
    --p-datatable-header-background: var(--surface-0);
    --p-datatable-header-border-color: var(--surface-150);
    --p-datatable-header-color: var(--surface-600);
    --p-datatable-cell-padding: 1rem;
    --p-datatable-cell-border-color: var(--surface-150);
    --p-datatable-row-hover-background: var(--surface-25);
    --p-datatable-row-stripe-background: var(--surface-50);
    --p-datatable-sort-icon-color: var(--icon-color-primary);
    --p-datatable-sort-badge-background: var(--primary-500);
    --p-datatable-sort-badge-color: var(--primary-0);

    /* Menu */
    --p-menu-background: var(--surface-0);
    --p-menu-border-color: var(--surface-150);
    --p-menu-border-radius: var(--border-radius-md);
    --p-menu-padding: 0.5rem 0;
    --p-menu-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    --p-menu-item-padding: 0.75rem 1rem;
    --p-menu-item-color: var(--surface-850);
    --p-menu-item-hover-background: var(--surface-100);
    --p-menu-item-hover-color: var(--surface-850);
    --p-menu-item-active-background: var(--primary-50);
    --p-menu-item-active-color: var(--primary-850);
    --p-menu-separator-color: var(--surface-200);
    --p-menu-submenu-label-color: var(--surface-850);
    --p-menu-item-icon-color: var(--icon-color-primary);

    /* Dropdown */
    --p-dropdown-background: var(--surface-0);
    --p-dropdown-border-color: var(--surface-500);
    --p-dropdown-border-radius: var(--border-radius-lg);
    --p-dropdown-hover-border-color: var(--primary-400);
    --p-dropdown-focus-border-color: var(--primary-500);
    --p-dropdown-item-padding: 0.375rem 0.75rem;
    --p-dropdown-item-hover-background: var(--surface-100);
    --p-dropdown-item-hover-color: var(--surface-900);
    --p-dropdown-trigger-color: var(--surface-600);
    --p-dropdown-trigger-hover-color: var(--surface-900);
    --p-dropdown-list-background: var(--surface-0);
    --p-dropdown-list-border-radius: var(--border-radius-md);
    --p-dropdown-list-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    --p-dropdown-padding-x: var(--form-field-padding-x);
    --p-dropdown-padding-y: var(--form-field-padding-y);

    /* Checkbox */
    --p-checkbox-width: 1.286rem;
    --p-checkbox-height: 1.286rem;
    --p-checkbox-border-color: var(--surface-600);
    --p-checkbox-border-radius: 4px;
    --p-checkbox-hover-border-color: var(--surface-800);
    --p-checkbox-active-border-color: var(--primary-600);
    --p-checkbox-active-background: var(--primary-600);
    --p-checkbox-checked-background: var(--primary-600);
    --p-checkbox-checked-hover-background: var(--primary-700);
    --p-checkbox-active-color: var(--primary-0);
 

    /* Checkbox Focus */
    --p-checkbox-focus-ring-width: 2px;
    --p-checkbox-focus-ring-style: solid;
    --p-checkbox-focus-ring-color: var(--primary-500);
    --p-checkbox-focus-ring-offset: 1px;
    --p-checkbox-focus-ring-shadow: 0 0 0 .2rem var(--primary-200);



    /* Radio Button */
    --p-radiobutton-width: 1.25rem;
    --p-radiobutton-height: 1.25rem;
    --p-radiobutton-border-color: var(--surface-600);
    --p-radiobutton-border-radius: 50%;
    --p-radiobutton-hover-border-color: var(--primary-600);
    --p-radiobutton-active-border-color: var(--primary-600);
    --p-radiobutton-active-background: var(--primary-900);
    --p-radiobutton-active-color: var(--primary-0);
    --p-radiobutton-focus-shadow: 0 0 0 0.2rem var(--primary-200);

    /* Radio Button Focus */
    --p-radiobutton-focus-ring-width: 1px;
    --p-radiobutton-focus-ring-style: solid;
    --p-radiobutton-focus-ring-color: var(--primary-500);
    --p-radiobutton-focus-ring-offset: 0;
    --p-radiobutton-focus-ring-shadow: 0 0 0 0.2rem var(--primary-200);

    /* Toast */
    --p-toast-border-radius: var(--border-radius-lg);
    --p-toast-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    --p-toast-padding: 1rem;
    --p-toast-margin: 0.5rem;
    --p-toast-info-background: var(--bluegray-50);
    --p-toast-info-border-color: var(--bluegray-500);
    --p-toast-info-color: var(--surface-850);
    --p-toast-success-background: var(--green-50);
    --p-toast-success-border-color: var(--green-500);
    --p-toast-success-color: var(--green-900);
    --p-toast-warn-background: var(--orange-50);
    --p-toast-warn-border-color: var(--orange-500);
    --p-toast-warn-color: var(--orange-900);
    --p-toast-error-background: var(--red-50);
    --p-toast-error-border-color: var(--red-500);
    --p-toast-error-color: var(--red-900);

    /* Tooltip */
    --p-tooltip-background: var(--surface-700);
    --p-tooltip-color: var(--surface-0);
    --p-tooltip-border-radius: var(--border-radius-md);
    --p-tooltip-padding: 0.2rem 0.625rem;
    --p-tooltip-arrow-width: 0.5rem;
    --p-tooltip-arrow-height: 0.5rem;
    --p-tooltip-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    --p-tooltip-gutter: 0.3rem;

    
    

    /* Tabs */
    --p-tabview-nav-background: transparent;
    --p-tabview-nav-border-width: 0 0 2px 0;
    --p-tabs-active-bar-height: 2px;
    --p-tabview-nav-border-color: var(--surface-150);
    --p-tabview-selected-nav-border-color: var(--primary-600);
    --p-tabview-nav-padding: 1rem;
    --p-tabview-nav-gap: 0;
    --p-tabview-nav-selected-font-weight: 600;
    --p-tabview-nav-hover-border-color: var(--primary-600);
    --p-tabview-nav-hover-background: var(--surface-100);
    --p-tabview-panel-padding: 1.5rem;
    --p-tabs-tab-active-color: var(--primary-850);
    --p-tabs-tab-active-border-color: var(--primary-600);
    --p-tabs-tab-hover-border-color: var(--surface-300);
    --p-tabs-tab-color: var(--surface-600);
    --p-tabs-tab-padding: .625rem 0rem 0.625rem 0rem;
    --p-tabs-tab-margin: 0rem 1.5rem -1px 0rem;
    --p-tabs-tab-border-width: 0 0 1px 0;
    --p-tabs-tabpanel-padding: 1rem 0 1rem 0;
    --p-tabs-tab-active-color: var(--primary-600);
    --p-tabs-tab-font-weight: 600;
    --p-tabs-active-bar-bottom: -1px;
    --p-tabs-active-bar-height: 2px;
    --p-tabs-tablist-border-width: 0 0 1px 0;
    --p-tabs-tablist-border-color: var(--surface-150); 
    --p-tabs-tab-focus-ring-width: 0;
    --p-tabs-tab-focus-ring-style: none;
    --p-tabs-tab-focus-ring-color: transparent;
    --p-tabs-tab-focus-ring-offset: 0;
    --p-tabs-tab-focus-ring-shadow: 0 0 0 0.2rem var(--primary-200);


    /* Calendar */
    --p-calendar-background: var(--surface-0);
    --p-calendar-border-color: var(--surface-300);
    --p-calendar-border-radius: var(--border-radius-md);
    --p-calendar-padding: 0.625rem 0.75rem;
    --p-calendar-hover-border-color: var(--primary-400);
    --p-calendar-focus-border-color: var(--primary-500);
    --p-calendar-button-width: 2rem;
    --p-calendar-button-height: 2rem;
    --p-calendar-button-border-radius: 50%;
    --p-calendar-button-hover-background: var(--surface-100);
    --p-calendar-button-hover-color: var(--surface-850);
    --p-calendar-table-cell-padding: 0.5rem;
    --p-calendar-table-cell-hover-background: var(--surface-100);
    --p-calendar-table-cell-selected-background: var(--primary-500);
    --p-calendar-table-cell-selected-color: var(--primary-0);
    --p-calendar-table-cell-today-border-color: var(--primary-400);

    /* Slider */
    --p-slider-background: var(--surface-200);
    --p-slider-height: 0.25rem;
    --p-slider-border-radius: var(--border-radius-md);
    --p-slider-handle-background: var(--primary-500);
    --p-slider-handle-border-color: var(--primary-500);
    --p-slider-handle-hover-background: var(--primary-600);
    --p-slider-handle-hover-border-color: var(--primary-600);
    --p-slider-handle-width: 1.25rem;
    --p-slider-handle-height: 1.25rem;
    --p-slider-handle-border-radius: 50%;
    --p-slider-range-background: var(--primary-500);
    --p-slider-handle-content-height: 1rem;
    --p-slider-handle-content-width: 1.5rem;
    --p-slider-handle-content-background: var(--primary-500);

    /* Progress Bar */
    --p-progressbar-height: 0.5rem;
    --p-progressbar-background: var(--surface-200);
    --p-progressbar-border-radius: var(--border-radius-md);
    --p-progressbar-value-background: var(--primary-500);
    --p-progressbar-value-color: var(--primary-0);
    --p-progressbar-border-radius: var(--border-radius-md);

    /* Accordion */
    --p-accordion-background: var(--surface-0);
    --p-accordion-border-color: var(--surface-150);
    --p-accordion-border-radius: 0px;
    --p-accordion-header-padding: 1rem 1.5rem;
    --p-accordion-header-background: var(--surface-0);
    --p-accordion-header-hover-background: var(--surface-50);
    --p-accordion-header-active-color: var(--surface-800);
    --p-accordion-header-active-background: var(--surface-50);
    --p-accordion-header-active-hover-background: var(--surface-50);
    --p-accordion-header-color: var(--surface-800);
    --p-accordion-header-border-radius: 0px;
    --p-accordion-header-first-top-border-radius: 0px;
    --p-accordion-header-last-bottom-border-radius: 0px;
    --p-accordion-header-last-active-bottom-border-radius: 0px;
    --p-accordion-content-padding: 1rem 1.5rem 2rem 1.5rem;
    --p-accordion-content-background: var(--surface-0);
    --p-accordion-content-border-color: var(--surface-150);
    --p-accordion-header-toggle-icon-color: var(--surface-850);
    --p-accordion-header-toggle-icon-hover-color: var(--surface-850);
    --p-accordion-header-toggle-icon-active-color: var(--surface-850);

    /* Breadcrumb Focus */
    --p-accordion-header-focus-ring-width: 1px;
    --p-accordion-header-focus-ring-style: solid;
    --p-accordion-header-focus-ring-color: var(--primary-500);
    --p-accordion-header-focus-ring-offset: 0;
    --p-accordion-header-focus-ring-shadow: 0 0 0 0.2rem var(--primary-200);

    /* Tree */
    --p-tree-padding: 1rem;
    --p-tree-node-padding: 0.5rem;
    --p-tree-node-content-padding: 0.5rem;
    --p-tree-node-hover-background: var(--surface-100);
    --p-tree-node-selected-background: var(--primary-50);
    --p-tree-node-selected-color: var(--primary-600);
    --p-tree-node-icon-selected-color: var(--icon-color-primary);
    --p-tree-node-focus-shadow: 0 0 0 0.2rem var(--primary-100);
    --p-tree-toggler-width: 4rem;
    --p-tree-toggler-height: 4rem;
    --p-tree-toggler-border-radius: 6px;
    --p-tree-node-toggle-button-border-radius: 6px;
    --p-tree-toggler-hover-background: var(--surface-100);
    --p-tree-node-toggle-button-hover-background: var(--surface-100);

    /* File Upload */
    --p-fileupload-content-padding: 2rem;
    --p-fileupload-content-border-color: var(--surface-150);
    --p-fileupload-content-border-style: dashed;
    --p-fileupload-content-border-radius: var(--border-radius-md);
    --p-fileupload-content-hover-border-color: var(--primary-400);
    --p-fileupload-content-hover-background: var(--surface-50);

    /* Paginator */
    --p-paginator-background: var(--surface-0);
    --p-paginator-border-color: var(--surface-150);
    --p-paginator-padding: 1rem;
    --p-paginator-element-width: 2.286rem;
    --p-paginator-element-height: 2.286rem;
    --p-paginator-element-border-radius: 6px;
    --p-paginator-element-hover-background: var(--surface-100);
    --p-paginator-element-hover-color: var(--surface-900);
    --p-paginator-element-active-background: var(--primary-600);
    --p-paginator-element-active-color: var(--primary-0);
    --p-paginator-element-active-color: var(--primary-0);
    --p-paginator-nav-button-selected-background: var(--bluegray-150);
    --p-paginator-nav-button-selected-color: var(--primary-850);
    --p-paginator-nav-button-border-radius: 6px;
    --p-paginator-nav-button-color: var(--surface-600);

    /* Steps */
    --p-steps-item-width: 2rem;
    --p-steps-item-height: 2rem;
    --p-steps-item-border-radius: 50%;
    --p-steps-item-background: var(--surface-0);
    --p-steps-item-border-color: var(--surface-300);
    --p-steps-item-color: var(--surface-900);
    --p-steps-item-active-background: var(--primary-100);
    --p-steps-item-active-color: var(--primary-0);
    --p-steps-item-visited-background: var(--primary-200);
    --p-steps-item-visited-color: var(--primary-700);

    /* Breadcrumb */
    --p-breadcrumb-padding: 1rem;
    --p-breadcrumb-border-radius: var(--border-radius-md);
    --p-breadcrumb-item-padding: 0.5rem;
    --p-breadcrumb-item-color: var(--surface-600);
    --p-breadcrumb-item-icon-color: var(--icon-color-primary);
    --p-breadcrumb-item-icon-hover-color: var(--surface-850);
    --p-breadcrumb-item-active-color: var(--surface-900);
    --p-breadcrumb-separator-color: var(--surface-400);
    --p-breadcrumb-separator-padding: 0 0.5rem;

    /* Breadcrumb Focus */
    --p-breadcrumb-item-focus-ring-width: 1px;
    --p-breadcrumb-item-focus-ring-style: solid;
    --p-breadcrumb-item-focus-ring-color: var(--primary-500);
    --p-breadcrumb-item-focus-ring-offset: 0;
    --p-breadcrumb-item-focus-ring-shadow: 0 0 0 0.2rem var(--primary-200);

    /* Tag */
    --p-tag-padding: 0.25rem 0.5rem;
    --p-tag-border-radius: var(--border-radius-md);
    --p-tag-font-weight: 600;
    --p-tag-info-background: var(--surface-100);
    --p-tag-info-color: var(--surface-800);
    --p-tag-success-background: var(--green-100);
    --p-tag-success-color: var(--green-800);
    --p-tag-warning-background: var(--orange-100);
    --p-tag-warning-color: var(--orange-800);
    --p-tag-danger-background: var(--red-100);
    --p-tag-danger-color: var(--red-800);

    /* MultiSelect */
    --p-multiselect-background: var(--surface-0);
    --p-multiselect-border-color: var(--surface-500);
    --p-multiselect-border-radius: var(--border-radius-lg);
    --p-multiselect-hover-border-color: var(--surface-600);
    --p-multiselect-focus-border-color: var(--primary-600);
    --p-multiselect-list-background: var(--surface-0);
    --p-multiselect-list-border-radius: var(--border-radius-md);
    --p-multiselect-list-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    --p-multiselect-token-background: var(--primary-100);
    --p-multiselect-token-color: var(--primary-700);
    --p-multiselect-token-padding: 0.25rem 0.5rem;
    --p-multiselect-token-border-radius: var(--border-radius-md);
    --p-multiselect-token-margin: 0.25rem;
    --p-multiselect-dropdown-color: var(--icon-color-primary);
    --p-multiselect-placeholder-color: var(--surface-600);
    --p-multiselect-padding-x: var(--select-field-padding-x);
    --p-multiselect-padding-y: var(--select-field-padding-y);

    /* MultiSelect Focus */
    --p-multiselect-focus-ring-width: 0;
    --p-multiselect-focus-ring-style: none;
    --p-multiselect-focus-ring-color: transparent;
    --p-multiselect-focus-ring-offset: 0;
    --p-multiselect-focus-ring-shadow: 0 0 0 0.2rem var(--primary-200);

    /* Chips */
    --p-chip-background: var(--surface-100);
    --p-chip-border-color: var(--surface-500);
    --p-chip-border-radius: var(--border-radius-md);
    --p-chip-padding: 0.625rem 0.75rem;
    --p-chip-hover-border-color: var(--surface-600);
    --p-chip-focus-border-color: var(--primary-600);
    --p-chip-token-background: var(--primary-100);
    --p-chip-token-color: var(--primary-850);
    --p-chip-token-padding: 0.25rem 0.5rem;
    --p-chip-token-border-radius: var(--border-radius-md);
    --p-chip-token-margin: 0.25rem;
    --p-chip-remove-icon-color: var(--icon-color-primary);

    /* Split Button */
    --p-splitbutton-menu-background: var(--surface-0);
    --p-splitbutton-menu-border-color: var(--surface-200);
    --p-splitbutton-menu-border-radius: var(--border-radius-lg);
    --p-splitbutton-menu-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    --p-splitbutton-menu-item-padding: 0.75rem 1rem;
    --p-splitbutton-menu-item-hover-background: var(--surface-100);
    --p-splitbutton-menu-item-hover-color: var(--surface-850);

    /* Speed Dial */
    --p-speeddial-button-width: 4rem;
    --p-speeddial-button-height: 4rem;
    --p-speeddial-button-border-radius: 50%;
    --p-speeddial-button-background: var(--primary-500);
    --p-speeddial-button-color: var(--primary-0);
    --p-speeddial-button-hover-background: var(--primary-600);
    --p-speeddial-action-background: var(--surface-0);
    --p-speeddial-action-color: var(--surface-700);
    --p-speeddial-action-width: 3rem;
    --p-speeddial-action-height: 3rem;
    --p-speeddial-action-border-radius: 50%;
    --p-speeddial-action-hover-background: var(--surface-100);
    --p-speeddial-action-hover-color: var(--surface-900);

    /* Carousel */
    --p-carousel-content-background: var(--surface-0);
    --p-carousel-content-border-color: var(--surface-200);
    --p-carousel-content-border-radius: var(--border-radius-md);
    --p-carousel-indicator-width: 2rem;
    --p-carousel-indicator-height: 0.5rem;
    --p-carousel-indicator-border-radius: var(--border-radius-md);
    --p-carousel-indicator-background: var(--surface-200);
    --p-carousel-indicator-active-background: var(--primary-500);
    --p-carousel-indicator-hover-background: var(--surface-300);
    --p-carousel-indicator-gap: 0.5rem;

    /* Galleria */
    --p-galleria-background: var(--surface-0);
    --p-galleria-border-color: var(--surface-200);
    --p-galleria-border-radius: var(--border-radius-md);
    --p-galleria-item-padding: 1rem;
    --p-galleria-thumbnail-width: 4rem;
    --p-galleria-thumbnail-height: 4rem;
    --p-galleria-thumbnail-border-radius: var(--border-radius-md);
    --p-galleria-thumbnail-active-border-color: var(--primary-500);
    --p-galleria-indicator-background: rgba(255, 255, 255, 0.3);
    --p-galleria-indicator-active-background: var(--primary-500);
    --p-galleria-indicator-border-radius: 50%;
    --p-galleria-indicator-width: 1rem;
    --p-galleria-indicator-height: 1rem;
    --p-galleria-indicator-gap: 0.5rem;

    /* Image */
    --p-image-preview-background: rgba(0, 0, 0, 0.9);
    --p-image-preview-padding: 1rem;
    --p-image-preview-border-radius: var(--border-radius-lg);
    --p-image-preview-mask-background: rgba(0, 0, 0, 0.9);
    --p-image-preview-icon-color: var(--surface-0);
    --p-image-preview-icon-size: 2rem;

    /* Editor */
    --p-editor-content-background: var(--surface-0);
    --p-editor-content-border-color: var(--surface-150);
    --p-editor-content-border-radius: var(--border-radius-lg);
    --p-editor-toolbar-background: var(--surface-0);
    --p-editor-toolbar-border-color: var(--surface-150);
    --p-editor-toolbar-padding: 0.5rem;
    --p-editor-toolbar-border-radius: var(--border-radius-md);
    --p-editor-toolbar-button-width: 2rem;
    --p-editor-toolbar-button-height: 2rem;
    --p-editor-toolbar-button-border-radius: var(--border-radius-md);
    --p-editor-toolbar-button-hover-background: var(--surface-200);
    --p-editor-toolbar-button-active-background: var(--surface-300);
    --p-editor-separator-color: var(--surface-300);

    /* Terminal */
    --p-terminal-background: var(--surface-900);
    --p-terminal-color: var(--surface-0);
    --p-terminal-border-radius: var(--border-radius-md);
    --p-terminal-padding: 1rem;
    --p-terminal-command-color: var(--primary-400);
    --p-terminal-response-color: var(--surface-0);
    --p-terminal-font-family: monospace;
    --p-terminal-font-size: 1rem;

    /* Avatar */
    --p-avatar-background: var(--surface-100);
    --p-avatar-color: var(--surface-850);
    --p-avatar-border-radius: 6px;
    --p-avatar-font-size: 1rem;
    --p-avatar-width: 2.5rem;
    --p-avatar-height: 2.5rem;
    --p-avatar-sm-font-size: 0.875rem;
    --p-avatar-sm-width: 2rem;
    --p-avatar-sm-height: 2rem;
    --p-avatar-lg-font-size: 1.5rem;
    --p-avatar-lg-width: 4rem;
    --p-avatar-lg-height: 4rem;
    --p-avatar-xl-font-size: 2rem;
    --p-avatar-xl-width: 6rem;
    --p-avatar-xl-height: 6rem;

    /* Badge */
    --p-badge-background: var(--primary-600);
    --p-badge-color: var(--primary-0);
    --p-badge-border-radius: 50%;
    --p-badge-font-weight: 700;
    --p-badge-min-width: 1.5rem;
    --p-badge-height: 1.5rem;
    --p-badge-dot-width: 0.5rem;
    --p-badge-dot-height: 0.5rem;

    /* Skeleton */
    --p-skeleton-background: var(--surface-150);
    --p-skeleton-animation-background: var(--surface-100);
    --p-skeleton-border-radius: var(--border-radius-md);
    --p-skeleton-animation-duration: 2s;

    /* Divider */
    --p-divider-background: var(--surface-150);
    --p-divider-text-color: var(--surface-850);
    --p-divider-font-weight: 600;
    --p-divider-padding: 1rem;
    --p-divider-border-width: 1px;
    --p-divider-border-color: var(--surface-150);

    /* Inplace */
    --p-inplace-padding: 1rem;
    --p-inplace-hover-background: var(--surface-100);
    --p-inplace-active-background: var(--surface-200);
    --p-inplace-active-color: var(--surface-850);
    --p-inplace-border-radius: var(--border-radius-md);

    /* ScrollTop */
    --p-scrolltop-background: var(--primary-500);
    --p-scrolltop-color: var(--primary-0);
    --p-scrolltop-width: 3rem;
    --p-scrolltop-height: 3rem;
    --p-scrolltop-border-radius: 50%;
    --p-scrolltop-hover-background: var(--primary-600);
    --p-scrolltop-transition-duration: 0.3s;

    /* Panel Menu */
    --p-panelmenu-background: var(--surface-0);
    --p-panelmenu-border-color: var(--surface-150);
    --p-panelmenu-border-radius: var(--border-radius-md);
    --p-panelmenu-header-padding: 1rem;
    --p-panelmenu-header-background: var(--surface-0);
    --p-panelmenu-header-hover-background: var(--surface-100);
    --p-panelmenu-header-active-background: var(--surface-200);
    --p-panelmenu-header-text-color: var(--surface-850);
    --p-panelmenu-content-padding: 1rem;
    --p-panelmenu-content-background: var(--surface-0);
    --p-panelmenu-content-border-color: var(--surface-150);

    /* Dock */
    --p-dock-background: var(--surface-0);
    --p-dock-border-radius: var(--border-radius-md);
    --p-dock-padding: 0.5rem;
    --p-dock-item-width: 4rem;
    --p-dock-item-height: 4rem;
    --p-dock-item-border-radius: 50%;
    --p-dock-item-hover-background: var(--surface-100);
    --p-dock-item-active-background: var(--surface-200);
    --p-dock-item-active-color: var(--surface-850);
    --p-dock-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    /* Splitter */
    --p-splitter-gutter-background: var(--surface-100);
    --p-splitter-gutter-handle-background: var(--surface-300);
    --p-splitter-gutter-handle-hover-background: var(--surface-400);
    --p-splitter-gutter-handle-width: 0.25rem;
    --p-splitter-gutter-handle-height: 2.5rem;
    --p-splitter-gutter-handle-border-radius: var(--border-radius-md);

    /* Timeline */
    --p-timeline-marker-background: var(--surface-200);
    --p-timeline-marker-border-color: var(--surface-300);
    --p-timeline-marker-width: 1rem;
    --p-timeline-marker-height: 1rem;
    --p-timeline-marker-border-radius: 50%;
    --p-timeline-marker-border-width: 2px;
    --p-timeline-line-background: var(--surface-200);
    --p-timeline-line-width: 2px;
    --p-timeline-event-padding: 1rem;
    --p-timeline-event-margin: 1rem;
    --p-timeline-event-border-radius: var(--border-radius-md);
    --p-timeline-event-background: var(--surface-0);
    --p-timeline-event-border-color: var(--surface-200);

    /* Toggle Button */
    --p-togglebutton-background: var(--surface-0);
    --p-togglebutton-border-color: var(--surface-500);
    --p-togglebutton-border-radius: var(--border-radius-lg);
    --p-togglebutton-padding: 0rem 0.5rem;
    --p-togglebutton-content-padding: var(--form-field-padding-y) var(--form-field-padding-x);
    --p-togglebutton-checked-background: var(--bluegray-150);
    --p-togglebutton-checked-border-color: var(--primary-600);
    --p-togglebutton-content-checked-background: var(--bluegray-150);
    --p-togglebutton-checked-color: var(--surface-850);
    --p-togglebutton-hover-background: var(--surface-100);
    --p-togglebutton-hover-border-color: var(--surface-400);
    --p-togglebutton-color: var(--surface-600);


    /* Toggle Button Focus */
    --p-togglebutton-focus-ring-width: 1px;
    --p-togglebutton-focus-ring-style: solid;
    --p-togglebutton-focus-ring-color: var(--primary-500);
    --p-togglebutton-focus-ring-offset: 0;
    --p-togglebutton-focus-ring-shadow: 0 0 0 0.2rem var(--primary-200);

    /* Toggle Switch */
    --p-toggleswitch-width: 3rem;
    --p-toggleswitch-height: 1.5rem;
    --p-toggleswitch-border-radius: var(--border-radius-pill);
    --p-toggleswitch-background: var(--surface-200);
    --p-toggleswitch-border-color: var(--surface-200);
    --p-toggleswitch-handle-background: var(--surface-0);
    --p-toggleswitch-handle-border-radius: 50%;
    --p-toggleswitch-handle-width: 1.25rem;
    --p-toggleswitch-handle-height: 1.25rem;
    --p-toggleswitch-checked-background: var(--primary-600);
    --p-toggleswitch-checked-border-color: var(--primary-600);
    --p-toggleswitch-focus-shadow: 0 0 0 0.2rem var(--primary-200);

    /* Toggle Switch Focus */
    --p-toggleswitch-focus-ring-width: 1px;
    --p-toggleswitch-focus-ring-style: solid;
    --p-toggleswitch-focus-ring-color: var(--primary-500);
    --p-toggleswitch-focus-ring-offset: 0;
    --p-toggleswitch-focus-ring-shadow: 0 0 0 0.2rem var(--primary-200);

    /* Toolbar */
    --p-toolbar-background: var(--surface-0);
    --p-toolbar-border-color: var(--surface-150);
    --p-toolbar-border-radius: var(--border-radius-md);
    --p-toolbar-padding: 1rem;
    --p-toolbar-gap: 0.5rem;

    /* TreeSelect */
    --p-treeselect-background: var(--surface-0);
    --p-treeselect-border-color: var(--surface-500);
    --p-treeselect-border-radius: var(--border-radius-md);
    --p-treeselect-padding: 0.625rem 0.75rem;
    --p-treeselect-hover-border-color: var(--surface-600);
    --p-treeselect-focus-border-color: var(--primary-600);
    --p-treeselect-list-background: var(--surface-0);
    --p-treeselect-list-border-radius: var(--border-radius-md);
    --p-treeselect-list-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    --p-treeselect-placeholder-color: var(--surface-850);

    /* TreeSelect Focus */
    --p-treeselect-focus-ring-width: 1px;
    --p-treeselect-focus-ring-style: solid;
    --p-treeselect-focus-ring-color: var(--primary-500);
    --p-treeselect-focus-ring-offset: 0;
    --p-treeselect-focus-ring-shadow: 0 0 0 0.2rem var(--primary-200);

    /* TreeTable */
    --p-treetable-background: var(--surface-0);
    --p-treetable-border-color: var(--surface-200);
    --p-treetable-border-radius: var(--border-radius-md);
    --p-treetable-header-padding: 1rem;
    --p-treetable-header-background: var(--surface-0);
    --p-treetable-header-border-color: var(--surface-200);
    --p-treetable-header-color: var(--surface-900);
    --p-treetable-cell-padding: 1rem;
    --p-treetable-cell-border-color: var(--surface-200);
    --p-treetable-row-hover-background: var(--surface-100);
    --p-treetable-row-stripe-background: var(--surface-50);
    --p-treetable-node-toggle-button-border-radius: 6px;
    --p-treetable-toggler-width: 4rem;
    --p-treetable-toggler-height: 4rem;
    --p-treetable-toggler-border-radius: 6px;
    --p-treetable-node-toggle-button-border-radius: 6px;
    --p-treetable-toggler-hover-background: var(--surface-100);
    --p-treetable-node-toggle-button-hover-background: var(--surface-100);

    /* Virtual Scroller */
    --p-virtualscroller-background: var(--surface-0);
    --p-virtualscroller-border-color: var(--surface-200);
    --p-virtualscroller-border-radius: var(--border-radius-md);
    --p-virtualscroller-padding: 0.5rem;
    --p-virtualscroller-item-padding: 0.5rem;
    --p-virtualscroller-loader-background: var(--surface-100);
    --p-virtualscroller-loader-color: var(--surface-700);

    /* Rating */
    --p-rating-icon-width: 1.25rem;
    --p-rating-icon-height: 1.25rem;
    --p-rating-icon-font-size: 1.25rem;
    --p-rating-icon-color: var(--surface-600);
    --p-rating-icon-active-color: var(--primary-600);
    --p-rating-icon-hover-color: var(--primary-600);
    --p-rating-icon-gap: 0.25rem;

    /* Ripple */
    --p-ripple-background: rgba(255, 255, 255, 0.3);
    --p-ripple-duration: 0.3s;

    /* Scroll Panel */
    --p-scrollpanel-background: var(--surface-0);
    --p-scrollpanel-border-color: var(--surface-200);
    --p-scrollpanel-border-radius: var(--border-radius-md);
    --p-scrollpanel-handle-background: var(--surface-300);
    --p-scrollpanel-handle-hover-background: var(--surface-400);
    --p-scrollpanel-handle-border-radius: var(--border-radius-md);

    /* Password */
    --p-password-panel-background: var(--surface-0);
    --p-password-panel-border-color: var(--surface-200);
    --p-password-panel-border-radius: var(--border-radius-lg);
    --p-password-panel-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    --p-password-meter-background: var(--surface-100);
    --p-password-meter-weak-background: var(--red-500);
    --p-password-meter-medium-background: var(--orange-500);
    --p-password-meter-strong-background: var(--green-500);
    --p-password-panel-padding-x: var(--form-field-padding-x);
    --p-password-panel-padding-y: var(--form-field-padding-y);

    /* Pick List */
    --p-picklist-background: var(--surface-0);
    --p-picklist-border-color: var(--surface-150);
    --p-picklist-border-radius: var(--border-radius-md);
    --p-picklist-header-padding: 1rem;
    --p-picklist-list-padding: 0.5rem 0;
    --p-picklist-item-padding: 0.75rem 1rem;
    --p-picklist-item-hover-background: var(--surface-100);
    --p-picklist-item-hover-color: var(--surface-850);
    --p-picklist-buttons-gap: 0.5rem;
    --p-picklist-buttons-width: 3rem;

    /* Order List */
    --p-orderlist-background: var(--surface-0);
    --p-orderlist-border-color: var(--surface-200);
    --p-orderlist-border-radius: var(--border-radius-md);
    --p-orderlist-header-padding: 1rem;
    --p-orderlist-list-padding: 0.5rem 0;
    --p-orderlist-item-padding: 0.75rem 1rem;
    --p-orderlist-item-hover-background: var(--surface-100);
    --p-orderlist-item-hover-color: var(--surface-900);
    --p-orderlist-buttons-gap: 0.5rem;
    --p-orderlist-buttons-width: 3rem;

    /* Organization Chart */
    --p-organizationchart-node-padding: 1rem;
    --p-organizationchart-node-gap: 2rem;
    --p-organizationchart-line-color: var(--surface-300);
    --p-organizationchart-line-width: 2px;
    --p-organizationchart-node-background: var(--surface-0);
    --p-organizationchart-node-border-color: var(--surface-150);
    --p-organizationchart-node-border-radius: var(--border-radius-md);

    /* Message */
    --p-message-padding: 1rem;
    --p-message-margin: 1rem;
    --p-message-border-radius: var(--border-radius-md);
    --p-message-info-background: var(--primary-50);
    --p-message-info-border-color: var(--primary-600);
    --p-message-info-color: var(--primary-700);
    --p-message-success-background: var(--green-100);
    --p-message-success-border-color: var(--green-500);
    --p-message-success-color: var(--green-900);
    --p-message-warn-background: var(--orange-100);
    --p-message-warn-border-color: var(--orange-500);
    --p-message-warn-color: var(--orange-900);
    --p-message-error-background: var(--red-100);
    --p-message-error-border-color: var(--red-500);
    --p-message-error-color: var(--red-900);

    /* Mega Menu */
    --p-megamenu-background: var(--surface-0);
    --p-megamenu-border-color: var(--surface-200);
    --p-megamenu-border-radius: var(--border-radius-md);
    --p-megamenu-padding: 0.5rem;
    --p-megamenu-item-padding: 0.75rem 1rem;
    --p-megamenu-item-hover-background: var(--surface-100);
    --p-megamenu-item-hover-color: var(--surface-900);
    --p-megamenu-submenu-padding: 1rem;
    --p-megamenu-submenu-header-padding: 0.75rem 1rem;
    --p-megamenu-submenu-header-font-weight: 600;

    /* Menu Bar */
    --p-menubar-background: var(--surface-0);
    --p-menubar-border-color: var(--surface-150);
    --p-menubar-border-radius: var(--border-radius-md);
    --p-menubar-color: var(--surface-850);
    --p-menubar-gap: 0.5rem;
    --p-menubar-padding: 0.5rem;
    --p-menubar-transition-duration: 0.2s;
    --p-menubar-base-item-border-radius: var(--border-radius-md);
    --p-menubar-base-item-padding: 0.75rem 1rem;
    --p-menubar-item-focus-background: var(--surface-100);
    --p-menubar-item-active-background: var(--primary-50);
    --p-menubar-item-color: var(--surface-850);
    --p-menubar-item-focus-color: var(--surface-850);
    --p-menubar-item-active-color: var(--primary-850);
    --p-menubar-item-padding: 0.75rem 1rem;
    --p-menubar-item-border-radius: var(--border-radius-md);
    --p-menubar-item-gap: 0.5rem;
    --p-menubar-item-icon-color: var(--icon-color-primary);
    --p-menubar-item-icon-focus-color: var(--surface-850);
    --p-menubar-item-icon-active-color: var(--primary-850);
    --p-menubar-submenu-padding: 0.5rem 0;
    --p-menubar-submenu-gap: 0;
    --p-menubar-submenu-background: var(--surface-0);
    --p-menubar-submenu-border-color: var(--surface-150);
    --p-menubar-submenu-border-radius: var(--border-radius-md);
    --p-menubar-submenu-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    --p-menubar-submenu-mobile-indent: 1rem;
    --p-menubar-submenu-icon-size: 0.75rem;
    --p-menubar-submenu-icon-color: var(--icon-color-primary);
    --p-menubar-submenu-icon-focus-color: var(--surface-850);
    --p-menubar-submenu-icon-active-color: var(--primary-850);
    --p-menubar-separator-border-color: var(--surface-150);
    --p-menubar-mobile-button-border-radius: var(--border-radius-md);
    --p-menubar-mobile-button-size: 2rem;
    --p-menubar-mobile-button-color: var(--surface-600);
    --p-menubar-mobile-button-hover-color: var(--surface-850);
    --p-menubar-mobile-button-hover-background: var(--surface-100);
    --p-menubar-mobile-button-focus-ring-width: 1px;
    --p-menubar-mobile-button-focus-ring-style: solid;
    --p-menubar-mobile-button-focus-ring-color: var(--primary-500);
    --p-menubar-mobile-button-focus-ring-offset: 0;
    --p-menubar-mobile-button-focus-ring-shadow: 0 0 0 0.2rem var(--primary-200);

    /* Color Picker */
    --p-colorpicker-width: 2.5rem;
    --p-colorpicker-height: 2.5rem;
    --p-colorpicker-border-radius: var(--border-radius-md);
    --p-colorpicker-border-color: var(--surface-300);
    --p-colorpicker-hover-border-color: var(--primary-400);
    --p-colorpicker-focus-border-color: var(--primary-500);
    --p-colorpicker-panel-background: var(--surface-0);
    --p-colorpicker-panel-border-radius: var(--border-radius-md);
    --p-colorpicker-panel-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    /* Confirm Dialog */
    --p-confirmdialog-padding: 1.5rem;
    --p-confirmdialog-background: var(--surface-0);
    --p-confirmdialog-border-radius: var(--border-radius-lg);
    --p-confirmdialog-header-padding: 1.5rem;
    --p-confirmdialog-header-border-bottom: 1px solid var(--surface-150);
    --p-confirmdialog-icon-font-size: 1.5rem;
    --p-confirmdialog-message-padding: 1.5rem;
    --p-confirmdialog-footer-padding: 1rem 1.5rem;
    --p-confirmdialog-footer-gap: 0.5rem;

    /* Drawer */
    --p-drawer-background: var(--surface-0);
    --p-drawer-border-color: var(--surface-150);
    --p-drawer-padding: 1.5rem;
    --p-drawer-width: 20rem;
    --p-drawer-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    /* Block UI */
    --p-blockui-background: rgba(0, 0, 0, 0.5);
    --p-blockui-border-radius: var(--border-radius-md);

    /* Textarea */
    --p-textarea-background: var(--surface-0);
    --p-textarea-border-color: var(--surface-500);
    --p-textarea-border-radius: var(--border-radius-lg);
    --p-textarea-padding: 0.625rem 0.75rem;
    --p-textarea-hover-border-color: var(--surface-600);
    --p-textarea-focus-border-color: var(--primary-600);
    --p-textarea-transition-duration: 0.2s;
    --p-textarea-focus-ring-shadow: 0 0 0 0.2rem var(--primary-200);

    /* Tiered Menu */
    --p-tieredmenu-background: var(--surface-0);
    --p-tieredmenu-border-color: var(--surface-150);
    --p-tieredmenu-border-radius: var(--border-radius-md);
    --p-tieredmenu-padding: 0.5rem 0;
    --p-tieredmenu-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    --p-tieredmenu-item-padding: 0.75rem 1rem;
    --p-tieredmenu-item-color: var(--surface-850);
    --p-tieredmenu-item-icon-color: var(--icon-color-primary);
    --p-tieredmenu-item-icon-hover-color: var(--surface-850);
    --p-tieredmenu-item-hover-background: var(--surface-100);
    --p-tieredmenu-item-hover-color: var(--surface-850);
    --p-tieredmenu-separator-color: var(--surface-200);


    
    /* Select */
    --p-select-border-color: var(--surface-500);
    --p-select-hover-border-color: var(--surface-600);
    --p-select-focus-border-color: var(--primary-500);
    --p-select-border-radius: var(--border-radius-lg);
    --p-select-placeholder-color: var(--surface-600);
    --p-select-panel-background: var(--surface-0);
    --p-select-panel-border-color: var(--surface-500);
    --p-select-panel-border-radius: var(--border-radius-lg);
    --p-select-panel-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    --p-select-clear-icon-color: var(--icon-color-primary);
    --p-select-dropdown-color: var(--icon-color-primary);
    --p-select-placeholder-color: var(--surface-600);
    --p-select-padding-x: var(--select-field-padding-x);
    --p-select-padding-y: var(--select-field-padding-y);
    
    /* Select Focus */
    --p-select-focus-ring-width: 0;
    --p-select-focus-ring-style: none;
    --p-select-focus-ring-color: transparent;
    --p-select-focus-ring-offset: 0;
    --p-select-focus-ring-shadow: 0 0 0 0.2rem var(--primary-200);

    /* Select Button */
    --p-selectbutton-background: var(--surface-0);
    --p-selectbutton-border-color: var(--surface-500);
    --p-selectbutton-border-radius: var(--border-radius-lg);
    --p-selectbutton-item-padding: 0.75rem 0.5rem;
    --p-selectbutton-item-background: transparent;
    --p-selectbutton-item-border-color: var(--surface-500);
    --p-selectbutton-item-color: var(--surface-600);
    --p-selectbutton-item-hover-background: var(--surface-100);
    --p-selectbutton-item-hover-color: var(--surface-900);
    --p-selectbutton-item-selected-background: var(--primary-600);
    --p-selectbutton-item-selected-color: var(--primary-0);
    

    /* Stepper */
    --p-stepper-background: var(--surface-0);
    --p-stepper-border-color: var(--surface-200);
    --p-stepper-border-radius: var(--border-radius-md);
    --p-stepper-padding: 1.5rem;
    --p-stepper-header-padding: 1rem;
    --p-stepper-content-padding: 1.5rem;
    --p-stepper-step-number-width: 2rem;
    --p-stepper-step-number-height: 2rem;
    --p-stepper-step-number-border-radius: 50%;
    --p-stepper-step-number-background: var(--primary-0);
    --p-stepper-step-number-color: var(--surface-700);
    --p-stepper-step-number-active-background: var(--primary-0);
    --p-stepper-step-number-active-color: var(--primary-0);
    --p-stepper-step-title-color: var(--surface-600);
    --p-stepper-step-title-active-color: var(--surface-850);
    --p-stepper-step-title-font-weight: 600;

    /* Progress Spinner */
    --p-progressspinner-stroke-color: var(--primary-600);
    --p-progressspinner-stroke-width: 3;
    --p-progressspinner-animation-duration: 1s;

    /* Overlay Badge */
    --p-overlaybadge-background: var(--primary-600);
    --p-overlaybadge-color: var(--primary-0);
    --p-overlaybadge-border-radius: 50%;
    --p-overlaybadge-font-weight: 700;
    --p-overlaybadge-min-width: 1.5rem;
    --p-overlaybadge-height: 1.5rem;
    --p-overlaybadge-dot-width: 0.5rem;
    --p-overlaybadge-dot-height: 0.5rem;

    /* Input Number */
    --p-inputnumber-button-width: 2rem;
    --p-inputnumber-button-height: 100%;
    --p-inputnumber-button-background: var(--surface-0);
    --p-inputnumber-button-border-color: var(--surface-300);
    --p-inputnumber-button-color: var(--surface-600);
    --p-inputnumber-button-hover-background: var(--surface-100);
    --p-inputnumber-button-hover-color: var(--surface-900);
    --p-inputnumber-button-active-background: var(--surface-200);
    --p-inputnumber-button-active-color: var(--surface-900);
    --p-inputnumber-button-padding-x: var(--form-field-padding-x);
    --p-inputnumber-button-padding-y: var(--form-field-padding-y);
    --p-inputnumber-button-border-radius: var(--border-radius-lg);
    --p-inputnumber-increment-button-border-radius: var(--border-radius-lg);
    --p-inputnumber-decrement-button-border-radius: var(--border-radius-lg);

    /* Input OTP */
    --p-inputotp-cell-width: 3rem;
    --p-inputotp-cell-height: 3rem;
    --p-inputotp-cell-spacing: 0.5rem;
    --p-inputotp-cell-border-radius: var(--border-radius-md);
    --p-inputotp-cell-background: var(--surface-0);
    --p-inputotp-cell-border-color: var(--surface-300);
    --p-inputotp-cell-hover-border-color: var(--primary-400);
    --p-inputotp-cell-focus-border-color: var(--primary-500);
    --p-inputotp-cell-selected-background: var(--surface-100);
    --p-inputotp-cell-selected-border-color: var(--primary-500);

    /* Input Group */
    --p-inputgroup-addon-background: var(--surface-50);
    --p-inputgroup-addon-border-color: var(--surface-500);
    --p-inputgroup-addon-color: var(--surface-600);
    --p-inputgroup-addon-border-radius: var(--border-radius-lg);
    --p-inputgroup-addon-padding-x: var(--form-field-padding-x);
    --p-inputgroup-addon-padding-y: var(--form-field-padding-y);

    /* Icon Field */
    --p-iconfield-icon-color: var(--icon-color-primary);
    --p-iconfield-icon-width: 2.5rem;
    --p-iconfield-icon-font-size: 1rem;

    /* Image Compare */
    --p-imagecompare-handle-background: var(--primary-500);
    --p-imagecompare-handle-color: var(--primary-0);
    --p-imagecompare-handle-width: 2.5rem;
    --p-imagecompare-handle-height: 2.5rem;
    --p-imagecompare-handle-border-radius: 50%;
    --p-imagecompare-range-background: var(--surface-200);
    --p-imagecompare-range-border-radius: var(--border-radius-md);

    /* Meter Group */
    --p-metergroup-background: var(--surface-100);
    --p-metergroup-border-radius: var(--border-radius-md);
    --p-metergroup-height: 1.5rem;
    --p-metergroup-segment-border-radius: var(--border-radius-md);
    --p-metergroup-segment-color: var(--surface-0);
    --p-metergroup-segment-font-size: 0.875rem;
    --p-metergroup-segment-font-weight: 600;

    /* Knob */
    --p-knob-stroke: var(--primary-500);
    --p-knob-value-color: var(--surface-700);
    --p-knob-value-font-size: 1.5rem;
    --p-knob-value-font-weight: 600;
    --p-knob-range-color: var(--surface-200);

    /* Float Label */
    --p-floatlabel-label-color: var(--surface-600);
    --p-floatlabel-focus-label-color: var(--primary-700);
    --p-floatlabel-font-size: 0.875rem;
    --p-floatlabel-transition-duration: 0.2s;
    --p-floatlabel-padding-x: var(--form-field-padding-x);
    --p-floatlabel-padding-y: var(--form-field-padding-y);

    /* Inline Message */
    --p-inlinemessage-padding: 0.5rem 0.75rem;
    --p-inlinemessage-border-radius: var(--border-radius-md);
    --p-inlinemessage-font-size: 0.875rem;
    --p-inlinemessage-info-background: var(--blue-50);
    --p-inlinemessage-info-border-color: var(--blue-100);
    --p-inlinemessage-info-color: var(--blue-900);
    --p-inlinemessage-success-background: var(--green-50);
    --p-inlinemessage-success-border-color: var(--green-100);
    --p-inlinemessage-success-color: var(--green-900);
    --p-inlinemessage-warning-background: var(--yellow-50);
    --p-inlinemessage-warning-border-color: var(--yellow-100);
    --p-inlinemessage-warning-color: var(--yellow-900);
    --p-inlinemessage-error-background: var(--red-50);
    --p-inlinemessage-error-border-color: var(--red-100);
    --p-inlinemessage-error-color: var(--red-900);

    /* Confirm Popup */
    --p-confirmpopup-background: var(--surface-0);
    --p-confirmpopup-border-color: var(--surface-150);
    --p-confirmpopup-border-radius: var(--border-radius-lg);
    --p-confirmpopup-padding: 1.25rem;
    --p-confirmpopup-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    --p-confirmpopup-message-margin: 0 0 1rem 0;
    --p-confirmpopup-message-color: var(--surface-850);
    --p-confirmpopup-message-font-size: 1rem;

    /* Context Menu */
    --p-contextmenu-background: var(--surface-0);
    --p-contextmenu-border-color: var(--surface-150);
    --p-contextmenu-border-radius: var(--border-radius-md);
    --p-contextmenu-padding: 0.5rem 0;
    --p-contextmenu-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    --p-contextmenu-item-padding: 0.75rem 1rem;
    --p-contextmenu-item-color: var(--surface-700);
    --p-contextmenu-item-hover-background: var(--surface-100);
    --p-contextmenu-item-hover-color: var(--surface-900);
    --p-contextmenu-separator-color: var(--surface-150);

    /* Data View */
    --p-dataview-content-padding: 1rem;
    --p-dataview-grid-gutter: 1rem;
    --p-dataview-header-background: var(--surface-50);
    --p-dataview-header-padding: 1.25rem;
    --p-dataview-header-border-color: var(--surface-200);
    --p-dataview-footer-background: var(--surface-50);
    --p-dataview-footer-padding: 1.25rem;
    --p-dataview-footer-border-color: var(--surface-200);

    /* Cascade Select */
    --p-cascadeselect-background: var(--surface-0);
    --p-cascadeselect-border-color: var(--surface-500);
    --p-cascadeselect-border-radius: var(--border-radius-md);
    --p-cascadeselect-padding: 0.625rem 0.75rem;
    --p-cascadeselect-hover-border-color: var(--surface-600);
    --p-cascadeselect-focus-border-color: var(--primary-600);
    --p-cascadeselect-panel-background: var(--surface-0);
    --p-cascadeselect-panel-border-color: var(--surface-150);
    --p-cascadeselect-panel-border-radius: var(--border-radius-md);
    --p-cascadeselect-panel-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    --p-cascadeselect-item-padding: 0.75rem 1rem;
    --p-cascadeselect-item-color: var(--surface-700);
    --p-cascadeselect-item-hover-background: var(--surface-100);
    --p-cascadeselect-item-hover-color: var(--surface-900);

    /* Listbox */
    --p-listbox-background: var(--surface-0);
    --p-listbox-border-color: var(--surface-500);
    --p-listbox-border-radius: var(--border-radius-md);
    --p-listbox-padding: 0.5rem 0;
    --p-listbox-item-padding: 0.75rem 1rem;
    --p-listbox-item-color: var(--surface-700);
    --p-listbox-item-hover-background: var(--surface-100);
    --p-listbox-item-hover-color: var(--surface-900);
    --p-listbox-item-selected-background: var(--primary-50);
    --p-listbox-item-selected-color: var(--primary-700);
}

.p-tooltip .p-tooltip-text {
    font-size: 13px;
    font-weight: 500;

  }

  .p-tooltip .p-tooltip-arrow {
    display: none;

  }

  .loading-spinner {
    animation-duration: .7s;
  }