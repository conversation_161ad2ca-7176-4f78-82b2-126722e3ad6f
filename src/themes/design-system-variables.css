/* Variables  */
:root {
    --color-monochrome-white: #ffffff;
    --color-monochrome-900: #1b1c1d;
    --color-monochrome-50: #f3f4f4;
    --color-monochrome-100: #e7e8e9;
    --color-monochrome-200: #cfd1d3;
    --color-monochrome-400: #9fa3a8;
    --color-monochrome-300: #b7babe;
    --color-monochrome-500: #878c92;
    --color-monochrome-600: #6c7075;
    --color-monochrome-700: #515458;
    --color-monochrome-800: #36383a;
    --color-monochrome-850: #282a2c;
    --color-blue-500: #0085ff;
    --color-blue-700: #005099;
    --color-blue-600: #006acc;
    --color-blue-800: #003566;
    --color-blue-400: #339dff;
    --color-blue-300: #66b6ff;
    --color-blue-200: #99ceff;
    --color-blue-100: #cce7ff;
    --color-blue-50: #e6f3ff;
    --color-blue-900: #001b33;
    --color-monochrome-150: #dbddde;
    --spacing-1: 2px;
    --spacing-2: 4px;
    --spacing-3: 8px;
    --spacing-4: 16px;
    --spacing-5: 24px;
    --spacing-6: 32px;
    --radius-xs: 2px;
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 16px;
    --radius-xl: 100px;
    --color-blue-650: #005db3;
    --color-blue-550: #0078e6;
    --color-bluegray-900: #1d262f;
    --color-bluegray-800: #3a4d5e;
    --color-bluegray-700: #58738d;
    --color-bluegray-600: #759abc;
    --color-bluegray-500: #92c0eb;
    --color-bluegray-400: #a8cdef;
    --color-bluegray-300: #bed9f3;
    --color-bluegray-200: #d3e6f7;
    --color-bluegray-100: #e9f2fb;
    --color-bluegray-50: #f4f9fd;
    --color-bluegray-150: #deecf9;
    --color-green-900: #0c2814;
    --color-green-800: #195028;
    --color-green-700: #25783d;
    --color-green-600: #32a051;
    --color-green-500: #3ec865;
    --color-green-400: #65d384;
    --color-green-300: #8bdea3;
    --color-green-200: #b2e9c1;
    --color-green-150: #c5efd1;
    --color-green-100: #d8f4e0;
    --color-green-50: #ecfaf0;
    --color-orange-900: #31180b;
    --color-orange-800: #622e17;
    --color-orange-700: #924620;
    --color-orange-600: #c35d2b;
    --color-orange-500: #f47536;
    --color-orange-400: #f6915d;
    --color-orange-300: #f8ac86;
    --color-orange-200: #fbc8af;
    --color-orange-150: #fcd6c3;
    --color-orange-100: #fde3d7;
    --color-orange-50: #fef1eb;
    --color-yellow-900: #302000;
    --color-yellow-800: #604101;
    --color-yellow-700: #906101;
    --color-yellow-600: #c08202;
    --color-yellow-500: #f0a202;
    --color-yellow-400: #f3b535;
    --color-yellow-300: #f6c767;
    --color-yellow-200: #f9da9a;
    --color-yellow-150: #fbe3b3;
    --color-yellow-100: #fceccc;
    --color-yellow-50: #fef6e6;
    --spacing-7: 48px;
    --color-red-900: #2b080b;
    --color-red-800: #560f16;
    --color-red-700: #811722;
    --color-red-600: #ac1e2d;
    --color-red-500: #d72638;
    --color-red-400: #df5160;
    --color-red-300: #e77d88;
    --color-red-200: #efa8af;
    --color-red-150: #f3bec3;
    --color-red-100: #f7d4d7;
    --color-red-50: #fbe9eb;
    --color-purple-900: #161628;
    --color-purple-800: #2c2b4f;
    --color-purple-700: #414177;
    --color-purple-600: #57569e;
    --color-purple-500: #6d6cc6;
    --color-purple-400: #8a89d1;
    --color-purple-300: #a7a7dd;
    --color-purple-200: #c5c4e8;
    --color-purple-150: #d3d3ee;
    --color-purple-100: #e2e2f4;
    --color-purple-50: #f0f0f9;
    --color-other-teal-100: #e2f4f4;
    --color-other-magenta-100: #f4e2f4;
    --color-monochrome-750: #444649;
    --color-other-transparent-0: #00000000;
    --color-other-transparent-50: #00000080;
    --color-other-transparent-20: #00000033;
    --color-special-logo-secondary: #868f99;
    --color-monochrome-25: #fafafa;
    --spacing-8: 64px;
    --color-monochrome-black: #000000;
    --color-other-teal-900: #143434;
    --color-other-magenta-900: #341434;
    --color-orange-650: #ab5226;
    --color-orange-625: #b85829;
    --stroke-thickness-primary: 1px;
    --stroke-thickness-secondary: 2px;
    --primary-color: #007ad9;
    --primary-color-text: #ffffff;
}

/* Aliases  */
:root {
    --color-text-main-primary: var(--color-monochrome-850);
    --color-text-main-secondary: var(--color-monochrome-600);
    --color-text-main-invert: var(--color-monochrome-white);
    --color-surface-primary: var(--color-monochrome-white);
    --color-surface-secondary: var(--color-monochrome-25);
    --color-button-primary-static: var(--color-blue-600);
    --color-border-primary: var(--color-monochrome-150);
    --color-border-secondary: var(--color-monochrome-500);
    --radius-full: var(--radius-xl);
    --radius-standard: var(--radius-sm);
    --radius-rounded: var(--radius-md);
    --spacing-xs: var(--spacing-1);
    --spacing-sm: var(--spacing-2);
    --spacing-md: var(--spacing-3);
    --spacing-lg: var(--spacing-4);
    --spacing-xl: var(--spacing-5);
    --dimensions-standard: var(--spacing-6);
    --color-text-link-static: var(--color-blue-650);
    --color-text-link-pressed: var(--color-blue-700);
    --color-text-link-hover: var(--color-blue-600);
    --color-button-primary-hover: var(--color-blue-550);
    --color-button-primary-pressed: var(--color-blue-650);
    --color-button-secondary-surface-static: var(--color-monochrome-white);
    --color-button-secondary-surface-hover: var(--color-bluegray-50);
    --color-button-secondary-surface-pressed: var(--color-bluegray-150);
    --color-button-secondary-border-static: var(--color-monochrome-500);
    --color-icon-main-static-primary: var(--color-monochrome-600);
    --color-button-secondary-border-hover: var(--color-blue-600);
    --color-button-secondary-border-pressed-focus: var(--color-blue-650);
    --color-icon-main-hover: var(--color-blue-600);
    --color-icon-main-pressed: var(--color-blue-700);
    --color-icon-main-toggled: var(--color-monochrome-850);
    --dimensions-wide: var(--spacing-7);
    --color-icon-status-critical: var(--color-red-500);
    --color-text-status-critical: var(--color-red-500);
    --color-text-status-warning: var(--color-yellow-700);
    --color-text-status-success: var(--color-green-700);
    --color-icon-status-warning: var(--color-yellow-600);
    --color-icon-status-success: var(--color-green-600);
    --color-icon-status-notification: var(--color-orange-500);
    --spacing-2xl: var(--spacing-6);
    --color-tags-info: var(--color-bluegray-150);
    --color-tags-category: var(--color-purple-100);
    --color-tags-active: var(--color-green-50);
    --color-tags-inactive: var(--color-yellow-100);
    --color-tags-danger: var(--color-red-100);
    --color-tags-generic-a: var(--color-monochrome-100);
    --color-tags-case-notification: var(--color-orange-150);
    --color-special-misc-avatar1: var(--color-other-teal-100);
    --color-special-misc-avatar2: var(--color-other-magenta-100);
    --color-tags-generic-b: var(--color-monochrome-600);
    --color-icon-main-invert: var(--color-monochrome-white);
    --color-icon-main-static-secondary: var(--color-blue-650);
    --color-special-case_tile-border-primary: var(--color-orange-500);
    --dimensions-small: var(--spacing-3);
    --color-special-case_tile-surface-hover: var(--color-orange-50);
    --color-special-case_tile-surface-pressed: var(--color-orange-100);
    --color-special-case_tile-surface-claimed: var(--color-orange-625);
    --color-special-misc-tooltip: var(--color-monochrome-750);
    --dimensions-medium: var(--spacing-4);
    --color-special-misc-transparent: var(--color-other-transparent-0);
    --color-special-misc-modal_overlay: var(--color-other-transparent-50);
    --color-border-error: var(--color-red-500);
    --color-special-misc-color_picker: var(--color-other-transparent-20);
    --color-special-misc-folder_icon: var(--color-yellow-200);
    --color-options_item-hover: var(--color-bluegray-100);
    --color-options_item-pressed: var(--color-bluegray-200);
    --color-options_item-selected: var(--color-bluegray-150);
    --color-special-logo-primary: var(--color-blue-500);
    --color-special-logo-tertirary: var(--color-monochrome-black);
    --spacing-3xl: var(--spacing-7);
    --color-surface-tertiary: var(--color-monochrome-50);
    --color-special-misc-switch: var(--color-monochrome-500);
    --dimensions-icon: var(--spacing-5);
    --spacing-4xl: var(--spacing-8);
    --dimensions-xtra-small: var(--spacing-2);
    --radius-slight: var(--radius-xs);
}

@media (prefers-color-scheme: dark) {
    :root {
        /* --color-text-main-primary: var(--color-monochrome-white); */
        --color-text-main-secondary: var(--color-monochrome-200);
        --color-text-main-invert: var(--color-monochrome-white);
        --color-surface-primary: var(--color-monochrome-800);
        --color-surface-secondary: var(--color-monochrome-750);
        --color-button-primary-static: var(--color-blue-500);
        --color-border-primary: var(--color-monochrome-700);
        --color-border-secondary: var(--color-monochrome-400);
        --radius-full: var(--radius-xl);
        --radius-standard: var(--radius-sm);
        --radius-rounded: var(--radius-md);
        --spacing-xs: var(--spacing-1);
        --spacing-sm: var(--spacing-2);
        --spacing-md: var(--spacing-3);
        --spacing-lg: var(--spacing-4);
        --spacing-xl: var(--spacing-5);
        --dimensions-standard: var(--spacing-6);
        --color-text-link-static: var(--color-blue-300);
        --color-text-link-pressed: var(--color-blue-400);
        --color-text-link-hover: var(--color-blue-200);
        --color-button-primary-hover: var(--color-blue-500);
        --color-button-primary-pressed: var(--color-blue-600);
        --color-button-secondary-surface-static: var(--color-monochrome-800);
        --color-button-secondary-surface-hover: var(--color-monochrome-700);
        --color-button-secondary-surface-pressed: var(--color-monochrome-750);
        --color-button-secondary-border-static: var(--color-monochrome-600);
        --color-icon-main-static-primary: var(--color-monochrome-100);
        --color-button-secondary-border-hover: var(--color-blue-500);
        --color-button-secondary-border-pressed-focus: var(--color-blue-550);
        --color-icon-main-hover: var(--color-blue-500);
        --color-icon-main-pressed: var(--color-blue-600);
        --color-icon-main-toggled: var(--color-monochrome-white);
        --dimensions-wide: var(--spacing-7);
        --color-icon-status-critical: var(--color-red-400);
        --color-text-status-critical: var(--color-red-400);
        --color-text-status-warning: var(--color-yellow-600);
        --color-text-status-success: var(--color-green-600);
        --color-icon-status-warning: var(--color-yellow-500);
        --color-icon-status-success: var(--color-green-500);
        --color-icon-status-notification: var(--color-orange-400);
        --spacing-2xl: var(--spacing-6);
        --color-tags-info: var(--color-bluegray-700);
        --color-tags-category: var(--color-purple-700);
        --color-tags-active: var(--color-green-700);
        --color-tags-inactive: var(--color-yellow-800);
        --color-tags-danger: var(--color-red-700);
        --color-tags-generic-a: var(--color-monochrome-700);
        --color-tags-case-notification: var(--color-orange-700);
        --color-special-misc-avatar1: var(--color-other-teal-900);
        --color-special-misc-avatar2: var(--color-other-magenta-900);
        --color-tags-generic-b: var(--color-monochrome-500);
        --color-icon-main-invert: var(--color-monochrome-white);
        --color-icon-main-static-secondary: var(--color-blue-400);
        --color-special-case_tile-border-primary: var(--color-orange-400);
        --dimensions-small: var(--spacing-3);
        --color-special-case_tile-surface-hover: var(--color-orange-800);
        --color-special-case_tile-surface-pressed: var(--color-orange-900);
        --color-special-case_tile-surface-claimed: var(--color-orange-500);
        --color-special-misc-tooltip: var(--color-monochrome-850);
        --dimensions-medium: var(--spacing-4);
        --color-special-misc-transparent: var(--color-other-transparent-0);
        --color-special-misc-modal_overlay: var(--color-other-transparent-50);
        --color-border-error: var(--color-red-400);
        --color-special-misc-color_picker: var(--color-other-transparent-20);
        --color-special-misc-folder_icon: var(--color-yellow-800);
        --color-options_item-hover: var(--color-bluegray-600);
        --color-options_item-pressed: var(--color-bluegray-700);
        --color-options_item-selected: var(--color-bluegray-800);
        --color-special-logo-primary: var(--color-blue-500);
        --color-special-logo-tertirary: var(--color-monochrome-white);
        --spacing-3xl: var(--spacing-7);
        --color-surface-tertiary: var(--color-monochrome-700);
        --color-special-misc-switch: var(--color-monochrome-750);
        --dimensions-icon: var(--spacing-5);
        --spacing-4xl: var(--spacing-8);
        --dimensions-xtra-small: var(--spacing-3);
        --radius-slight: var(--radius-xs);
    }
}
