declare module 'primevue/button' {
    import { DefineComponent } from 'vue';
    const Button: DefineComponent<{
        label?: string;
        icon?: string;
        iconPos?: string;
        badge?: string;
        disabled?: boolean;
        loading?: boolean;
        outlined?: boolean;
        text?: boolean;
        id?: string;
        class?: string | Record<string, any>;
        style?: string | object;
        dataTestid?: string;
        'data-testid'?: string;
        onClick?: (event: Event) => void;
    }>;
    export default Button;
}

declare module 'primevue/inputtext' {
    import { DefineComponent } from 'vue';
    const InputText: DefineComponent<{
        modelValue?: string | number;
        type?: string;
        size?: string;
        disabled?: boolean;
        invalid?: boolean;
        placeholder?: string;
        id?: string;
        class?: string | Record<string, any>;
        style?: string | object;
        dataTestid?: string;
        'data-testid'?: string;
    }>;
    export default InputText;
}

declare module 'primevue/inputgroup' {
    import { DefineComponent } from 'vue';
    const InputGroup: DefineComponent<{
        id?: string;
        class?: string | Record<string, any>;
        style?: string | object;
        dataTestid?: string;
        'data-testid'?: string;
    }>;
    export default InputGroup;
}
