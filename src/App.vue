<script setup lang="ts">
import { ref } from 'vue';
import InputText from 'primevue/inputtext';
import Button from 'primevue/button';
import BravoDataView from './components/BravoDataView.vue';
import BravoTag from './components/BravoTag.vue';

const inputValue = ref<string>('');

const products = [
    {
        id: '1000',
        code: 'f230fh0g3',
        name: 'Bamboo Watch',
        description: 'Product Description',
        image: 'bamboo-watch.jpg',
        price: 65,
        category: 'Accessories',
        quantity: 24,
        inventoryStatus: 'INSTOCK',
        rating: 5,
    },
    {
        id: '1001',
        code: 'nvklal433',
        name: 'Black Watch',
        description: 'Product Description',
        image: 'black-watch.jpg',
        price: 72,
        category: 'Accessories',
        quantity: 61,
        inventoryStatus: 'OUTOFSTOCK',
        rating: 4,
    },
];

const getSeverity = (product: any) => {
    switch (product.inventoryStatus) {
        case 'INSTOCK':
            return 'success';
        case 'LOWSTOCK':
            return 'warning';
        case 'OUTOFSTOCK':
            return 'danger';
        default:
            return null;
    }
};
</script>

<template>
  <div class="p-4">
    <div class="flex align-items-center gap-2 mb-4">
      <Button
        icon="pi pi-search"
        aria-label="Search"
      />
      <InputText
        v-model="inputValue"
        placeholder="Enter text..."
      />
      <Button
        label="Submit"
        severity="primary"
      />
    </div>

    <BravoDataView
      :value="products"
      layout="list"
      data-key="id"
    >
      <template #list="slotProps">
        <div class="flex flex-col">
          <div
            v-for="(item, index) in slotProps.items"
            :key="item.id"
          >
            <div
              class="flex flex-col sm:flex-row sm:items-center p-6 gap-4"
              :class="{ 'border-t border-surface-200 dark:border-surface-700': index !== 0 }"
            >
              <div class="md:w-40 relative">
                <img
                  class="block xl:block mx-auto rounded w-full"
                  :src="`https://primefaces.org/cdn/primevue/images/product/${item.image}`"
                  :alt="item.name"
                />
                <div
                  class="absolute bg-black/70 rounded-border"
                  style="left: 4px; top: 4px"
                >
                  <BravoTag
                    :value="item.inventoryStatus"
                    :severity="
                      getSeverity(item) as 'success' | 'danger' | 'info' | 'warn' | undefined
                    "
                  ></BravoTag>
                </div>
              </div>
              <div class="flex flex-col md:flex-row justify-between md:items-center flex-1 gap-6">
                <div class="flex flex-row md:flex-col justify-between items-start gap-2">
                  <div>
                    <span class="font-medium text-surface-500 dark:text-surface-400 text-sm">{{
                      item.category
                    }}</span>
                    <div class="text-lg font-medium mt-2">
                      {{ item.name }}
                    </div>
                  </div>
                  <div
                    class="bg-surface-100 p-1"
                    style="border-radius: 30px"
                  >
                    <div
                      class="bg-surface-0 flex items-center gap-2 justify-center py-1 px-2"
                      style="
                                                border-radius: 30px;
                                                box-shadow:
                                                    0px 1px 2px 0px rgba(0, 0, 0, 0.04),
                                                    0px 1px 2px 0px rgba(0, 0, 0, 0.06);
                                            "
                    >
                      <span class="text-surface-900 font-medium text-sm">{{ item.rating }}</span>
                      <i class="pi pi-star-fill text-yellow-500"></i>
                    </div>
                  </div>
                </div>
                <div class="flex flex-col md:items-end gap-8">
                  <span class="text-xl font-semibold">${{ item.price }}</span>
                  <div class="flex flex-row-reverse md:flex-row gap-2">
                    <Button
                      icon="pi pi-heart"
                      outlined
                    ></Button>
                    <Button
                      icon="pi pi-shopping-cart"
                      label="Buy Now"
                      :disabled="item.inventoryStatus === 'OUTOFSTOCK'"
                      class="flex-auto md:flex-initial whitespace-nowrap"
                    ></Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </BravoDataView>
  </div>
</template>

<style scoped>
.button-style {
    margin-top: 50px;
    float: right;
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 10px;
    margin-top: 100px;
}

.flex {
    display: flex;
}

.align-items-center {
    align-items: center;
}

.gap-2 {
    gap: 0.5rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.p-4 {
    padding: 1rem;
}
</style>
