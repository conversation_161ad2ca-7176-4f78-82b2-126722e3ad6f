<script setup lang="ts">
import Accordion from 'primevue/accordion';
</script>

<template>
  <Accordion
    class="bravo-block"
    v-bind="$attrs"
  >
    <slot></slot>
  </Accordion>
</template>

<style lang="scss" scoped>
.bravo-block {
    --p-accordion-header-active-background: #fff;
    --p-accordion-content-padding: 1rem 1.5rem 2rem 1.5rem;
    --p-accordion-header-padding: 1rem 1.5rem;
}

.bravo-block {
    &:deep(.p-accordionheader-toggle-icon) {
        transition: transform 1s ease;
    }

    &:deep(.p-accordionheader) {
        font-size: 13px;
        font-weight: 600;
        text-transform: uppercase;

        .p-accordionheader-toggle-icon {
            transform: rotate(-90deg);
        }
    }

    &:deep(.p-accordionpanel-active > .p-accordionheader .p-accordionheader-toggle-icon) {
        transform: rotate(180deg);
    }
}
</style>
