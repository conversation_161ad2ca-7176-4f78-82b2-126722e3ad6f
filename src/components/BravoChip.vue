<script setup lang="ts">
import Chip from 'primevue/chip';

interface Props {
    label?: string;
    icon?: string;
    image?: string;
    removable?: boolean;
}

defineProps<Props>();
defineEmits<{
    (e: 'remove'): void;
}>();
</script>

<template>
  <Chip
    class="bravo-chip"
    :label="label"
    :icon="icon"
    :image="image"
    :removable="removable"
    v-bind="$attrs"
    @remove="$emit('remove')"
  >
    <slot></slot>
  </Chip>
</template>

<style lang="scss" scoped></style>
