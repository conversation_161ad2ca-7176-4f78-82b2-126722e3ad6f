<script setup lang="ts">
import Toolbar from 'primevue/toolbar';
</script>

<template>
  <Toolbar
    class="bravo-toolbar"
    v-bind="$attrs"
  >
    <template
      v-if="$slots.start"
      #start
    >
      <slot name="start"></slot>
    </template>
    <template
      v-if="$slots.center"
      #center
    >
      <slot name="center"></slot>
    </template>
    <template
      v-if="$slots.end"
      #end
    >
      <slot name="end"></slot>
    </template>
  </Toolbar>
</template>

<style lang="scss" scoped></style>
