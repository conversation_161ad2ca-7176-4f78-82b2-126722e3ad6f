<script setup lang="ts">
import BravoSelectField from './BravoSelectField.vue';

defineProps({
    label: {
        type: String,
        default: '',
    },
    filterOptions: {
        type: Array as () => Array<{ [key: string]: string | number }>,
        default: () => [],
    },
    optionLabel: {
        type: String,
        default: 'label',
    },
    optionValue: {
        type: String,
        default: 'value',
    },
    placeholder: {
        type: String,
        default: 'Select option',
    },
    id: {
        type: String,
        required: true,
    },
    dataTestId: {
        type: String,
        required: true,
    },
});

const emit = defineEmits(['update:modelValue', 'filter-change', 'focus', 'blur']);

const handleChange = (value: string | number | null) => {
    emit('update:modelValue', value);
    emit('filter-change', value);
};

const handleFocus = (event: Event) => {
    emit('focus', event);
};

const handleBlur = (event: Event) => {
    emit('blur', event);
};
</script>

<template>
  <div class="bravo-filter-select">
    <label
      v-if="label"
      class="bravo-filter-select-label"
    >{{ label }}</label>
    <BravoSelectField
      v-bind="$attrs"
      :id="id"
      class="bravo-filter-select-field"
      :options="filterOptions"
      :option-label="optionLabel"
      :option-value="optionValue"
      :placeholder="placeholder"
      :data-test-id="dataTestId"
      @update:model-value="handleChange"
      @focus="handleFocus"
      @blur="handleBlur"
    >
      <!-- Forward all slots to the underlying BravoSelectField component -->
      <template
        v-for="(_, name) in $slots"
        #[name]="slotData"
      >
        <slot
          :name="name"
          v-bind="slotData || {}"
        />
      </template>
    </BravoSelectField>
  </div>
</template>

<style lang="scss">
.bravo-filter-select {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: fit-content;

    &-label {
        font-weight: 500;
        font-size: 0.875rem;
        color: #333;
    }

    &-field {
        width: fit-content;
        min-width: auto;

        .p-select {
            border: none !important;
            border-radius: 4px;
            transition: background-color 0.2s;
            background: transparent;
            box-shadow: none !important;
            width: fit-content;
            min-width: auto;

            &:hover {
                background-color: #f5f5f5;
            }

            &:focus,
            &.p-focus {
                box-shadow: none !important;
                background-color: #f5f5f5;
            }

            .p-select-label {
                padding: 0.5rem 0.75rem;
                color: #333;
                white-space: nowrap;
                padding-right: 0.25rem;
            }

            .p-select-dropdown {
                width: 2rem;
                color: #666;
                padding: 0 0.5rem;
                flex-shrink: 0;
            }
        }

        .p-select-overlay {
            border-radius: 4px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            min-width: 200px;

            .p-select-list {
                padding: 0.25rem 0;

                .p-select-option {
                    padding: 0.5rem 0.75rem;
                    color: #333;
                    border-radius: 0;
                    transition: background-color 0.2s;

                    &:hover {
                        background-color: #f5f5f5;
                    }

                    &.p-selected {
                        background-color: #e6f7ff;
                        color: #0366d6;
                    }
                }
            }
        }

        .bravo-selectfield-wrapper {
            width: fit-content;
        }

        .bravo-selectfield {
            width: fit-content !important;
            min-width: auto !important;
        }
    }
}
</style> 