<script setup lang="ts">
import Inplace from 'primevue/inplace';

interface Props {
    active?: boolean;
    closable?: boolean;
    disabled?: boolean;
}

defineProps<Props>();
defineEmits<{
    (e: 'update:active', value: boolean): void;
    (e: 'open'): void;
    (e: 'close'): void;
}>();
</script>

<template>
  <Inplace
    class="bravo-inplace"
    :active="active"
    :closable="closable"
    :disabled="disabled"
    v-bind="$attrs"
    @update:active="$emit('update:active', $event)"
    @open="$emit('open')"
    @close="$emit('close')"
  >
    <template #display>
      <slot name="display"></slot>
    </template>
    <template #content>
      <slot name="content"></slot>
    </template>
  </Inplace>
</template>

<style lang="scss" scoped></style>
