<script setup lang="ts">
import TieredMenu from 'primevue/tieredmenu';
import { ref } from 'vue';

const menuRef = ref();

// Expose the toggle method to parent components
defineExpose({
    toggle: (event: Event) => menuRef.value?.toggle(event),
    show: (event: Event) => menuRef.value?.show(event),
    hide: () => menuRef.value?.hide(),
});
</script>

<template>
  <TieredMenu
    ref="menuRef"
    class="bravo-tiered-menu"
    v-bind="$attrs"
  >
    <slot></slot>
  </TieredMenu>
</template>

<style lang="scss" scoped></style>
