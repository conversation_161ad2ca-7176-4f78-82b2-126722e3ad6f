<script setup lang="ts">
import Checkbox from 'primevue/checkbox';
import { BravoLabel } from './index';

interface CheckboxProps {
    withLabel: boolean;
    label: string;
    id: string;
    dataTestId: string;
    forElement: string;
    isRequired: boolean;
    className?: string;
    toolTipText?: string;
    toolTipPosition?: string;
    iconName?: string;
}

const props = withDefaults(defineProps<CheckboxProps>(), {
    withLabel: false,
    label: '',
    id: '',
    dataTestId: '',
    forElement: '',
});
</script>

<template>
  <div :class="props.withLabel ? 'bravo-checkbox-wrapper' : ''">
    <Checkbox
      v-bind="$attrs"
      :id="props.id"
      class="bravo-checkbox"
      :data-testid="props.dataTestId"
    >
      <slot></slot>
    </Checkbox>
    <BravoLabel
      v-if="props.withLabel"
      :id="props.id + '-label'"
      :class-name="props.className"
      :text="props.label"
      :for-element="props.forElement"
      :is-required="props.isRequired"
      :icon-name="props.iconName"
      :data-testid="props.dataTestId + '-label'"
      :tool-tip-text="props.toolTipText"
      :tool-tip-position="props.toolTipPosition"
    />
  </div>
</template>

<style lang="scss" scoped>
.bravo-checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
}
</style>
