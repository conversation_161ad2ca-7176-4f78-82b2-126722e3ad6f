<script setup lang="ts">
import CxmIcon from '../CxmIcon/CxmIcon.vue';

type BaseLabelProps = {
    text: string;
    id?: string;
    dataTestId?: string;
    forElement?: string;
    isRequired?: boolean;
    iconName?: string;
    toolTipText?: string;
    toolTipPosition?: string;
    mode?: 'primary' | 'secondary' | 'dark';
    className?: string;
};

const props = withDefaults(defineProps<BaseLabelProps>(), {});
</script>

<template>
  <div class="wrapper">
    <span :class="`${props.className ? props.className : ''} label`">
      <label
        :id="props.id"
        :for="props.forElement"
        :data-testid="props.dataTestId ? props.dataTestId : props.id"
        :class="`${props.mode === 'primary' ? 'primary-label' : props.mode === 'dark' ? 'dark-label' : 'secondary-label'} ${props?.isRequired ? 'required' : ''}`"
      >{{ props.text }}</label>
    </span>
    <span
      v-if="props?.iconName"
      v-tooltip.bottom="$props.toolTipText"
    >
      <CxmIcon
        :id="props.id + '-icon'"
        :name="props.iconName ? props.iconName : 'info'"
        class="icon"
      />
    </span>
  </div>
</template>

<style lang="scss" scoped>
.primary-label {
    color: #005db2;
}
.secondary-label {
    color: #6c7075;
}
.dark-label {
    color: #303336;
}
.label {
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    word-break: break-all;
}

.required::after {
    color: #a11f26;
    font-size: 13px;
    content: '*';
    font-style: normal;
    font-weight: 400;
    padding-left: 2px;
    line-height: normal;
}

.icon {
    width: 14px;
    height: 14px;
    color: #006acc;
    margin-bottom: 2px;
}

.wrapper {
    display: grid;
    grid-template-columns: max-content max-content;
    grid-gap: 4px;
}
</style>
