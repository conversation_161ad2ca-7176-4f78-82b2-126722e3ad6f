<script setup lang="ts">
import CxmIcon from './CxmIcon/CxmIcon.vue';

type BaseLabelProps = {
    text: string;
    id?: string;
    dataTestId?: string;
    forElement?: string;
    isRequired?: boolean;
    iconName?: string;
    toolTipText?: string;
    toolTipPosition?: string;
    mode?: 'primary' | 'secondary' | 'dark';
    className?: string;
};

const props = withDefaults(defineProps<BaseLabelProps>(), {});
</script>

<template>
  <div class="wrapper">
    <span :class="`${props.className ? props.className : ''} label`">
      <label
        :id="props.id"
        :for="props.forElement"
        :data-testid="props.dataTestId ? props.dataTestId : props.id"
        :class="`${props.mode === 'primary' ? 'primary-label' : props.mode === 'dark' ? 'dark-label' : props.mode === 'secondary' ? 'secondary-label' : 'default-label'} ${props?.isRequired ? 'required' : ''}`"
      >{{ props.text }}</label>
    </span>
    <span
      v-if="props?.iconName"
      v-tooltip.bottom="$props.toolTipText"
      class="icon-container"
    >
      <CxmIcon
        :id="props.id + '-icon'"
        :name="props.iconName ? props.iconName : 'info'"
        class="icon"
      />
    </span>
  </div>
</template>


<style lang="scss" scoped>
.primary-label {
    color: var(--text-color-secondary);
}
.secondary-label {
    color: var(--text-color-primary);
}
.dark-label {
    color: var(--text-color-primary);
}
.default-label {
    color: var(--text-color-secondary);
}
.label {
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    word-break: break-all;
    padding: 6px 0;
    display: inline-block;
}

.required::after {
    color: var(--red-600);
    font-size: 14px;
    content: '*';
    font-style: normal;
    font-weight: 400;
    padding-left: 2px;
    line-height: normal;
}

.icon-container {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
}

.icon {
    width: 14px;
    height: 14px;
    color: var(--primary-600);
    margin-bottom: 2px;
}

.wrapper {
    display: grid;
    grid-template-columns: max-content max-content;
    grid-gap: 4px;

    &:hover {
        .icon-container {
            opacity: 1;
        }
    }
}
</style>
