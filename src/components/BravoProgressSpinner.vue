<script setup lang="ts">
import ProgressSpinner from 'primevue/progressspinner';

interface Props {
    strokeWidth?: string;
    fill?: string;
    animationDuration?: string;
}

defineProps<Props>();
</script>

<template>
  <ProgressSpinner
    class="bravo-progress-spinner"
    :stroke-width="strokeWidth"
    :fill="fill"
    :animation-duration="animationDuration"
    v-bind="$attrs"
  />
</template>

<style lang="scss" scoped></style>
