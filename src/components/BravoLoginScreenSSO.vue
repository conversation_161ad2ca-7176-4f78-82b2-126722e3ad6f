<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import BravoButton from './BravoButton.vue';
import BravoInputText from './BravoInputText.vue';
import BravoPassword from './BravoPassword.vue';
import Checkbox from 'primevue/checkbox';

const email = ref('');
const password = ref('');
const rememberMe = ref(false);
const loading = ref(false);
const error = ref('');
const showPasswordField = ref(false);
const checkingEmail = ref(false);

const props = defineProps<{
    submitHandler?: (credentials: { email: string; password: string; rememberMe: boolean }) => Promise<void>;
    emailCheckHandler?: (email: string) => Promise<boolean>; // Returns true if SSO is required, false if password login
}>();

const emit = defineEmits<{
    submit: [{ email: string; password: string; rememberMe: boolean }];
    ssoRedirect: [{ email: string }];
    forgotPassword: [];
}>();

const buttonLabel = computed(() => {
    if (loading.value) {
        return checkingEmail.value ? 'Checking...' : 'Signing in...';
    }
    return showPasswordField.value ? 'Sign in' : 'Continue';
});

// Focus the email input field
const focusEmailInput = () => {
    nextTick(() => {
        setTimeout(() => {
            const input = document.querySelector('.form-group:first-child input');
            if (input) {
                (input as HTMLInputElement).focus();
            }
        }, 100);
    });
};

// Focus the email input when component is mounted
onMounted(() => {
    focusEmailInput();
});

// Focus the password input when password field becomes visible
watch(showPasswordField, (newValue) => {
    if (newValue && !loading.value) {
        // Use nextTick to ensure DOM is updated
        nextTick(() => {
            // Add a small delay to ensure components are fully rendered
            setTimeout(() => {
                // Try multiple selector approaches to find the password input
                const passwordInput = document.querySelector('.password-field-container input');
                if (passwordInput) {
                    (passwordInput as HTMLInputElement).focus();
                } else {
                    // Alternative selector if the first one doesn't work
                    const altPasswordInput = document.querySelector('.form-group:nth-child(2) input');
                    if (altPasswordInput) {
                        (altPasswordInput as HTMLInputElement).focus();
                    }
                }
            }, 100);
        });
    }
});

const handleContinue = async () => {
    if (!email.value) {
        error.value = 'Please enter your email address';
        return;
    }

    if (showPasswordField.value) {
        await handleSignIn();
        return;
    }

    // Email validation
    checkingEmail.value = true;
    loading.value = true;
    error.value = '';

    try {
        // Check if email should use SSO
        if (props.emailCheckHandler) {
            const trimmedEmail = email.value.trim();
            let requireSSO = false;
            
            try {
                requireSSO = await props.emailCheckHandler(trimmedEmail);
            } catch (apiError) {
                // Check for network errors or CORS issues
                if (apiError instanceof TypeError || 
                    (apiError instanceof Error && 
                     (apiError.message.includes('NetworkError') || 
                      apiError.message.includes('CORS') || 
                      apiError.message.includes('network')))) {
                    throw new Error('Network error: Unable to connect to authentication service. Please check your internet connection or try again later.');
                }
                // Re-throw other errors
                throw apiError;
            }
            
            if (requireSSO) {
                // Route to SSO login
                emit('ssoRedirect', { email: trimmedEmail });
                return;
            }
        }
        
        // Show password field if not SSO
        showPasswordField.value = true;
    } catch (err) {
        error.value = err instanceof Error ? err.message : 'Email validation failed. Please try again.';
    } finally {
        checkingEmail.value = false;
        loading.value = false;
    }
};

const handleSignIn = async () => {
    if (!password.value) {
        error.value = 'Please enter your password';
        return;
    }

    loading.value = true;
    error.value = '';
    const trimmedEmail = email.value.trim();

    try {
        emit('submit', {
            email: trimmedEmail,
            password: password.value,
            rememberMe: rememberMe.value,
        });

        if (props.submitHandler) {
            await props.submitHandler({
                email: trimmedEmail,
                password: password.value,
                rememberMe: rememberMe.value,
            });
        }
    } catch (err) {
        error.value = err instanceof Error ? err.message : 'Login failed. Please try again.';
    } finally {
        loading.value = false;
    }
};

const handleGoBack = () => {
    showPasswordField.value = false;
    error.value = '';
    // Focus the email input when going back
    focusEmailInput();
};
</script>

<template>
  <div class="login-wrapper">
    <div class="content-column">
      <div class="login-container">
        <h1>Sign in to your account</h1>
        <div
          v-if="error"
          class="error-message"
        >
          {{ error }}
        </div>

        <form @submit.prevent="handleContinue">
          <div class="form-group email-field-container">
            <label>Email</label>
            <BravoInputText
              v-model="email"
              type="email"
              :disabled="loading"
              tabindex="1"
            />
          </div>

          <div
            v-if="showPasswordField"
            class="form-group password-field-container"
          >
            <label>Password</label>
            <BravoPassword
              v-model="password"
              toggle-mask
              :feedback="false"
              :disabled="loading"
              tabindex="2"
            />
          </div>

          <div class="form-group remember-me">
            <div class="checkbox-container">
              <div class="checkbox-wrapper">
                <Checkbox
                  v-model="rememberMe"
                  :binary="true"
                  :disabled="loading"
                  input-id="remember-me-checkbox"
                  :tabindex="4"
                />
              </div>
              <label
                class="remember-me-label"
                for="remember-me-checkbox"
              >Remember me on this device</label>
            </div>
          </div>

          <BravoButton
            type="submit"
            :label="buttonLabel"
            class="sign-in-button"
            tabindex="3"
            :loading="loading"
            :disabled="loading"
          />
        </form>

        <div v-if="showPasswordField" class="links-container">
          <BravoButton
            type="button"
            label="Forgot your password?"
            class="secondary-link"
            variant="link"
            tabindex="5"
            @click="emit('forgotPassword')"
          />
        </div>
      </div>
      
      <div
        v-if="showPasswordField"
        class="go-back-container"
      >
        <button 
          class="go-back-button" 
          type="button"
          tabindex="6"
          @click="handleGoBack"
        >
          <i class="pi pi-angle-left"></i>
          <span class="go-back-text">Go Back</span>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.content-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 440px;
}

.login-container {
    width: 100%;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
}

.login-container h1 {
    font-size: 20px;
    line-height: 28px;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--surface-850);
    text-align: left;
}

.form-group {
    margin-bottom: 1rem;

}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 14px;
    color: var(--surface-850);
}


.sign-in-button {
    width: 100%;
    background: var(--primary-600);
    padding: 0.75rem;
    font-weight: 500;
    margin: 1rem 0 1rem 0;
    min-height: 44px; /* Ensure touch-friendly height */
}

.links-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.secondary-link {
    font-size: 14px;
    color: var(--primary-600);
    text-decoration: none;
}

.remember-me {
    display: flex;
    align-items: center;
    margin-top: 0.5rem;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: .75rem;
    padding: 0;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    padding: 0;
}

.remember-me-label {
    margin: 0 !important;
    padding: 0;
    cursor: pointer;
    font-size: 14px;
    color: var(--surface-850);
    display: inline-block;
    line-height: 20px;
}

:deep(.p-checkbox) {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
}

:deep(.p-checkbox-box) {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
}

:deep(.p-password input),
:deep(.p-inputtext) {
    width: 100%;
    height: 44px !important; /* Increased for better touch targets */
}

:deep(.p-password) {
    width: 100%;
    display: block;
}

.error-message {
    background-color: var(--red-100);
    color: var(--red-700);
    padding: 0.75rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    font-size: 14px;
}

.go-back-container {
    margin-top: 1.5rem;
    text-align: center;
    width: 100%;
}

.go-back-button {
    background: transparent;
    border: none;
    color: var(--primary-600);
    font-size: 14px;
    cursor: pointer;
    padding: 0.75rem 1rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    margin: 0 auto;
    min-height: 44px;
}

.go-back-text {
    position: relative;
}

.go-back-button:hover .go-back-text {
    text-decoration: underline;
}

.go-back-button i {
    font-size: 12px;
}

@media (min-width: 640px) {
    .login-container {
        padding: 3rem;
    }
    
    .login-container h1 {
        font-size: 22px;
        padding-bottom: .5rem;
    }
}

@media (min-width: 768px) {
    .login-container {
        padding: 4rem;
    }
    
    .login-container h1 {
        font-size: 24px;
        padding-bottom: 1rem;
    }
}
</style>
