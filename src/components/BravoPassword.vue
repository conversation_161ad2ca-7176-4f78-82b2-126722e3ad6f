<script setup lang="ts">
import Password from 'primevue/password';
import { computed } from 'vue';

const props = defineProps({
    tabindex: [String, Number],
});

const inputProps = computed(() => {
    return {
        inputProps: props.tabindex ? { tabindex: props.tabindex } : undefined,
    };
});
</script>

<template>
  <Password
    v-bind="{ ...$attrs, ...inputProps }"
    class="bravo-passwordfield"
  />
</template>

<style lang="scss" scoped>
.bravo-passwordfield {
    display: block;

    :deep(.p-password) {
        display: block;
    }

    :deep(.p-inputtext) {
        width: 100%;
        height: inherit;
    }
}
</style>
