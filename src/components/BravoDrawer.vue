<script setup lang="ts">
import Sidebar from 'primevue/drawer';

const emit = defineEmits(['update:visible']);
</script>

<template>
  <Sidebar
    class="bravo-drawer"
    v-bind="$attrs"
    @update:visible="(value) => emit('update:visible', value)"
  >
    <slot></slot>
    <template
      v-if="$slots.header"
      #header
    >
      <slot name="header"></slot>
    </template>
    <template
      v-if="$slots.footer"
      #footer
    >
      <slot name="footer"></slot>
    </template>
  </Sidebar>
</template>

<style lang="scss">
.bravo-drawer {
  .p-drawer-close-button.p-button-rounded {
    border-radius: var(--border-radius-lg) !important;
  }
}
</style>


