<script setup lang="ts">
import Tree from 'primevue/tree';
</script>

<template>
  <Tree
    class="bravo-navtree"
    v-bind="$attrs"
  />
</template>

<style lang="scss" scoped>
.bravo-navtree {
    :deep(.p-tree-node-label) {
        font-weight: 700;
    }

    :deep(.p-tree-node-children .p-tree-node-toggle-button) {
        display: none !important;
    }

    :deep(.p-tree-node-children) {
        padding-left: 28px;
    }

    :deep(.p-tree-node-children .p-tree-node-label) {
        font-weight: 400;
        color: var(--text-color-primary);
    }
}
</style>
