<script setup lang="ts">
import AccordionPanel from 'primevue/accordionpanel';

// Define props with default value
defineProps({
    value: {
        type: [String, Number],
        required: true,
    },
});
</script>

<template>
  <AccordionPanel
    class="bravo-accordion-panel"
    :value="value"
    v-bind="$attrs"
  >
    <slot></slot>
  </AccordionPanel>
</template>

<style lang="scss" scoped></style>
