<script setup lang="ts">
import Avatar from 'primevue/avatar';
import AvatarGroup from 'primevue/avatargroup';
import { computed, useAttrs } from 'vue';

// Re-export AvatarGroup for convenience
defineExpose({ AvatarGroup });

// Get access to attrs
const attrs = useAttrs();

const props = defineProps<{
  firstName?: string;
  lastName?: string;
  image?: string;
  backgroundColor?: string;
}>();

const initials = computed(() => {
  // Don't show initials if image is provided
  if (props.image) return '';
  
  // Show initials if at least one name exists
  if (props.firstName && props.lastName) {
    return `${props.firstName[0]}${props.lastName[0]}`;
  } else if (props.firstName) {
    return props.firstName[0];
  } else if (props.lastName) {
    return props.lastName[0];
  }
  
  return ''; // No names available, will default to icon
});

// Compute whether to show the icon based on what's available
const showIcon = computed(() => {
  // Don't show icon if image is provided
  if (props.image) return false;
  
  // Don't show icon if we have initials
  if (initials.value) return false;
  
  // Show icon as fallback
  return true;
});

// Compute styles including background color for avatars with initials or icon
const avatarStyle = computed(() => {
  // Only apply background color when showing initials or icon (not for image avatars)
  if (!props.image && props.backgroundColor) {
    const attrStyle = typeof attrs.style === 'object' ? attrs.style : {};
    return { 
      backgroundColor: props.backgroundColor,
      ...attrStyle
    };
  }
  
  // Return existing style
  return attrs.style || {};
});
</script>

<template>
  <Avatar
    v-bind="$attrs"
    class="bravo-avatar"
    :label="initials"
    :image="props.image"
    :icon="showIcon ? 'pi pi-user' : undefined"
    :style="avatarStyle"
  >
    <slot></slot>
  </Avatar>
</template>

<style lang="scss" scoped></style>
