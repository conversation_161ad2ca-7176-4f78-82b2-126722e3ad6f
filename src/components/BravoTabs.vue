<script setup lang="ts">
import Tabs from 'primevue/tabs';

// Define props to match PrimeVue Tabs
interface Props {
    value: string | number;
    scrollable?: boolean;
}

const props = defineProps<Props>();

defineOptions({
    name: 'BravoTabs',
});
</script>

<template>
  <Tabs
    class="bravo-tabs"
    :scrollable="props.scrollable"
    :value="props.value"
    v-bind="$attrs"
  >
    <slot></slot>
  </Tabs>
</template>

<style lang="scss" scoped>
.bravo-tabs {
    width: 100%;
    
    /* Hide scrollbar for the tabs component - comprehensive approach */
    :deep(*) {
        scrollbar-width: none !important; /* Firefox */
        -ms-overflow-style: none !important; /* Internet Explorer 10+ */
        
        &::-webkit-scrollbar {
            display: none !important; /* WebKit browsers */
        }
    }
    
    /* Specific targeting for common PrimeVue tab selectors */
    :deep(.p-tabs-tablist),
    :deep(.p-tablist),
    :deep(.p-tabs-nav),
    :deep(.p-tabs-header) {
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
        
        &::-webkit-scrollbar {
            display: none !important;
        }
    }
}
</style>
