<script setup lang="ts">
import PanelMenu from 'primevue/panelmenu';
import { ref, provide, onMounted } from 'vue';
import Badge from 'primevue/badge';

// Track active menu items
const activeItem = ref<string | null>(null);
const menuRef = ref<HTMLElement | null>(null);

// Provide a function to allow menu items to mark themselves as active
provide('setActiveMenuItem', (itemLabel: string) => {
    activeItem.value = itemLabel;
});

// Expose the active item to child components
provide('activeMenuItem', activeItem);

// Function to handle item clicks safely
function handleItemClick(event: Event, onClick?: any) {
    // Prevent default behavior
    event.preventDefault();

    // Call the provided onClick handler if it exists
    if (onClick && typeof onClick === 'function') {
        onClick(event);
    }
}

// Call the setupClickHandlers function when component is mounted
onMounted(() => {
    setTimeout(() => {
        if (menuRef.value) {
            setupClickHandlers();
        }
    }, 300);
});

function setupClickHandlers() {
    // Add click handlers to all menu items (excluding headers)
    const allLinks = menuRef.value?.querySelectorAll('a.menu-item, a') || [];

    allLinks.forEach((link) => {
        // Skip if the link is a header item or already has a click handler
        if (link.closest('.p-panelmenu-header') || link.hasAttribute('data-menu-click-handled')) {
            return;
        }

        // Mark this link as having a click handler
        link.setAttribute('data-menu-click-handled', 'true');

        // Add click handler
        link.addEventListener('click', () => {
            // Only handle clicks on non-header items
            if (!link.closest('.p-panelmenu-header')) {
                // Remove active class from all links
                document.querySelectorAll('.bravo-active-item').forEach((el) => {
                    el.classList.remove('bravo-active-item');
                });

                // Add active class to this link
                link.classList.add('bravo-active-item');

                // Store the label text
                activeItem.value = link.textContent?.trim() || null;
            }
        });
    });
}

// Define props to receive custom attributes
defineOptions({
    inheritAttrs: false,
});
</script>

<template>
  <div
    ref="menuRef"
    class="bravo-panel-menu-container"
  >
    <PanelMenu
      class="bravo-panel-menu"
      expand-icon="pi pi-angle-right"
      collapse-icon="pi pi-angle-down"
      v-bind="$attrs"
    >
      <template #item="{ item, props }">
        <a
          class="menu-item flex items-center justify-between px-4 py-2 cursor-pointer"
          :class="[props.class]"
          href="javascript:void(0)"
          @click="(event) => handleItemClick(event, props.onClick)"
        >
          <div class="flex items-center">
            <span
              v-if="item.icon"
              :class="[item.icon]"
            ></span>
            <span
              v-if="!item.items"
              class="ml-2"
            >{{ item.label }}</span>
            <span
              v-else
              class="font-semibold header-label"
            >{{ item.label }}</span>
          </div>
          <div class="flex items-center">
            <Badge
              v-if="item.badge"
              :value="item.badge"
              class="custom-badge"
            />
            <i
              v-if="item.items"
              class="pi pi-angle-right custom-submenu-icon"
            ></i>
          </div>
        </a>
      </template>
    </PanelMenu>
  </div>
</template>

<style lang="scss" scoped>
.bravo-panel-menu-container {
    width: 100%;
}

.bravo-panel-menu {
    /* General styling */
    .custom-submenu-icon {
        font-size: 16px;
        margin-left: 8px;
        transition: transform 0.3s;
        color: var(--text-color-primary) !important;
    }

    /* Target header icons - dynamic state handling through CSS */
    ::v-deep(.p-panelmenu-header) {
        &[aria-expanded='true'] {
            .custom-submenu-icon {
                /* Change icon when expanded - direct approach */
                color: var(--text-color-primary) !important;
                transform: rotate(90deg);
            }
        }
    }

    /* Custom header label styling */
    .header-label {
        font-size: 13px;
        font-weight: 600;
        color: var(--text-color-primary);
        text-transform: uppercase;
        margin: 0;
        padding: 0;
    }

    /* Badge styling - consolidated from stories file */
    ::v-deep(.p-badge),
    ::v-deep(.custom-badge) {
        background-color: transparent !important;
        color: var(--text-color-secondary, #6c757d) !important;
        font-size: 14px !important;
        font-weight: normal !important;
        padding: 0 !important;
        min-width: auto !important;
        height: auto !important;
    }

    /* Bold badge when in selected state */
    ::v-deep(a.bravo-active-item .custom-badge),
    ::v-deep(.bravo-active-item .custom-badge),
    ::v-deep(.menu-item.bravo-active-item .custom-badge) {
        font-weight: 700 !important;
        color: var(--text-color-secondary) !important;
    }

    ::v-deep(.p-panelmenu-icon) {
        margin-right: 0.5rem;
    }

    ::v-deep(.p-panelmenu-panel) {
        border: none;
        outline: none;
        background-color: var(--surface-50);
    }

    /* Submenu icons - hide PrimeVue's default ones */
    ::v-deep(.p-submenu-icon) {
        display: none !important;
    }

    /* Active state styling - target all possible menu items */
    ::v-deep(a.bravo-active-item),
    ::v-deep(.bravo-active-item),
    ::v-deep(.menu-item.bravo-active-item),
    ::v-deep(.p-panelmenu-content a.bravo-active-item),
    ::v-deep(.p-panelmenu-content .bravo-active-item) {
        background-color: white !important;
        font-weight: 600 !important;
        position: relative;
        border-radius: 8px;
    }

    /* Also apply active state to any anchor tag */
    ::v-deep(a.bravo-active-item),
    ::v-deep(a.menu-item.bravo-active-item) {
        background-color: white !important;
        font-weight: 600 !important;
        position: relative;
        border-radius: 8px;
    }

    ::v-deep(.p-panelmenu-item-link) {
        padding-left: 14px;
        padding-right: 14px;
        padding-top: 10px;
        padding-bottom: 10px;
    }

    /* Header link styling */
    ::v-deep(.p-panelmenu-header-link) {
        padding-left: 16px;
        padding-right: 16px;
        padding-top: 10px;
        padding-bottom: 10px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between; /* This helps with alignment */

        /* Ensure header links don't get the active/highlight styling */
        &.p-highlight,
        &.bravo-active-item {
            background-color: transparent !important;
            font-weight: normal !important;
            color: inherit !important;
        }
    }

    /* Menu item styling */
    ::v-deep(.menu-item),
    ::v-deep(a.menu-item) {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 16px;
        cursor: pointer;
        background-color: var(--surface-50);
        border-radius: 8px;
        transition:
            background-color 0.2s,
            color 0.2s;

        &:hover {
            background-color: white;
        }
    }

    /* Add background-color to all menu items */
    ::v-deep(.p-panelmenu-item-link),
    ::v-deep(.p-menuitem-link) {
        background-color: var(--surface-50);
    }

    /* Hover effects */
    ::v-deep(a:hover),
    ::v-deep(.menu-item:hover),
    ::v-deep(.p-panelmenu-item-link:hover),
    ::v-deep(.p-menuitem-link:hover) {
        background-color: white;
        border-radius: 8px;
    }

    ::v-deep(.p-panelmenu-submenu .p-panelmenu-root-list) {
        padding-left: 0 !important;
    }

    /* Alternative selectors to ensure we target the correct element */
    ::v-deep(.p-panelmenu-content) {
        .p-submenu-list,
        .p-panelmenu-root-list,
        ul {
            padding-left: 0 !important;
            margin-left: 0 !important;
        }
    }
}
</style>
