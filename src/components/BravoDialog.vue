<script setup lang="ts">
import Dialog from 'primevue/dialog';

const emit = defineEmits(['update:visible']);
</script>

<template>
  <Dialog
    class="bravo-dialog"
    v-bind="$attrs"
    @update:visible="(value) => emit('update:visible', value)"
  >
    <slot></slot>
    <template
      v-if="$slots.header"
      #header
    >
      <slot name="header"></slot>
    </template>
    <template
      v-if="$slots.footer"
      #footer
    >
      <slot name="footer"></slot>
    </template>
  </Dialog>
</template>

<style>
/* Override to change border radius on dialog close buttons */
.p-dialog-close-button.p-button-rounded {
  border-radius: var(--border-radius-md) !important;
}
</style>
