<script setup lang="ts">
import { ref, computed, toRefs, onMounted, onBeforeUpdate } from 'vue';

import BravoDatePicker from './BravoDatePicker.vue';
import BravoDialog from './BravoDialog.vue';
import BravoButton from './BravoButton.vue';
import BravoInputText from './BravoInputText.vue';
import BravoInputNumber from './BravoInputNumber.vue';
import BravoInputGroup from './BravoInputGroup.vue';
import BravoSelectButton from './BravoSelectButton.vue';
import BravoToggleButton from './BravoToggleButton.vue';

const props = defineProps({
    modelValue: {
        type: [String, Date],
        default: '',
    },
    type: {
        type: String,
        default: 'date',
    },
});

const { modelValue } = toRefs(props);
const { type } = toRefs(props);

const emit = defineEmits(['update:modelValue', 'relativeDatePickerApply', 'relativeDatePickerCancel']);

const TYPE_DATE = 'date';
const TYPE_DURATION = 'duration';

const DAYS_PER_WEEK = 7;
const HOURS_PER_DAY = 24;

const disabled = false;

const UNITS_HOURS = 0;
const UNITS_DAYS = 1;
const UNITS_WEEKS = 2;

const selectedUnit = ref(UNITS_HOURS);
const showModal = ref(false);
const selectedDate = ref<Date>(new Date());

const unitQuantity = ref(0);
const currentUnit = ref(UNITS_HOURS);
const hours = ref(12);
const minutes = ref(0);
const isAM = ref(true);

const isModeFixed = computed(() => type.value.toLowerCase() !== TYPE_DURATION);

const unitTabs = [
    {
        id: UNITS_HOURS,
        identifier: 'hours',
        abbr: 'H',
        text: 'Hours',
        icon: null,
        content: 'User tab content',
    },
    {
        id: UNITS_DAYS,
        identifier: 'days',
        abbr: 'D',
        text: 'Days',
        icon: null,
        content: 'Comment tab content',
    },
    {
        id: UNITS_WEEKS,
        identifier: 'weeks',
        abbr: 'W',
        text: 'Weeks',
        icon: null,
        content: 'Comment tab content',
    },
];

const isTypeDuration = computed(() => type.value.toLowerCase() === TYPE_DURATION);
const isTypeDate = computed(() => type.value.toLowerCase() !== TYPE_DURATION);

const inputType = ref(props.type);

// Options for the BravoSelectButton toggle
const toggleOptions = ref([
    { label: 'Fixed Date', value: TYPE_DATE },
    { label: 'Relative Date', value: TYPE_DURATION }
]);

// Options for the unit selection
const unitOptions = ref([
    { label: 'Hours', value: UNITS_HOURS },
    { label: 'Days', value: UNITS_DAYS },
    { label: 'Weeks', value: UNITS_WEEKS }
]);

onMounted(() => {
    if (isTypeDuration.value) {
        getDurationSettings();
    } else if (isTypeDate.value) {
        getCurrentDateSettings();
    }
});

onBeforeUpdate(() => {
    if (isTypeDuration.value) {
        getDurationSettings();
    } else if (isTypeDate.value) {
        getCurrentDateSettings();
    }
});

function getCurrentDateSettings() {
    selectedDate.value = modelValue?.value ? new Date(modelValue.value) : new Date(Date.now());
    isAM.value = selectedDate.value.getHours() < 12;
    
    // Convert 24-hour to 12-hour format
    const hour24 = selectedDate.value.getHours();
    if (hour24 === 0) {
        hours.value = 12;
        isAM.value = true;
    } else if (hour24 <= 12) {
        hours.value = hour24;
        isAM.value = hour24 < 12;
    } else {
        hours.value = hour24 - 12;
        isAM.value = false;
    }
    
    minutes.value = selectedDate.value.getMinutes();
}

function parseFromISO8601(duration: string) {
    const values: string[] | null = duration.match(/^(PT|P)([\d]+)([D|H|W])/);

    if (values && values.length === 4) {
        const tab = unitTabs.find((x) => x.abbr === values[3]);

        if (tab) {
            return {
                quantity: parseInt(values[2]),
                unit: tab.identifier,
            };
        }
    }
    return null;
}

function getDurationSettings() {
    if (modelValue?.value && typeof modelValue.value === 'string') {
        const values = parseFromISO8601(modelValue.value);
        if (values) {
            const tab = unitTabs.find((x) => x.identifier === values['unit']);

            if (tab) {
                selectedUnit.value = tab.id;
                unitQuantity.value = values['quantity'];
                currentUnit.value = selectedUnit.value;
            }
        }
    }
}

const localValue = computed({
    get() {
        // If we have a string value, check if it's an ISO8601 duration
        if (typeof modelValue.value === 'string' && modelValue.value) {
            const durationValue = parseFromISO8601(modelValue.value);
            if (durationValue) {
                // It's a duration string, display it as "in X units"
                return `in ${durationValue['quantity']} ${durationValue['unit']}`;
            } else {
                // It's a date string, convert to Date for display
                return new Date(modelValue.value);
            }
        } else if (modelValue.value instanceof Date) {
            // It's already a Date object
            return modelValue.value;
        } else {
            // Empty or null value
            return '';
        }
    },
    set(val) {
        console.log('localValue set', val);
        emit('update:modelValue', val);
    },
});

function onBoxClicked(event: Event) {
    event.preventDefault();
    event.stopPropagation();
    if (!showModal.value) {
        showModal.value = true;
    }
}

function onCancel() {
    // Reset input type back to original
    inputType.value = props.type;
    
    // Reset any uncommitted changes back to original values
    if (isTypeDuration.value) {
        getDurationSettings();
    } else if (isTypeDate.value) {
        getCurrentDateSettings();
    }
    
    // Close the dialog
    showModal.value = false;
    
    // Emit the cancel event
    emit('relativeDatePickerCancel');
}

function onApply() {
    let newValue;
    if (inputType.value === TYPE_DURATION) {
        newValue = getDuration();
    } else {
        newValue = getFixedDate();
    }
    
    // Update the model value
    localValue.value = newValue;
    
    // Close the dialog
    showModal.value = false;
    
    // Emit the apply event
    emit('relativeDatePickerApply', newValue);
}

function getFixedDate(): string {
    // Create a copy of the selected date to avoid mutation
    const date = new Date(selectedDate.value);
    
    if (isModeFixed.value) {
        // Handle 12-hour format conversion to 24-hour
        let hour24 = hours.value;
        if (!isAM.value && hours.value !== 12) {
            hour24 = hours.value + 12;
        } else if (isAM.value && hours.value === 12) {
            hour24 = 0;
        }
        
        date.setHours(hour24);
        date.setMinutes(minutes.value);
        date.setSeconds(0);
        date.setMilliseconds(0);
        
        return date.toISOString();
    }
    
    // For relative duration mode, calculate future date
    let durationHours = 0;
    if (currentUnit.value == UNITS_HOURS) {
        durationHours = unitQuantity.value;
    } else if (currentUnit.value == UNITS_DAYS) {
        durationHours = unitQuantity.value * HOURS_PER_DAY;
    } else if (currentUnit.value == UNITS_WEEKS) {
        durationHours = unitQuantity.value * DAYS_PER_WEEK * HOURS_PER_DAY;
    }
    
    const futureDate = new Date();
    futureDate.setHours(futureDate.getHours() + durationHours);
    return futureDate.toISOString();
}

function getDuration(): string {
    // Duration Format P[n]Y[n]M[n]DT[n]H[n]M[n]S
    let duration = 'P';
    const quantity = String(unitQuantity.value);
    if (currentUnit.value == UNITS_HOURS) {
        duration += 'T' + quantity + 'H';
    } else if (currentUnit.value == UNITS_DAYS) {
        duration += quantity + 'D';
    } else if (currentUnit.value == UNITS_WEEKS) {
        duration += quantity + 'W';
    }
    return duration;
}
</script>

<template>
  <div class="relative-datebox">
    <!-- TOP-LEVEL INPUT FIELDS  -->
    <div v-if="isTypeDate">
      <BravoInputGroup>
        <BravoDatePicker
          id="calendar-12h"
          v-model="localValue as Date"
          class="bravo-relative-datetime-input"
          show-time
          hour-format="12"
          date-format="m/d/yy"
          data-testid="picker-datebox"
          readonly
          @click="onBoxClicked"
          @focus="onBoxClicked"
        />
      </BravoInputGroup>
    </div>
    <div v-if="isTypeDuration">
      <BravoInputGroup>
        <BravoInputText
          :value="localValue"
          class="bravo-relative-datetime-input"
          data-testid="picker-textbox"
          readonly
          @click="onBoxClicked"
          @focus="onBoxClicked"
        />
        <BravoButton
          icon="pi pi-calendar"
          :outlined="true"
          @click="onBoxClicked"
        />
      </BravoInputGroup>
    </div>
    <!-- MODAL -->
    <BravoDialog
      v-if="showModal"
      :visible="true"
      modal
      header="Select Date/Time"
    >
      <div class="datebox-dialog tasks-ui-popup-content">
        <div class="relative-datebox-container">
          <div class="relative-datebox-toggle">
            <BravoSelectButton
              v-if="type === TYPE_DATE"
              v-model="inputType"
              :options="toggleOptions"
              option-label="label"
              option-value="value"
              data-testid="picker-mode-toggle"
            />
          </div>
          <div v-if="inputType === TYPE_DATE">
            <div
              class="tasks-ui-calendar-container"
              data-testid="picker-fixed-view"
            >
              <div class="fixed-calendar">
                <BravoDatePicker
                  v-model="selectedDate"
                  :inline="true"
                  :disabled="disabled"
                  data-testid="picker-calendar"
                />
              </div>

              <div class="tasks-ui-time-container">
                <BravoInputNumber
                  v-model="hours"
                  class="fixed-hours"
                  show-buttons
                  :min="1"
                  :max="12"
                  data-testid="picker-hours"
                />
                <span class="tasks-ui-time-delimiter">:</span>
                <BravoInputNumber
                  v-model="minutes"
                  class="fixed-minutes"
                  show-buttons
                  :min="0"
                  :max="59"
                  data-testid="picker-minutes"
                  :format="false"
                />
                <div class="tasks-ui-meridiem-container">
                  <BravoToggleButton
                    v-model="isAM"
                    on-label="AM"
                    off-label="AM"
                    data-testid="picker-button-am"
                  />
                  <BravoToggleButton
                    :model-value="!isAM"
                    on-label="PM"
                    off-label="PM"
                    data-testid="picker-button-pm"
                    @update:model-value="isAM = !$event"
                  />
                </div>
              </div>
            </div>
          </div>
          <div v-if="inputType === TYPE_DURATION">
            <div
              data-testid="picker-relative-view"
              class="datebox-duration"
            >
              <div class="duration-unit-amount">
                <BravoInputNumber
                  v-model="unitQuantity"
                  class="duration-unit-amount"
                  aria-label="Integer Format"
                  data-testid="picker-unit-quantity"
                  show-buttons
                  :min="0"
                />
              </div>

              <BravoSelectButton
                v-model="currentUnit"
                :options="unitOptions"
                option-label="label"
                option-value="value"
                class="duration-unit-toggle"
                data-testid="picker-unit-selector"
              />
            </div>
          </div>
        </div>
        <div class="datebox-ctas">
          <BravoButton
            label="Cancel"
            :outlined="true"
            data-testid="picker-button-cancel"
            @click="onCancel"
          />
          <BravoButton
            label="Apply"
            data-testid="picker-button-apply"
            @click="onApply"
          />
        </div>
      </div>
    </BravoDialog>
  </div>
</template>

<style scoped lang="scss">
.tasks-ui-time-container {
    display: flex;
    flex-direction: row;
    justify-content: left;
    align-items: center;
    gap: 12px;
    margin-top: 20px;
    width: 100%;
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
}
.tasks-ui-popup-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}
.tasks-ui-meridiem-container {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    gap: 4px;
    padding-bottom: 12px;
}
.tasks-ui-time-delimiter {
    font-size: 18px;
    font-weight: 600;
    line-height: normal;
}

.tasks-ui-relative-date {
    display: flex;
    align-items: center;
    gap: 10px;
}

.tasks-ui-calendar-container {
    width: 100%;
}

.relative-datebox-container {
    width: 100%;
}

.bravo-relative-datetime-input {
    cursor: pointer;
    
    &:deep(.p-inputtext) {
        cursor: pointer;
        opacity: 1;
        background-color: var(--p-form-field-background);
        
        &:hover {
            border-color: var(--p-form-field-border-color-hover);
        }
        
        &:focus {
            border-color: var(--p-form-field-border-color-focus);
            box-shadow: var(--p-form-field-focus-ring);
        }
    }
}

// Add styling to reduce border between input and button in input group
:deep(.p-inputgroup .p-inputtext:not(:first-child)) {
    border-left-width: 0px !important;
}

:deep(.p-inputgroup .p-button:not(:first-child)) {
    border-left-width: 0px !important;
}

// Target the input group more specifically
:deep(.p-inputgroup) {
    .p-inputtext {
        border-right-width: 1px !important;
        margin-right: -1px !important;
    }
    
    .p-button {
        border-left-width: 1px !important;
        margin-left: -1px !important;
    }
}

.relative-datebox {
    position: relative;
}

.relative-datebox-toggle {
    margin-bottom: 10px;
}

.datebox-duration {
    margin-top: 36px;
    display: flex;
    gap: 12px;
}

.fixed-calendar {
    margin-top: 36px;
}

.fixed-minutes,
.fixed-hours,
.duration-unit-amount {
    &:deep(.p-inputnumber-input) {
        width: 60px;
    }
    &:deep(.p-button) {
        background: #ffffff;
    }
    
    // Fix for faded corners on increment/decrement buttons
    &:deep(.p-inputnumber-button) {
        background: #ffffff !important;
        border-radius: var(--p-inputnumber-button-border-radius) !important;
        
        &.p-inputnumber-increment-button {
            border-top-right-radius: var(--p-inputnumber-increment-button-border-radius) !important;
            border-bottom-right-radius: var(--p-inputnumber-increment-button-border-radius) !important;
        }
        
        &.p-inputnumber-decrement-button {
            border-top-left-radius: var(--p-inputnumber-decrement-button-border-radius) !important;
            border-bottom-left-radius: var(--p-inputnumber-decrement-button-border-radius) !important;
        }
        
        &:hover {
            background: var(--p-button-secondary-hover-background) !important;
        }
    }
}

.duration-unit-toggle {
    min-width: 252px;
}

.datebox-ctas {
    margin: 24px 0px 0px 0px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 16px;
    align-self: stretch;
}

.datebox-dialog {
    width: 350px;
    max-width: 100%;
}
</style> 