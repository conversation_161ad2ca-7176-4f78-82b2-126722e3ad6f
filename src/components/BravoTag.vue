<script setup lang="ts">
import Tag from 'primevue/tag';

interface Props {
    value?: string;
    severity?: 'success' | 'info' | 'warn' | 'danger';
    rounded?: boolean;
    icon?: string;
    state?: 'new' | 'ready' | 'waiting' | 'resolved' | 'closed' | 'draft' | 'published' | 'unpublished' | 'previously-published' | 'archived' ;
}

defineProps<Props>();
</script>

<template>
  <Tag
    class="bravo-tag"
    :class="{
      'state-new': state === 'new',
      'state-ready': state === 'ready',
      'state-waiting': state === 'waiting',
      'state-resolved': state === 'resolved',
      'state-closed': state === 'closed',
      'state-draft': state === 'draft',
      'state-published': state === 'published',
      'state-unpublished': state === 'unpublished',
      'state-previously-published': state === 'previously-published',
      'state-archived': state === 'archived',
      

    }"
    :value="value"
    :severity="severity"
    :rounded="rounded"
    :icon="icon"
    v-bind="$attrs"
  >
    <slot></slot>
  </Tag>
</template>

<style lang="scss">
.p-tag.bravo-tag {
  line-height: 1.25 !important;
}

.p-tag.bravo-tag.state-new {
    background: var(--orange-100);
    color: var(--orange-800);
}

.p-tag.bravo-tag.state-ready {
    background: var(--orange-100);
    color: var(--orange-800);
}

.p-tag.bravo-tag.state-waiting {
    background: var(--yellow-100);
    color: var(--yellow-800);
}

.p-tag.bravo-tag.state-resolved {
    background: var(--surface-100);
    color: var(--surface-800);
}

.p-tag.bravo-tag.state-closed {
    background: var(--surface-200);
    color: var(--surface-850);
}

.p-tag.bravo-tag.state-draft {
    background: var(--yellow-100);
    color: var(--surface-850);
}

  .p-tag.bravo-tag.state-published {
      background: var(--green-100);
      color: var(--surface-850);
  }

  .p-tag.bravo-tag.state-unpublished {
      background: var(--surface-100);
      color: var(--surface-850);
  }

.p-tag.bravo-tag.state-previously-published {
    background: var(--surface-100);
    color: var(--surface-850);
}

.p-tag.bravo-tag.state-archived {
    background: var(--surface-100);
    color: var(--surface-850);
}
</style>
