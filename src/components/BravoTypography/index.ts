import { App } from 'vue';
import BravoBody from './BravoBody.vue';
import BravoBodyBold from './BravoBodyBold.vue';
import BravoBodyUnderline from './BravoBodyUnderline.vue';
import BravoCaption1 from './BravoCaption1.vue';
import BravoCaption2 from './BravoCaption2.vue';
import BravoCaption3 from './BravoCaption3.vue';
import BravoHeadline from './BravoHeadline.vue';
import BravoLargeBody from './BravoLargeBody.vue';
import BravoLargeTitle from './BravoLargeTitle.vue';
import BravoParagraph from './BravoParagraph.vue';
import BravoSmallCaption from './BravoSmallCaption.vue';
import BravoSubhead from './BravoSubhead.vue';
import BravoTitle1 from './BravoTitle1.vue';
import BravoTitle2 from './BravoTitle2.vue';
import BravoTitle3 from './BravoTitle3.vue';
import BravoTitlePage from './BravoTitlePage.vue';
import BravoTypography from './BravoTypography.vue';

export {
    BravoBody,
    BravoBodyBold,
    BravoBodyUnderline,
    BravoCaption1,
    BravoCaption2,
    BravoCaption3,
    BravoHeadline,
    BravoLargeBody,
    BravoLargeTitle,
    BravoParagraph,
    BravoSmallCaption,
    BravoSubhead,
    BravoTitle1,
    BravoTitle2,
    BravoTitle3,
    BravoTitlePage,
    BravoTypography,
};

// For Vue plugin installation
export default {
    install(app: App) {
        app.component('BravoBody', BravoBody);
        app.component('BravoBodyBold', BravoBodyBold);
        app.component('BravoBodyUnderline', BravoBodyUnderline);
        app.component('BravoCaption1', BravoCaption1);
        app.component('BravoCaption2', BravoCaption2);
        app.component('BravoCaption3', BravoCaption3);
        app.component('BravoHeadline', BravoHeadline);
        app.component('BravoLargeBody', BravoLargeBody);
        app.component('BravoLargeTitle', BravoLargeTitle);
        app.component('BravoParagraph', BravoParagraph);
        app.component('BravoSmallCaption', BravoSmallCaption);
        app.component('BravoSubhead', BravoSubhead);
        app.component('BravoTitle1', BravoTitle1);
        app.component('BravoTitle2', BravoTitle2);
        app.component('BravoTitle3', BravoTitle3); 
        app.component('BravoTitlePage', BravoTitlePage);
        app.component('BravoTypography', BravoTypography);
    },
};
