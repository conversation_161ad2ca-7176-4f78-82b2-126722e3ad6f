<template>
  <component
    :is="resolveComponent"
    v-bind="$attrs"
  >
    <slot></slot>
  </component>
</template>

<script lang="ts">
import { computed, defineComponent } from 'vue';
import {
    BravoBody,
    BravoBodyBold,
    BravoBodyUnderline,
    BravoCaption1,
    BravoCaption2,
    BravoCaption3,
    BravoHeadline,
    BravoLargeBody,
    BravoLargeTitle,
    BravoParagraph,
    BravoSmallCaption,
    BravoSubhead,
    BravoTitle1,
    BravoTitle2,
    BravoTitle3,
} from '.';

export type TypographyVariant =
    | 'body'
    | 'bodyBold'
    | 'bodyUnderline'
    | 'caption1'
    | 'caption2'
    | 'caption3'
    | 'headline'
    | 'largeBody'
    | 'largeTitle'
    | 'paragraph'
    | 'smallCaption'
    | 'subhead'
    | 'title1'
    | 'title2'
    | 'title3';

export default defineComponent({
    name: 'BravoTypography',

    props: {
        variant: {
            type: String as () => TypographyVariant,
            required: true,
        },
    },

    setup(props) {
        const componentMap = {
            body: BravoBody,
            bodyBold: BravoBodyBold,
            bodyUnderline: BravoBodyUnderline,
            caption1: BravoCaption1,
            caption2: BravoCaption2,
            caption3: BravoCaption3,
            headline: BravoHeadline,
            largeBody: BravoLargeBody,
            largeTitle: BravoLargeTitle,
            paragraph: BravoParagraph,
            smallCaption: BravoSmallCaption,
            subhead: BravoSubhead,
            title1: BravoTitle1,
            title2: BravoTitle2,
            title3: BravoTitle3,
        };

        const resolveComponent = computed(() => {
            return componentMap[props.variant];
        });

        return {
            resolveComponent,
        };
    },
});
</script>
