<template>
  <h1 class="large-title">
    <slot></slot>
  </h1>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
    name: 'BravoLargeTitle',
});
</script>

<style scoped>
.large-title {
    font-size: 20px;
    font-weight: 600; /* semibold */
    color: var(--text-color-primary);
    text-transform: none;
    margin: 0;
    padding: 0;
}
</style>
