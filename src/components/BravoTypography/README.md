# BravoTypography

A comprehensive typography system for your Vue application.

## Installation

### Global Registration

```js
import { createApp } from 'vue';
import App from './App.vue';
import BravoTypography from './components/BravoTypography';

const app = createApp(App);
app.use(BravoTypography);
app.mount('#app');
```

### Individual Component Import

```js
import {
    BravoLargeTitle,
    BravoTitle1,
    BravoTitle2,
    BravoTitle3,
    BravoHeadline,
    BravoLargeBody,
    BravoSubhead,
    BravoBody,
    BravoBodyBold,
    BravoBodyUnderline,
    BravoParagraph,
    BravoCaption1,
    BravoCaption2,
    BravoCaption3,
    BravoSmallCaption,
} from './components/BravoTypography';

export default {
    components: {
        BravoLargeTitle,
        BravoTitle1,
        BravoTitle2,
        BravoTitle3,
        BravoHeadline,
        BravoLargeBody,
        BravoSubhead,
        BravoBody,
        BravoBodyBold,
        BravoBodyUnderline,
        BravoParagraph,
        BravoCaption1,
        <PERSON><PERSON>aption2,
        Bravo<PERSON>aption3,
        BravoSmallCaption,
    },
};
```

## Available Components

| Component          | Description          | HTML Element | Font Size | Font Weight    | Text Transform |
| ------------------ | -------------------- | ------------ | --------- | -------------- | -------------- |
| BravoLargeTitle    | Large title text     | h1           | 20px      | 600 (semibold) | none           |
| BravoTitle1        | Primary title        | h2           | 18px      | 500            | none           |
| BravoTitle2        | Secondary title      | h2           | 16px      | 500            | none           |
| BravoTitle3        | Tertiary title       | h3           | 15px      | 500            | none           |
| BravoHeadline      | Headline text        | h3           | 14px      | 500            | uppercase      |
| BravoLargeBody     | Large body text      | div          | 14px      | 500            | none           |
| BravoSubhead       | Subheading text      | div          | 13px      | 500            | none           |
| BravoBody          | Standard body text   | p            | 13px      | 400            | none           |
| BravoBodyBold      | Bold body text       | p            | 13px      | 700            | none           |
| BravoBodyUnderline | Underlined body text | p            | 13px      | 500            | none           |
| BravoParagraph     | Paragraph text       | p            | 13px      | 400            | none           |
| BravoCaption1      | Primary caption      | span         | 12px      | 500            | none           |
| BravoCaption2      | Secondary caption    | span         | 12px      | 400            | none           |
| BravoCaption3      | Tertiary caption     | span         | 12px      | 500            | uppercase      |
| BravoSmallCaption  | Small caption text   | span         | 11px      | 400            | none           |

## Usage

```vue
<template>
    <div>
        <BravoLargeTitle>Large Title</BravoLargeTitle>
        <BravoTitle1>Title 1</BravoTitle1>
        <BravoTitle2>Title 2</BravoTitle2>
        <BravoTitle3>Title 3</BravoTitle3>
        <BravoHeadline>Headline</BravoHeadline>
        <BravoLargeBody>Large body text for important content.</BravoLargeBody>
        <BravoSubhead>Subheading text for section introductions.</BravoSubhead>
        <BravoBody>This is regular body text for general content.</BravoBody>
        <BravoBodyBold>This is bold body text for emphasis.</BravoBodyBold>
        <BravoBodyUnderline>This is underlined body text for links or highlights.</BravoBodyUnderline>
        <BravoParagraph>This is paragraph text for longer content blocks.</BravoParagraph>
        <BravoCaption1>Primary caption for images or UI elements.</BravoCaption1>
        <BravoCaption2>Secondary caption for additional information.</BravoCaption2>
        <BravoCaption3>TERTIARY CAPTION FOR LABELS</BravoCaption3>
        <BravoSmallCaption>Small caption for fine print or metadata.</BravoSmallCaption>
    </div>
</template>

<script>
import {
    BravoLargeTitle,
    BravoTitle1,
    BravoTitle2,
    BravoTitle3,
    BravoHeadline,
    BravoLargeBody,
    BravoSubhead,
    BravoBody,
    BravoBodyBold,
    BravoBodyUnderline,
    BravoParagraph,
    BravoCaption1,
    BravoCaption2,
    BravoCaption3,
    BravoSmallCaption,
} from '@/components/BravoTypography';

export default {
    components: {
        BravoLargeTitle,
        BravoTitle1,
        BravoTitle2,
        BravoTitle3,
        BravoHeadline,
        BravoLargeBody,
        BravoSubhead,
        BravoBody,
        BravoBodyBold,
        BravoBodyUnderline,
        BravoParagraph,
        BravoCaption1,
        BravoCaption2,
        BravoCaption3,
        BravoSmallCaption,
    },
};
</script>
```
