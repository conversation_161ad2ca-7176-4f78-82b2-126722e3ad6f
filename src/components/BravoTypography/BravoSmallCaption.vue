<template>
  <span class="small-caption">
    <slot></slot>
  </span>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
    name: 'BravoSmallCaption',
});
</script>

<style scoped>
.small-caption {
    font-size: 12px;
    font-weight: 400;
    color: var(--text-color-secondary);
    text-transform: none;
    margin: 0;
    padding: 0;
}
</style>
