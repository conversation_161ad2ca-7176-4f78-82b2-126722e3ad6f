<template>
  <p class="body-underline">
    <slot></slot>
  </p>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
    name: 'BravoBodyUnderline',
});
</script>

<style scoped>
.body-underline {
    font-size: 14px;
    font-weight: 500;
    color: var(--color-text-link-static);
    text-transform: none;
    text-decoration: underline;
    margin: 0;
    padding: 0;
}
</style>
