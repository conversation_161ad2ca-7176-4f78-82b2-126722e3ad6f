<script setup lang="ts">
import Select from 'primevue/select';
import { ref, computed } from 'vue';

interface Props {
    text?: boolean;
    view?: boolean;
    invalid?: any;
    modelValue?: string | number | null;
    options?: Array<{ [key: string]: string | number }>;
    optionLabel?: string;
    optionValue?: string;
    placeholder?: string;
    id: string;
    dataTestId: string;
}

const props = withDefaults(defineProps<Props>(), {
    text: false,
    view: false,
    options: () => [],
    optionLabel: 'label',
    optionValue: 'value',
    placeholder: '',
});

const emit = defineEmits(['focus', 'blur', 'update:modelValue']);
const isEditing = ref(false);
const selectRef = ref();

const displayValue = computed(() => {
    if (!props.modelValue) return props.placeholder;
    const selectedOption = props.options.find((opt) => String(opt[props.optionValue]) === String(props.modelValue));
    return selectedOption ? selectedOption[props.optionLabel] : props.modelValue;
});

const handleFocus = (event: Event) => {
    isEditing.value = true;
    emit('focus', event);
};

const handleBlur = (event: Event) => {
    // Only relevant for view mode
    if (props.view) {
        setTimeout(() => {
            const target = (event as FocusEvent).relatedTarget as HTMLElement | null;
            const overlay = document.querySelector('.p-overlay-visible');
            if (target && (overlay?.contains(target) || selectRef.value?.$el.contains(target))) {
                return;
            }
            isEditing.value = false;
            emit('blur', event);
        }, 100);
    } else {
        // For standard mode, just emit the blur event
        emit('blur', event);
    }
};

const focusAndExpand = async () => {
    isEditing.value = true;
    // Wait for the select component to be rendered
    await new Promise((resolve) => setTimeout(resolve, 0));
    if (selectRef.value) {
        selectRef.value.$el.click();
    }
};

const handleClick = () => {
    if (props.view && !isEditing.value) {
        focusAndExpand();
    }
};

const handleUpdateValue = (val: string | number | null) => {
    emit('update:modelValue', val);
};
</script>

<template>
  <div
    class="bravo-selectfield-wrapper"
    @click="handleClick"
  >
    <Select
      v-if="!view || (view && isEditing)"
      :id="props.id"
      ref="selectRef"
      class="bravo-selectfield"
      :class="{ 'p-selectfield-text': text }"
      :model-value="modelValue"
      :options="options"
      :option-label="optionLabel"
      :option-value="optionValue"
      :placeholder="placeholder"
      :invalid="invalid"
      :data-testid="props.dataTestId"
      v-bind="$attrs"
      @update:model-value="handleUpdateValue"
      @focus="handleFocus"
      @blur="handleBlur"
    >
      <!-- Forward all slots to the underlying Select component -->
      <template
        v-for="(_, name) in $slots"
        #[name]="slotData"
      >
        <slot
          :name="name"
          v-bind="slotData || {}"
        />
      </template>
    </Select>
    <div
      v-else
      class="bravo-selectfield-view"
      :class="{ placeholder: !modelValue }"
    >
      <!-- If we have a value slot, use it for the view mode as well -->
      <slot 
        v-if="$slots.value && modelValue" 
        name="value" 
        :value="options.find(opt => String(opt[optionValue]) === String(modelValue))"
      />
      <template v-else>
        {{ displayValue }}
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.bravo-selectfield-wrapper {
    display: inline-block;
    width: 100%;
}

.bravo-selectfield-view {
    padding: 0.5rem;
    border: 1px dashed transparent;
    min-height: 2.5rem;
    display: flex;
    align-items: center;
    color: var(--surface-850);
    cursor: pointer;

    &.placeholder {
        color: var(--surface-400);
    }

    &:hover {
        border-radius: 6px;
        background-color: var(--surface-100);
    }
}

.p-selectfield-text {
    background: transparent;
    border: none;
    padding: 0;
    color: var(--p-button-text-primary-color);
    box-shadow: none;

    &:not(.p-disabled) {
        &:hover {
            background: var(--p-button-text-primary-hover-background);
            color: var(--p-button-text-primary-hover-color);
        }

        &:active {
            background: var(--p-button-text-primary-active-background);
            color: var(--p-button-text-primary-active-color);
        }
    }
}
</style>
