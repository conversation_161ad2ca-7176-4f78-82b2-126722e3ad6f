<script setup lang="ts">
import Tab from 'primevue/tab';

interface Props {
    value: string | number;
    disabled?: boolean;
    as?: string;
    asChild?: boolean;
}

const props = defineProps<Props>();

defineOptions({
    name: 'BravoTab',
});
</script>

<template>
  <Tab
    class="bravo-tab"
    :value="props.value"
    :disabled="props.disabled"
    :as="props.as"
    :as-child="props.asChild"
    v-bind="$attrs"
  >
    <slot></slot>
  </Tab>
</template>

<style lang="scss" scoped>
</style> 