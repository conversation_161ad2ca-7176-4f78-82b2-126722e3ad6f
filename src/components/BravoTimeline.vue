<script setup lang="ts">
import Timeline from 'primevue/timeline';
import BravoCaption2 from './BravoTypography/BravoCaption2.vue';
import BravoBody from './BravoTypography/BravoBody.vue';
import BravoTimestamp from './BravoTimestamp.vue';
import BravoSubhead from './BravoTypography/BravoSubhead.vue';

interface TimelineItem {
    title: string;
    user: string;
    datetime: string | number; // Changed from timestamp to datetime to match BravoTimestamp props
    content: string;
    icon?: string; // Optional icon class name (e.g. 'pi pi-check')
}

interface Props {
    value: TimelineItem[];
    align?: 'left' | 'right' | 'alternate' | 'top' | 'bottom';
    layout?: 'vertical' | 'horizontal';
    useIcons?: boolean; // Whether to use icons instead of dots
}

defineProps<Props>();
</script>

<template>
  <Timeline
    class="bravo-timeline"
    v-bind="$props"
  >
    <template
      v-if="useIcons"
      #marker="slotProps"
    >
      <span class="custom-marker">
        <i :class="slotProps.item.icon || 'pi pi-circle-fill'"></i>
      </span>
    </template>
    <template #content="slotProps">
      <div class="timeline-item">
        <BravoSubhead>{{ slotProps.item.title }}</BravoSubhead>
        <div class="timeline-metadata">
          <BravoCaption2>{{ slotProps.item.user }}</BravoCaption2>
          <BravoTimestamp 
            class="timestamp"
            :datetime="slotProps.item.datetime"
          />
        </div>
        <!-- Content section with customizable inner content -->
        <div class="timeline-content">
          <!-- Use custom content if slot is provided -->
          <slot 
            v-if="$slots['item-content']" 
            name="item-content"
            :item="slotProps.item"
          >
          </slot>
          
          <!-- Default content rendering if no slot is provided -->
          <template v-else>
            <BravoBody>
              {{ slotProps.item.content }}
            </BravoBody>
          </template>
        </div>
      </div>
    </template>
  </Timeline>
</template>

<style lang="scss" scoped>
.bravo-timeline {
    :deep(.p-timeline) {
        padding: 0;
        margin-left: 0;
    }

    :deep(.p-timeline-event-content) {
        padding: 0 1rem;
        margin-top: -0.15rem;
    }

    :deep(.p-timeline-event-opposite) {
        display: none;
    }

    :deep(.p-timeline-event-connector) {
        top: 1.5rem;
    }

    :deep(.custom-marker) {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        background-color: var(--surface-card);
        border: 1px solid var(--surface-200);

        i {
            font-size: 1rem;
            color: var(--icon-color-primary);
        }
    }
}

.timeline-item {
    padding-bottom: 1.5rem;

    .timeline-metadata {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0.1rem 0;
    }

    .timeline-content {
        margin-top: 0.5rem;
    }
}
</style>
