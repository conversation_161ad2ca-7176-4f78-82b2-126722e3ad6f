<script setup lang="ts">
import Button from 'primevue/button';
import Tooltip from 'primevue/tooltip';
import BravoAvatar from './BravoAvatar.vue';
import { ref, watch } from 'vue';
import { NavItem } from '../types/navigation';

const props = defineProps<{
    items: NavItem[];
    bottomItems?: NavItem[];
}>();

// Register tooltip directive
const vTooltip = Tooltip;

const emit = defineEmits<{
    (e: 'nav-click', item: NavItem): void;
    (e: 'update:items', items: NavItem[]): void;
    (e: 'update:bottomItems', items: NavItem[]): void;
}>();

// Create reactive copies of items
const localItems = ref<NavItem[]>([]);
const localBottomItems = ref<NavItem[]>([]);

// Initialize and watch for props changes
watch(
    () => props.items,
    (newItems) => {
        localItems.value = newItems.map((item) => ({ ...item }));
    },
    { immediate: true }
);

watch(
    () => props.bottomItems,
    (newItems) => {
        if (newItems) {
            localBottomItems.value = newItems.map((item) => ({ ...item }));
        } else {
            localBottomItems.value = [];
        }
    },
    { immediate: true }
);

const handleNavClick = (clickedItem: NavItem, isBottomItem = false) => {
    if (isBottomItem) {
        // Update active states for bottom items
        localBottomItems.value = localBottomItems.value.map((item) => ({
            ...item,
            active: item.id === clickedItem.id,
        }));

        // Emit both the clicked item and the updated items array
        emit('nav-click', clickedItem);
        emit('update:bottomItems', localBottomItems.value);
    } else {
        // Update active states for main items
        localItems.value = localItems.value.map((item) => ({
            ...item,
            active: item.id === clickedItem.id,
        }));

        // Emit both the clicked item and the updated items array
        emit('nav-click', clickedItem);
        emit('update:items', localItems.value);
    }
};
</script>

<template>
  <nav class="bravo-main-navigation">
    <div class="nav-items">
      <Button
        v-for="item in localItems.filter(item => item.visible !== false)"
        :key="item.id"
        v-tooltip="item.tooltip"
        :class="{ 'nav-item': true, active: item.active }"
        text
        @click="handleNavClick(item)"
      >
        <template v-if="item.avatar">
          <BravoAvatar 
            :image="item.avatar.image"
            :first-name="item.avatar.firstName"
            :last-name="item.avatar.lastName"
            :size="item.avatar.size"
            :shape="item.avatar.shape"
            :style="item.avatar.style"
            :background-color="item.avatar.backgroundColor"
            class="bravo-avatar"
          />
        </template>
        <template v-else>
          <i :class="item.icon"></i>
        </template>
        <span class="sr-only">{{ item.label }}</span>
      </Button>
    </div>

    <div
      v-if="localBottomItems.length > 0"
      class="bottom-items"
    >
      <Button
        v-for="item in localBottomItems.filter(item => item.visible !== false)"
        :key="item.id"
        v-tooltip="item.tooltip"
        :class="{ 'nav-item': true, active: item.active }"
        text
        @click="handleNavClick(item, true)"
      >
        <template v-if="item.avatar">
          <BravoAvatar 
            :image="item.avatar.image"
            :first-name="item.avatar.firstName"
            :last-name="item.avatar.lastName"
            :size="item.avatar.size"
            :shape="item.avatar.shape"
            :style="item.avatar.style"
            :background-color="item.avatar.backgroundColor"
            class="bravo-avatar"
          />
        </template>
        <template v-else>
          <i :class="item.icon"></i>
        </template>
        <span class="sr-only">{{ item.label }}</span>
      </Button>
    </div>
  </nav>
</template>

<style lang="scss" scoped>
.bravo-main-navigation {
    width: 56px;
    height: 100%;
    background-color: var(--surface-100);
    display: flex;
    flex-direction: column;
    padding: 0.5rem 0;
}

.nav-items {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    align-items: center;
    flex: 1;
}

.bottom-items {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    align-items: center;
    margin-top: auto;
    padding-top: 0.5rem;
}

:deep(.nav-item.p-button) {
    width: 40px;
    height: 40px;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;
    color: var(--surface-600);

    &:hover {
        background-color: rgba(0, 0, 0, 0.04);
        color: var(--surface-850);
    }

    &.active {
        color: var(--surface-850);
        background-color: var(--surface-200);
    }

    i {
        font-size: 1.25rem;
    }
    
    .bravo-avatar, :deep(.p-avatar) {
        width: 40px;
        height: 40px;
        font-size: 0.8rem;
    }
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
</style>
