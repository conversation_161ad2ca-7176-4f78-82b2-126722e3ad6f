<script setup lang="ts">
import Panel from 'primevue/panel';

const props = defineProps<{
    header?: string;
    toggleable?: boolean;
    collapsed?: boolean;
}>();
</script>

<template>
  <Panel
    class="bravo-panel"
    :header="props.header"
    :toggleable="props.toggleable"
    :collapsed="props.collapsed"
    v-bind="$attrs"
  >
    <template
      v-for="(_, name) in $slots"
      :key="name"
      #[name]
    >
      <slot :name="name"></slot>
    </template>
  </Panel>
</template>

<style lang="scss" scoped></style>
