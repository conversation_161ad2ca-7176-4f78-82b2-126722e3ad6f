<script setup lang="ts">
import { ref, computed } from 'vue';
import BravoButton from './BravoButton.vue';
import BravoPassword from './BravoPassword.vue';

const email = ref('');
const password = ref('');
const confirmPassword = ref('');
const loading = ref(false);
const error = ref('');
const isResetSuccess = ref(false);

const props = defineProps<{
  userEmail?: string;
  submitHandler?: (password: string) => Promise<void>;
}>();

const emit = defineEmits<{
  submit: [{ password: string }];
  backToLogin: [];
}>();

// Set email from props if provided
if (props.userEmail) {
  email.value = props.userEmail;
}

const passwordsMatch = computed(() => {
  if (!password.value || !confirmPassword.value) return true;
  return password.value === confirmPassword.value;
});

const handleSubmit = async () => {
    if (!password.value) {
        error.value = 'Please enter a password';
        return;
    }

    if (!confirmPassword.value) {
        error.value = 'Please confirm your password';
        return;
    }

    if (!passwordsMatch.value) {
        error.value = 'Passwords do not match';
        return;
    }

    loading.value = true;
    error.value = '';

    try {
        // Emit the submit event with the password
        emit('submit', { password: password.value });
        
        // If submitHandler prop is provided, call it
        if (props.submitHandler) {
            await props.submitHandler(password.value);
        }
        
        // Set success state
        isResetSuccess.value = true;
    } catch (err) {
        error.value = err instanceof Error ? err.message : 'Failed to reset password. Please try again.';
    } finally {
        loading.value = false;
    }
};

const handleBackToLogin = () => {
    emit('backToLogin');
};
</script>

<template>
  <div class="login-wrapper">
    <div class="content-column">
      <div class="login-container">
        <!-- Reset Form -->
        <div v-if="!isResetSuccess">
          <h1>Create a new password</h1>
          <p class="subtitle">
            Create a new password for <strong>{{ email }}</strong> below to log in.
          </p>
          
          <div
            v-if="error"
            class="error-message"
          >
            {{ error }}
          </div>

          <form @submit.prevent="handleSubmit">
            <div class="form-group password-field-container">
              <label>Password</label>
              <BravoPassword
                v-model="password"
                toggle-mask
                :feedback="false"
                :disabled="loading"
                tabindex="1"
              />
            </div>

            <div class="form-group password-field-container">
              <label>Confirm Password</label>
              <BravoPassword
                v-model="confirmPassword"
                toggle-mask
                :feedback="false"
                :disabled="loading"
                tabindex="2"
              />
            </div>

            <BravoButton
              type="submit"
              label="Reset Password"
              class="submit-button"
              tabindex="3"
              :loading="loading"
              :disabled="loading"
            />
          </form>
        </div>
        
        <!-- Success Screen -->
        <div
          v-else
          class="success-container"
        >
          <h1>Password reset complete</h1>
          <p class="subtitle">
            Your password has been reset successfully. You can now log in with your new password.
          </p>
          
          <BravoButton
            label="Back to Login"
            class="back-to-login-button"
            tabindex="1"
            @click="handleBackToLogin"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.content-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 440px;
}

.login-container {
    width: 100%;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
}

.login-container h1 {
    font-size: 20px;
    line-height: 28px;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--surface-850);
    text-align: left;
}

.subtitle {
    font-size: 14px;
    line-height: 20px;
    color: var(--surface-700);
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 14px;
    color: var(--surface-850);
}

.submit-button, .back-to-login-button {
    width: 100%;
    background: var(--primary-600);
    padding: 0.75rem;
    font-weight: 500;
    margin: 1rem 0 1rem 0;
    min-height: 44px;
}

:deep(.p-password) {
    width: 100%;
    display: block;
}

:deep(.p-password input) {
    width: 100%;
    height: 44px !important;
}

.error-message {
    background-color: var(--red-100);
    color: var(--red-700);
    padding: 0.75rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    font-size: 14px;
}

.success-container strong {
    font-weight: 600;
}

@media (min-width: 640px) {
    .login-container {
        padding: 3rem;
    }

    .login-container h1 {
        font-size: 22px;
        padding-bottom: 0.5rem;
    }
}

@media (min-width: 768px) {
    .login-container {
        padding: 4rem;
    }

    .login-container h1 {
        font-size: 24px;
        padding-bottom: 0.5rem;
    }
}
</style> 