<script setup lang="ts">
import { ref, computed, toRefs, onMounted, onBeforeUpdate } from 'vue';

// @ts-ignore
import Calendar from 'primevue/calendar';
import Dialog from 'primevue/dialog';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import InputNumber from 'primevue/inputnumber';
import InputGroup from 'primevue/inputgroup';

const props = defineProps({
    modelValue: {
        type: [String, Date],
        default: '',
    },
    type: {
        type: String,
        default: 'date',
    },
});

const { modelValue } = toRefs(props);
const { type } = toRefs(props);

const emit = defineEmits(['update:modelValue', 'relativeDatePickerApply', 'relativeDatePickerCancel']);

const TYPE_DATE = 'date';
const TYPE_DURATION = 'duration';

const DAYS_PER_WEEK = 7;
const HOURS_PER_DAY = 24;

const disabled = false;

const UNITS_HOURS = 0;
const UNITS_DAYS = 1;
const UNITS_WEEKS = 2;

const selectedUnit = ref(UNITS_HOURS);
const isPopupVisible = ref(false);
const selectedDate = ref<Date>(new Date());

const unitQuantity = ref(0);
const currentUnit = ref(UNITS_HOURS);
const hours = ref();
const minutes = ref();
const isAM = ref();

const isModeFixed = computed(() => type.value.toLowerCase() !== TYPE_DURATION);

const unitTabs = [
    {
        id: UNITS_HOURS,
        identifier: 'hours',
        abbr: 'H',
        text: 'Hours',
        icon: null,
        content: 'User tab content',
    },
    {
        id: UNITS_DAYS,
        identifier: 'days',
        abbr: 'D',
        text: 'Days',
        icon: null,
        content: 'Comment tab content',
    },
    {
        id: UNITS_WEEKS,
        identifier: 'weeks',
        abbr: 'W',
        text: 'Weeks',
        icon: null,
        content: 'Comment tab content',
    },
];

const isTypeDuration = computed(() => type.value.toLowerCase() === TYPE_DURATION);
const isTypeDate = computed(() => type.value.toLowerCase() !== TYPE_DURATION);

const inputType = ref(props.type);

onMounted(() => {
    if (isTypeDuration.value) {
        getDurationSettings();
    } else if (isTypeDate.value) {
        getCurrentDateSettings();
    }
});

onBeforeUpdate(() => {
    if (isTypeDuration.value) {
        getDurationSettings();
    } else if (isTypeDate.value) {
        getCurrentDateSettings();
    }
});

function getCurrentDateSettings() {
    selectedDate.value = modelValue?.value ? new Date(modelValue.value) : new Date(Date.now());
    isAM.value = selectedDate.value.getHours() < 12;
    hours.value = selectedDate.value.getHours() % 12 || 12;
    minutes.value = selectedDate.value.getMinutes();
}
function parseFromISO8601(duration: string) {
    const values: string[] | null = duration.match(/^(PT|P)([\d]+)([D|H|W])/);

    if (values && values.length === 4) {
        const tab = unitTabs.find((x) => x.abbr === values[3]);

        if (tab) {
            return {
                quantity: parseInt(values[2]),
                unit: tab.identifier,
            };
        }
    }
    return null;
}
function getDurationSettings() {
    if (modelValue?.value && typeof modelValue.value === 'string') {
        const values = parseFromISO8601(modelValue.value);
        if (values) {
            const tab = unitTabs.find((x) => x.identifier === values['unit']);

            if (tab) {
                selectedUnit.value = tab.id;
                unitQuantity.value = values['quantity'];
                currentUnit.value = selectedUnit.value;
            }
        }
    }
}

// @ts-ignore
const localValue = computed({
    get() {
        if (isTypeDuration.value) {
            if (typeof modelValue.value === 'string') {
                const value = parseFromISO8601(modelValue.value) ?? { quantity: 0, unit: 'hours' };
                return `in ${value['quantity']} ${value['unit']}`;
            } else {
                return '';
            }
        } else {
            return typeof modelValue.value === 'string' ? new Date(modelValue.value) : modelValue.value;
        }
    },
    set(val) {
        console.log('localValue set', val);
        emit('update:modelValue', val);
    },
});

function onBoxClicked(event: any) {
    event.preventDefault();
    event.stopPropagation();
    isPopupVisible.value = true;
}

function onAM() {
    isAM.value = true;
}

function onPM() {
    isAM.value = false;
}

function onCancel() {
    isPopupVisible.value = false;
    console.log('onCancel');
}

function onApply() {
    isPopupVisible.value = false;
    if (isTypeDuration.value) {
        const duration = getDuration();
        console.log('duration', duration);
        localValue.value = duration;
    } else {
        const fixedDate = getFixedDate();
        console.log('getFixedDate', fixedDate);
        localValue.value = fixedDate;
    }
}

function getFixedDate(): string {
    let date = selectedDate.value;
    if (isModeFixed.value) {
        date.setHours(isAM.value ? hours.value : hours.value + 12);
        date.setMinutes(minutes.value);
        return date.toISOString();
    }
    let durationHours = 0;
    if (currentUnit.value == UNITS_HOURS) {
        durationHours = unitQuantity.value;
    } else if (currentUnit.value == UNITS_DAYS) {
        durationHours = unitQuantity.value * HOURS_PER_DAY;
    } else if (currentUnit.value == UNITS_WEEKS) {
        durationHours = unitQuantity.value * DAYS_PER_WEEK * HOURS_PER_DAY;
    }
    date = new Date(Date.now());
    date.setHours(date.getHours() + durationHours);
    return date.toISOString();
}

function getDuration(): string {
    // Duration Format P[n]Y[n]M[n]DT[n]H[n]M[n]S
    let duration = 'P';
    const quantity = String(unitQuantity.value);
    if (currentUnit.value == UNITS_HOURS) {
        duration += 'T' + quantity + 'H';
    } else if (currentUnit.value == UNITS_DAYS) {
        duration += quantity + 'D';
    } else if (currentUnit.value == UNITS_WEEKS) {
        duration += quantity + 'W';
    }
    return duration;
}
</script>

<template>
  <div class="relative-datebox">
    <!-- TOP-LEVEL INPUT FIELDS  -->
    <div v-if="isTypeDate">
      <div
        class="calendar-input-mask"
        tabindex="1"
        @focus="onBoxClicked"
        @click="onBoxClicked"
      ></div>
      <InputGroup>
        <!-- @ts-ignore: v-model expects Date but can be string -->
        <Calendar
          id="calendar-12h"
          v-model="localValue as Date"
          class="disabled-calendar-input"
          show-time
          hour-format="12"
          date-format="m/d/yy"
          data-testid="picker-datebox"
          tabindex="-1"
          :disabled="true"
        />
      </InputGroup>
    </div>
    <div v-if="isTypeDuration">
      <InputGroup>
        <InputText
          :value="localValue"
          data-testd="picker-textbox"
          @focus="onBoxClicked"
        />
        <Button
          icon="pi pi-calendar"
          :outlined="true"
          @focus="onBoxClicked"
        />
      </InputGroup>
    </div>
    <!-- MODAL -->
    <Dialog
      v-model:visible="isPopupVisible"
      :show-header="false"
      :show-footer="false"
      :dismissable-mask="true"
    >
      <div class="datebox-dialog tasks-ui-popup-content">
        <div class="relative-datebox-container">
          <div class="relative-datebox-toggle">
            <InputGroup>
              <Button
                v-if="type === TYPE_DATE"
                :outlined="inputType === TYPE_DURATION"
                label="Fixed Date"
                size="large"
                @click="inputType = TYPE_DATE"
              />
              <Button
                :outlined="inputType === TYPE_DATE"
                label="Relative Date"
                size="large"
                @click="inputType = TYPE_DURATION"
              />
            </InputGroup>
          </div>
          <div v-if="inputType === TYPE_DATE">
            <div
              class="tasks-ui-calendar-container"
              data-testid="picker-fixed-view"
            >
              <div class="fixed-calendar">
                <Calendar
                  v-model="selectedDate"
                  :inline="true"
                  :disabled="disabled"
                  data-testid="picker-calendar"
                ></Calendar>
              </div>

              <div class="tasks-ui-time-container">
                <InputNumber
                  v-model="hours"
                  class="fixed-hours"
                  show-buttons
                  :min="1"
                  :max="12"
                  data-testid="picker-hours"
                />
                <span class="tasks-ui-time-delimiter">:</span>
                <InputNumber
                  v-model="minutes"
                  class="fixed-minutes"
                  show-buttons
                  :min="0"
                  :max="59"
                  data-testid="picker-minutes"
                  :format="true"
                  :prefix="minutes > 9 ? '' : '0'"
                />
                <div class="tasks-ui-meridiem-container">
                  <Button
                    :class="{ 'tod-selected': isAM }"
                    label="AM"
                    :outlined="true"
                    data-testid="picker-button-am"
                    @click="onAM"
                  />
                  <Button
                    :class="{ 'tod-selected': !isAM }"
                    label="PM"
                    :outlined="true"
                    data-testid="picker-button-pm"
                    @click="onPM"
                  />
                </div>
              </div>
            </div>
          </div>
          <div v-if="inputType === TYPE_DURATION">
            <div
              data-testid="picker-relative-view"
              class="datebox-duration"
            >
              <div class="duration-unit-amount">
                <InputNumber
                  v-model="unitQuantity"
                  class="duration-unit-amount"
                  aria-label="Integer Format"
                  data-testid="picker-unit-quantity"
                  show-buttons
                  :min="0"
                />
              </div>

              <InputGroup class="duration-unit-toggle">
                <Button
                  :outlined="currentUnit != 0"
                  label="Hours"
                  size="large"
                  @click="currentUnit = 0"
                />
                <Button
                  :outlined="currentUnit != 1"
                  label="Days"
                  size="large"
                  @click="currentUnit = 1"
                />
                <Button
                  :outlined="currentUnit != 2"
                  label="Weeks"
                  size="large"
                  @click="currentUnit = 2"
                />
              </InputGroup>
            </div>
          </div>
        </div>
        <div class="datebox-ctas">
          <Button
            label="Cancel"
            :outlined="true"
            data-testid="picker-button-cancel"
            size="large"
            @click="onCancel"
          />
          <Button
            label="Apply"
            data-testid="picker-button-apply"
            size="large"
            @click="onApply"
          />
        </div>
      </div>
    </Dialog>
  </div>
</template>

<style scoped lang="scss">
.tasks-ui-time-container {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    align-items: center;
    margin-top: 20px;
}
.tasks-ui-popup-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    margin-top: 20px;
}
.tasks-ui-meridiem-container {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
}
.tasks-ui-time-delimiter {
    font-size: 18px;
    font-weight: 600;
    line-height: normal;
}

.tasks-ui-relative-date {
    display: flex;
    align-items: center;
    gap: 10px;
}

.tasks-ui-calendar-container {
    width: 100%;
}

.tod-selected {
    background-color: var(--color-button-secondary-surface-pressed);
    border-color: var(--color-button-secondary-border-pressed-focus);
}

.relative-datebox-container {
    width: 100%;
}

.calendar-input-mask {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 1;
    cursor: pointer;
}
.relative-datebox {
    position: relative;
}
.relative-datebox-toggle {
    Button {
        width: 50%;
        margin: auto;
    }
}

.datebox-duration {
    margin-top: 36px;
    display: flex;
    justify-content: space-between;
}

.fixed-calendar {
    margin-top: 36px;
}

.fixed-minutes,
.fixed-hours,
.duration-unit-amount {
    &:deep(.p-inputnumber-input) {
        width: 60px;
    }
    &:deep(.p-button) {
        background: #ffffff;
    }
}

.duration-unit-toggle {
    width: 252px;
}

.datebox-ctas {
    margin: 24px 0px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 16px;
    align-self: stretch;
}

.datebox-dialog {
    width: 400px;
    max-width: 100%;
}

.disabled-calendar-input {
    &:deep(.p-inputtext) {
        opacity: 1;
    }
}
</style>
