<script setup lang="ts">
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import { ref, computed } from 'vue';

type DataTableProps = {
    columns?: any[];
    value: any[];
    paginator: boolean;
    rowHover: boolean;
    dataKey: string;
    rows: number;
    rowsPerPageOptions: number[];
    tableStyle: any;
    stripedRows: boolean;
    showGridlines: boolean;
};

const props = withDefaults(defineProps<DataTableProps>(), {
    columns: () => []
});
const selectedProducts = ref([]);

// Determine if we should use the columns-based rendering
const useColumnsRendering = computed(() => Array.isArray(props.columns) && props.columns.length > 0);
</script>

<template>
  <DataTable 
    v-model:selection="selectedProducts" 
    class="bravo-datatable" 
    v-bind="{ ...props, ...$attrs }"
  >
    <!-- When columns are provided, render them dynamically -->
    <template v-if="useColumnsRendering">
      <Column
        v-if="columns.some((col) => col.selectionMode === 'multiple')"
        selection-mode="multiple"
        header-style="width: 3rem"
      />
      <Column
        v-for="col in props.columns"
        :key="col.id"
        :field="col.field"
        :header="col.header"
        :sortable="col.sortable"
        :filter-match-mode="col.filterMatchMode"
      >
        <!-- If bodyTemplate is a function, use render function -->
        <template
          v-if="typeof col.bodyTemplate === 'function'"
          #body="slotProps"
        >
          <template v-if="typeof col.bodyTemplate(slotProps.data) === 'string'">
            <span v-html="col.bodyTemplate(slotProps.data)"></span>
          </template>
          <template v-else>
            <component :is="col.bodyTemplate(slotProps.data)"></component>
          </template>
        </template>

        <!-- Default fallback -->
        <template
          v-else
          #body="slotProps"
        >
          {{ slotProps.data[col.field] }}
        </template>
      </Column>
    </template>
        
    <!-- When no columns provided, just pass through slots -->
    <template v-else>
      <slot></slot>
    </template>
  </DataTable>
</template>

<style lang="scss" scoped></style>
