<script setup lang="ts">
import Stepper from 'primevue/stepper';

// TODO: export these components
// import StepList from 'primevue/steplist';
// import StepPanels from 'primevue/steppanels';
// import Step from 'primevue/step';
// import StepPanel from 'primevue/steppanel';
</script>

<template>
  <Stepper
    class="bravo-stepper"
    v-bind="$attrs"
  >
    <template
      v-for="(_, name) in $slots"
      :key="name"
      #[name]
    >
      <slot :name="name"></slot>
    </template>
  </Stepper>
</template>

<style lang="scss" scoped></style>
