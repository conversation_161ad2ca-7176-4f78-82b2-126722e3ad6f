<script setup lang="ts">
import InputGroup from 'primevue/inputgroup';
import InputGroupAddon from 'primevue/inputgroupaddon';

// Re-export the addon component so it can be used alongside InputGroup
defineExpose({ InputGroupAddon });
</script>

<template>
  <InputGroup
    class="bravo-inputgroup"
    v-bind="$attrs"
  >
    <slot></slot>
  </InputGroup>
</template>

<style lang="scss" scoped></style>
