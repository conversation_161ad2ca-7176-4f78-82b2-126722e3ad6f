<script lang="ts" setup>
type BaseLoaderCustomerSummaryProps = {
    isLoading: boolean;
};
const props = withDefaults(defineProps<BaseLoaderCustomerSummaryProps>(), {});
</script>

<template>
  <div
    v-if="props.isLoading"
    class="cxm-loader-customer-summary"
  >
    <div class="loader-container-one">
      <div class="loader-cell h18 w30"></div>
      <div class="loader-cell h16 w75"></div>
      <div class="loader-cell h16 w85"></div>
      <div class="loader-cell h16 w75"></div>
      <div class="loader-cell h16 w60"></div>
      <div class="loader-cell h16 w25"></div>

      <div class="loader-cell h14 w0"></div>
      <div class="loader-cell h16 w60"></div>
      <div class="loader-cell h16 w75 t2"></div>
      <div class="loader-cell h16 w85 t2"></div>
      <div class="loader-cell h16 w60 t3"></div>
      <div class="loader-cell h16 w40 t3"></div>
    </div>
  </div>
</template>

<style scoped>
.loader-container-one {
    width: 100%;
    background-size: 100%;
    margin: 12px 8px 12px 8px;
    display: grid;
    background-color: none;
    align-content: left;
    grid-template-columns: 100%;
    /* Four columns */
    grid-template-rows: 20px;
    /* Five rows */
    gap: 10px;
    /* Adjust the gap between cells as needed */
}

/* Skeleton Loader Cell (Rectangular Blocks) */
.loader-cell {
    background-color: #e4e5e7;
    /* Light gray background color */
    border-radius: 4px;
    /* Rounded corners */
    /*animation: pulse 1.5s infinite; /* Add animation to create the pulsating effect */
    background: -webkit-gradient(
        linear,
        left top,
        right top,
        color-stop(8%, rgba(130, 130, 130, 0.2)),
        color-stop(18%, rgba(130, 130, 130, 0.3)),
        color-stop(33%, rgba(130, 130, 130, 0.2))
    );
    background: linear-gradient(
        to right,
        rgba(130, 130, 130, 0.2) 8%,
        rgba(130, 130, 130, 0.3) 18%,
        rgba(130, 130, 130, 0.2) 33%
    );
    background-size: 100vw 100vh;
    animation: shine-lines 2.5s infinite ease-out;
}

.loader-cell-inline {
    display: inline-block;
}

.hr {
    display: block;
    height: 1px;
    border: 0;
    margin: 20px 0px 20px 0px;
    border-top: 1px solid #e4e5e7;
}

.h24 {
    height: 24px;
    margin-bottom: 15px;
}

.h20 {
    height: 20px;
    margin-bottom: 0px;
}

.h18 {
    height: 18px;
}

.h16 {
    height: 16px;
}

.h14 {
    height: 14px;
}

.w0 {
    width: 0%;
}

.w5 {
    width: 5%;
}

.w15 {
    width: 15%;
}

.w16px {
    width: 16px;
}

.w24px {
    width: 24px;
}

.w5 {
    width: 5%;
}

.w7 {
    width: 7%;
}

.w10 {
    width: 10%;
}

.w15 {
    width: 15%;
}

.w25 {
    width: 25%;
}

.w30 {
    width: 30%;
}

.w40 {
    width: 40%;
}

.w50 {
    width: 50%;
}

.w60 {
    width: 60%;
}

.w75 {
    width: 75%;
}

.w85 {
    width: 85%;
}

.padding3Right {
    margin-right: 3%;
}

.t2 {
    opacity: 0.6 !important;
}

.t3 {
    opacity: 0.4 !important;
}

@keyframes shine-lines {
    0% {
        background-position: -25vw 0;
    }

    100% {
        background-position: 125vw 0;
    }
}
</style>
