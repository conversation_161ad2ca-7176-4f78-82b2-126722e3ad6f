<script setup lang="ts">
import { computed, withDefaults } from 'vue';
// Import BravoButton component instead of PrimeV<PERSON> Button directly
import BravoButton from './BravoButton.vue';
// Typography components from BravoTypography directory
import BravoLargeTitle from './BravoTypography/BravoLargeTitle.vue';
import BravoLargeBody from './BravoTypography/BravoLargeBody.vue';

// Default fallback image URL
const DEFAULT_IMAGE_URL = 'https://cdn.prod.website-files.com/5d4b12a4c03a64ab754f23bd/67f9df4569df927d4c0065e1_content-zero-state.png';

const props = withDefaults(defineProps<{
  title?: string;
  message?: string;
  buttonLabel?: string;
  buttonIcon?: string;
  imageSrc?: string;
  imageAlt?: string;
  actionHandler?: () => void;
  showButton?: boolean;
}>(), {
  title: 'Create your first item',
  message: 'You haven\'t created any items yet. Click the button below to get started.',
  buttonLabel: 'Create New',
  buttonIcon: 'pi pi-plus',
  imageAlt: 'Zero state',
  showButton: true
});

const emit = defineEmits<{
  action: [];
}>();

const handleAction = () => {
  emit('action');
  if (props.actionHandler) {
    props.actionHandler();
  }
};

// Use default values if props are not provided
const displayTitle = computed(() => props.title);
const displayMessage = computed(() => props.message);
const displayButtonLabel = computed(() => props.buttonLabel);
const displayButtonIcon = computed(() => props.buttonIcon);
const displayImageAlt = computed(() => props.imageAlt);
const shouldShowButton = computed(() => props.showButton);

// Handle image source with proper fallback to locally imported image
const displayImageSrc = computed(() => {
  if (props.imageSrc) {
    // If it starts with http or https, use it directly
    if (props.imageSrc.startsWith('http')) {
      return props.imageSrc;
    }
    // For paths like @/assets/..., use them as-is
    // The build system (webpack/vite) will handle resolving the path
    return props.imageSrc;
  }
  return DEFAULT_IMAGE_URL;
});
</script>

<template>
  <div class="zero-state-wrapper">
    <div class="zero-state-container">
      <div class="zero-state-content">
        <div class="zero-state-icon">
          <img
            :src="displayImageSrc"
            :alt="displayImageAlt"
          />
        </div>
        <BravoLargeTitle>{{ displayTitle }}</BravoLargeTitle>
        <BravoLargeBody class="zero-state-message">
          {{ displayMessage }}
        </BravoLargeBody>
        <div v-if="shouldShowButton">
          <BravoButton 
            class="bravo-button"
            :label="displayButtonLabel" 
            :icon="displayButtonIcon" 
            @click="handleAction" 
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.zero-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 2rem 0;
}

.zero-state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  background-color: var(--surface-0, #ffffff);
  max-width: 500px;
  width: 100%;
}

.zero-state-message {
  color: var(--text-color-secondary, var(--surface-600)) !important;
  margin: 1rem 0 2rem 0 !important;
  max-width: 400px !important;
}

.zero-state-icon {
  position: relative;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 