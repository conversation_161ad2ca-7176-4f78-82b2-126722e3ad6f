<script setup lang="ts">
import MultiSelect from 'primevue/multiselect';

defineProps({
    label: {
        type: String,
        default: '',
    },
    filterOptions: {
        type: Array,
        default: () => [],
    },
    optionLabel: {
        type: String,
        default: 'label',
    },
    optionValue: {
        type: String,
        default: 'value',
    },
    placeholder: {
        type: String,
        default: 'Select options',
    },
});

const emit = defineEmits(['update:modelValue', 'filter-change']);

interface MultiSelectChangeEvent {
    originalEvent: Event;
    value: unknown;
}

const handleChange = (event: MultiSelectChangeEvent) => {
    emit('update:modelValue', event.value);
    emit('filter-change', event.value);
};
</script>

<template>
  <div class="bravo-filter">
    <label
      v-if="label"
      class="bravo-filter-label"
    >{{ label }}</label>
    <MultiSelect
      class="bravo-filter-select"
      v-bind="$attrs"
      :options="filterOptions"
      :option-label="optionLabel"
      :option-value="optionValue"
      :placeholder="placeholder"
      display="chip"
      filter
      @change="handleChange"
    />
  </div>
</template>

<style lang="scss">
.bravo-filter {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: fit-content;

    &-label {
        font-weight: 500;
        font-size: 0.875rem;
        color: #333;
    }

    .p-multiselect {
        border: none !important;
        border-radius: 4px;
        transition: background-color 0.2s;
        background: transparent;
        box-shadow: none !important;
        width: fit-content;
        min-width: auto;
        display: grid !important;
        grid-template-columns: 1fr auto;
        align-items: center;

        &:hover {
            background-color: #f5f5f5;
        }

        &:focus,
        &.p-focus {
            box-shadow: none !important;
            background-color: #f5f5f5;
        }

        .p-multiselect-label-container {
            grid-column: 1;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            min-width: 0;
        }

        .p-multiselect-label {
            padding: 0.5rem 0.75rem;
            color: #333;
            white-space: nowrap;
            padding-right: 0rem;
        }

        .p-multiselect-trigger {
            grid-column: 2;
            width: 2rem;
            color: #666;
            padding: 0 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        // Keep chip formatting unchanged
        &.p-multiselect-chip {
            .p-multiselect-token {
                background-color: #f0f0f0;
                color: #333;
                border-radius: 4px;
                padding: 0.25rem 0.5rem;
                margin: 0.125rem;

                .p-multiselect-token-icon {
                    margin-left: 0.5rem;
                    color: #666;

                    &:hover {
                        color: #333;
                    }
                }
            }
        }
    }

    .p-multiselect-panel {
        border-radius: 4px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        min-width: 200px;

        .p-multiselect-items {
            padding: 0.25rem 0;

            .p-multiselect-item {
                padding: 0.5rem 0.75rem;
                color: #333;
                border-radius: 0;
                transition: background-color 0.2s;

                &:hover {
                    background-color: #f5f5f5;
                }

                &.p-highlight {
                    background-color: #e6f7ff;
                    color: #0366d6;
                }
            }
        }

        .p-multiselect-header {
            padding: 0.5rem;
            border-bottom: 1px solid #f0f0f0;

            .p-multiselect-filter-container {
                margin-bottom: 0.25rem;

                .p-inputtext {
                    padding: 0.5rem;
                    border: 1px solid #e0e0e0;
                    border-radius: 4px;
                }
            }
        }
    }
}
</style>
