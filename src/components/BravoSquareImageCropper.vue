<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
import BravoButton from './BravoButton.vue'
import BravoTitle3 from './BravoTypography/BravoTitle3.vue'
import BravoBody from './BravoTypography/BravoBody.vue'
import uploadIcon from '../assets/fileupload.svg'

interface CropArea {
  x: number
  y: number
  size: number
  initialized: boolean
}

interface ResizeHandle {
  position: string
  cursor: string
  style: Record<string, string>
}

interface CropError {
  message: string
  [key: string]: unknown
}

interface Props {
  waitForApiResponse?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  waitForApiResponse: false
})

const emit = defineEmits<{
  'cropped': [blob: Blob]
  'crop-error': [error: CropError]
  'submission-complete': []
}>()

// Refs
const fileInput = ref<HTMLInputElement>()
const imageContainer = ref<HTMLDivElement>()
const imageElement = ref<HTMLImageElement>()
const cropBox = ref<HTMLDivElement>()
const fileInputArea = ref<HTMLDivElement>()

// State
const imageData = ref<string>('')
const croppedImageUrl = ref<string>('')
const isPreviewProcessing = ref(false)
const isSubmitProcessing = ref(false)
const errorMessage = ref<string>('')
const isDragging = ref(false)
const isResizing = ref(false)
const resizeDirection = ref<string>('')
const dragStart = ref({ x: 0, y: 0 })
const isDragOver = ref(false)

const cropArea = ref<CropArea>({
  x: 0,
  y: 0,
  size: 100,
  initialized: false
})

// Computed styles
const overlayStyle = computed(() => {
  if (!imageElement.value) return {}
  
  const containerRect = imageContainer.value?.getBoundingClientRect()
  
  if (!containerRect) return {}
  
  return {
    top: '0px',
    left: '0px',
    width: `${imageElement.value.offsetWidth}px`,
    height: `${imageElement.value.offsetHeight}px`
  }
})

const cropBoxStyle = computed(() => {
  return {
    left: `${cropArea.value.x}px`,
    top: `${cropArea.value.y}px`,
    width: `${cropArea.value.size}px`,
    height: `${cropArea.value.size}px`
  }
})

const resizeHandles = computed(() => {
  const handles: ResizeHandle[] = [
    {
      position: 'nw',
      cursor: 'cursor-nw-resize',
      style: { top: '-6px', left: '-6px' }
    },
    {
      position: 'ne',
      cursor: 'cursor-ne-resize',
      style: { top: '-6px', right: '-6px' }
    },
    {
      position: 'sw',
      cursor: 'cursor-sw-resize',
      style: { bottom: '-6px', left: '-6px' }
    },
    {
      position: 'se',
      cursor: 'cursor-se-resize',
      style: { bottom: '-6px', right: '-6px' }
    }
  ]
  return handles
})

// File handling
const processFile = (file: File) => {
  if (!file.type.startsWith('image/')) {
    errorMessage.value = 'Please select a valid image file'
    return
  }
  
  errorMessage.value = ''
  const reader = new FileReader()
  
  reader.onload = (e) => {
    imageData.value = e.target?.result as string
    cropArea.value.initialized = false
  }
  
  reader.onerror = () => {
    errorMessage.value = 'Error reading file'
  }
  
  reader.readAsDataURL(file)
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (!file) return
  
  processFile(file)
}

// Initialize crop area
const initializeCropArea = async () => {
  await nextTick()
  
  if (!imageElement.value) return
  
  const size = Math.min(imageElement.value.offsetWidth, imageElement.value.offsetHeight) * 0.6
  
  cropArea.value = {
    x: (imageElement.value.offsetWidth - size) / 2,
    y: (imageElement.value.offsetHeight - size) / 2,
    size: size,
    initialized: true
  }
}

// Mouse event handlers
const handleContainerMouseDown = (event: MouseEvent) => {
  if (!cropBox.value?.contains(event.target as Node)) {
    event.preventDefault()
  }
}

const handleCropBoxMouseDown = (event: MouseEvent) => {
  if (isResizing.value) return
  
  event.preventDefault()
  event.stopPropagation()
  
  isDragging.value = true
  const rect = cropBox.value?.getBoundingClientRect()
  
  if (rect) {
    dragStart.value = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    }
  }
}

const handleResizeMouseDown = (event: MouseEvent, direction: string) => {
  event.preventDefault()
  event.stopPropagation()
  
  isResizing.value = true
  resizeDirection.value = direction
  
  const rect = cropBox.value?.getBoundingClientRect()
  if (rect) {
    dragStart.value = {
      x: event.clientX,
      y: event.clientY
    }
  }
}

const handleMouseMove = (event: MouseEvent) => {
  if (!imageElement.value) return
  
  if (isDragging.value) {
    const containerRect = imageContainer.value?.getBoundingClientRect()
    if (!containerRect) return
    
    const newX = event.clientX - containerRect.left - dragStart.value.x
    const newY = event.clientY - containerRect.top - dragStart.value.y
    
    // Constrain to image bounds
    const maxX = imageElement.value.offsetWidth - cropArea.value.size
    const maxY = imageElement.value.offsetHeight - cropArea.value.size
    
    cropArea.value.x = Math.max(0, Math.min(maxX, newX))
    cropArea.value.y = Math.max(0, Math.min(maxY, newY))
  }
  
  if (isResizing.value) {
    const deltaX = event.clientX - dragStart.value.x
    const deltaY = event.clientY - dragStart.value.y
    const delta = Math.max(deltaX, deltaY) // Use the larger delta to maintain square
    
    let newSize = cropArea.value.size
    let newX = cropArea.value.x
    let newY = cropArea.value.y
    
    switch (resizeDirection.value) {
      case 'nw':
        newSize = cropArea.value.size - delta
        newX = cropArea.value.x + delta
        newY = cropArea.value.y + delta
        break
      case 'ne':
        newSize = cropArea.value.size + delta
        newY = cropArea.value.y - delta
        break
      case 'sw':
        newSize = cropArea.value.size + delta
        newX = cropArea.value.x - delta
        break
      case 'se':
        newSize = cropArea.value.size + delta
        break
    }
    
    // Minimum size constraint
    newSize = Math.max(50, newSize)
    
    // Maximum size constraint
    if (imageElement.value) {
      const maxSize = Math.min(
        imageElement.value.offsetWidth - newX,
        imageElement.value.offsetHeight - newY
      )
      newSize = Math.min(newSize, maxSize)
    }
    
    // Update position constraints
    if (imageElement.value) {
      newX = Math.max(0, Math.min(newX, imageElement.value.offsetWidth - newSize))
      newY = Math.max(0, Math.min(newY, imageElement.value.offsetHeight - newSize))
    }
    
    cropArea.value.size = newSize
    cropArea.value.x = newX
    cropArea.value.y = newY
    
    dragStart.value = { x: event.clientX, y: event.clientY }
  }
}

const handleMouseUp = () => {
  isDragging.value = false
  isResizing.value = false
  resizeDirection.value = ''
}

// Cropping functionality
const cropImage = async () => {
  if (!imageElement.value || !cropArea.value.initialized) return
  
  isPreviewProcessing.value = true
  errorMessage.value = ''
  
  // Track start time for minimum loading duration
  const startTime = Date.now()
  
  try {
    // Create canvas for cropping
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      throw new Error('Could not get canvas context')
    }
    
    // Set canvas size to crop area
    canvas.width = cropArea.value.size
    canvas.height = cropArea.value.size
    
    // Reset any transforms and ensure proper orientation
    ctx.setTransform(1, 0, 0, 1, 0, 0)
    
    // Calculate scaling factor
    const scaleX = imageElement.value.naturalWidth / imageElement.value.offsetWidth
    const scaleY = imageElement.value.naturalHeight / imageElement.value.offsetHeight
    
    // Draw cropped image
    ctx.drawImage(
      imageElement.value,
      cropArea.value.x * scaleX,
      cropArea.value.y * scaleY,
      cropArea.value.size * scaleX,
      cropArea.value.size * scaleY,
      0,
      0,
      cropArea.value.size,
      cropArea.value.size
    )
    
    // Convert to blob - but only create preview, don't emit yet
    canvas.toBlob(async (blob) => {
      if (!blob) {
        const error: CropError = {
          message: 'Failed to create image blob'
        }
        emit('crop-error', error)
        isPreviewProcessing.value = false
        return
      }
      
      // Ensure minimum loading time for better UX
      const elapsedTime = Date.now() - startTime
      const minLoadingTime = 300 // 300ms minimum for preview
      
      if (elapsedTime < minLoadingTime) {
        await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsedTime))
      }
      
      // Create preview URL for validation (don't emit yet)
      croppedImageUrl.value = URL.createObjectURL(blob)
      
      isPreviewProcessing.value = false
    }, 'image/png', 1.0)
    
  } catch (error) {
    // Ensure minimum loading time even for errors
    const elapsedTime = Date.now() - startTime
    const minLoadingTime = 300
    
    if (elapsedTime < minLoadingTime) {
      await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsedTime))
    }
    
    const cropError: CropError = {
      message: error instanceof Error ? error.message : 'Unknown error'
    }
    emit('crop-error', cropError)
    errorMessage.value = `Cropping failed: ${cropError.message}`
    isPreviewProcessing.value = false
  }
}

const submitCroppedImage = async () => {
  if (!imageElement.value || !cropArea.value.initialized) return
  
  isSubmitProcessing.value = true
  errorMessage.value = ''
  
  // Track start time for minimum loading duration
  const startTime = Date.now()
  
  try {
    // Create canvas for cropping
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      throw new Error('Could not get canvas context')
    }
    
    // Set canvas size to crop area
    canvas.width = cropArea.value.size
    canvas.height = cropArea.value.size
    
    // Reset any transforms and ensure proper orientation
    ctx.setTransform(1, 0, 0, 1, 0, 0)
    
    // Calculate scaling factor
    const scaleX = imageElement.value.naturalWidth / imageElement.value.offsetWidth
    const scaleY = imageElement.value.naturalHeight / imageElement.value.offsetHeight
    
    // Draw cropped image
    ctx.drawImage(
      imageElement.value,
      cropArea.value.x * scaleX,
      cropArea.value.y * scaleY,
      cropArea.value.size * scaleX,
      cropArea.value.size * scaleY,
      0,
      0,
      cropArea.value.size,
      cropArea.value.size
    )
    
    // Convert to blob and emit it
    canvas.toBlob(async (blob) => {
      if (!blob) {
        const error: CropError = {
          message: 'Failed to create image blob'
        }
        emit('crop-error', error)
        isSubmitProcessing.value = false
        return
      }
      
      // Ensure minimum loading time for better UX (only if not waiting for API)
      if (!props.waitForApiResponse) {
        const elapsedTime = Date.now() - startTime
        const minLoadingTime = 500 // 500ms minimum for submit
        
        if (elapsedTime < minLoadingTime) {
          await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsedTime))
        }
      }
      
      // Emit the cropped blob for submission
      emit('cropped', blob)
      
      // Signal that submission is complete and component can be closed (only if not waiting for API)
      if (!props.waitForApiResponse) {
        emit('submission-complete')
        isSubmitProcessing.value = false
      }
    }, 'image/png', 1.0)
    
  } catch (error) {
    // Ensure minimum loading time even for errors
    const elapsedTime = Date.now() - startTime
    const minLoadingTime = 500
    
    if (elapsedTime < minLoadingTime) {
      await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsedTime))
    }
    
    const cropError: CropError = {
      message: error instanceof Error ? error.message : 'Unknown error'
    }
    emit('crop-error', cropError)
    errorMessage.value = `Cropping failed: ${cropError.message}`
    isSubmitProcessing.value = false
  }
}

const clearImage = () => {
  imageData.value = ''
  cropArea.value.initialized = false
  errorMessage.value = ''
  clearCroppedImage()
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const clearCroppedImage = () => {
  if (croppedImageUrl.value) {
    URL.revokeObjectURL(croppedImageUrl.value)
    croppedImageUrl.value = ''
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  
  // Clean up blob URL to prevent memory leaks
  clearCroppedImage()
})

// Drag and drop event handlers
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
  const dataTransfer = event.dataTransfer
  const files = dataTransfer?.files
  
  if (files && files.length > 0) {
    processFile(files[0])
  }
}

const triggerFileInput = () => {
  if (fileInput.value) {
    fileInput.value.click()
  }
}

// Method for parent to call when API response is received
const completeSubmission = () => {
  if (isSubmitProcessing.value) {
    isSubmitProcessing.value = false
    emit('submission-complete')
  }
}

// Expose method for parent component
defineExpose({
  completeSubmission
})
</script>

<template>
  <div class="square-image-cropper">
    <!-- File Input -->
    <div 
      ref="fileInputArea"
      class="mb-4 file-drop-area"
      :class="{ 'drag-over': isDragOver }"
      @dragover.prevent="handleDragOver"
      @dragenter.prevent="handleDragEnter"
      @dragleave="handleDragLeave"
      @drop.prevent="handleDrop"
    >
      <div class="text-center mb-2">
        <!-- Upload Icon -->
        <div>
          <img 
            :src="uploadIcon" 
            alt="Upload files" 
            class="upload-icon mx-auto"
          />
        </div>
        <BravoBody class="text-secondary">
          Drop an image here or
        </BravoBody>
      </div>
      <div class="text-center">
        <BravoButton
          severity="primary"
          label="Choose File"
          @click="triggerFileInput"
        />
      </div>
      <input
        ref="fileInput"
        type="file"
        accept="image/*"
        class="hidden"
        @change="handleFileSelect"
      />
    </div>

    <!-- Image Cropping Area -->
    <div
      v-if="imageData"
      class="relative mb-4 flex justify-center"
    >
      <div
        ref="imageContainer"
        class="relative border border-gray-300 rounded-lg overflow-hidden"
        @mousedown="handleContainerMouseDown"
      >
        <!-- Original Image -->
        <img
          ref="imageElement"
          :src="imageData"
          alt="Image to crop"
          class="block max-w-full max-h-96"
          @load="initializeCropArea"
        />
        
        <!-- Crop Overlay -->
        <div
          v-if="cropArea.initialized"
          class="absolute pointer-events-none"
          :style="overlayStyle"
        >
          <!-- Dimmed areas -->
          <div class="absolute inset-0 bg-black bg-opacity-40"></div>
          
          <!-- Clear crop area -->
          <div
            ref="cropBox"
            class="absolute bg-transparent border-2 border-white shadow-lg pointer-events-auto cursor-move"
            :style="cropBoxStyle"
            @mousedown="handleCropBoxMouseDown"
          >
            <!-- Resize handles -->
            <div
              v-for="handle in resizeHandles"
              :key="handle.position"
              class="absolute w-3 h-3 bg-white border border-gray-400 cursor-pointer"
              :class="handle.cursor"
              :style="handle.style"
              @mousedown="handleResizeMouseDown($event, handle.position)"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Crop Button -->
    <div
      v-if="imageData"
      class="flex justify-end gap-2"
    >
      <BravoButton
        v-if="imageData"
        severity="secondary"
        label="Clear"
        @click="clearImage"
      />
      
      <BravoButton
        v-if="cropArea.initialized"
        :disabled="isPreviewProcessing"
        severity="primary"
        :label="isPreviewProcessing ? 'Processing...' : 'Preview'"
        @click="cropImage"
      />
    </div>

    <!-- Cropped Image Preview -->
    <div
      v-if="croppedImageUrl"
      class="mt-4"
    >
      <BravoTitle3>Cropped Image Preview:</BravoTitle3>
      <BravoBody class="text-secondary preview-description">
        This preview shows how your image will look once saved.
      </BravoBody>
      <div class="border border-gray-300 rounded-lg p-4 bg-gray-50 preview-container">
        <img 
          :src="croppedImageUrl" 
          alt="Cropped image preview"
          class="max-w-xs max-h-64 mx-auto block border border-gray-200 rounded shadow-sm"
        />
        <div class="mt-3 text-center flex gap-2 justify-center">
          <BravoButton
            severity="secondary"
            label="Clear Preview"
            @click="clearCroppedImage"
          />
          <BravoButton
            :disabled="isSubmitProcessing"
            severity="primary"
            :label="isSubmitProcessing ? 'Submitting...' : 'Submit'"
            @click="submitCroppedImage"
          />
        </div>
      </div>
    </div>

    <!-- Error Display -->
    <div
      v-if="errorMessage"
      class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded"
    >
      {{ errorMessage }}
    </div>
  </div>
</template>

<style scoped>
.cursor-nw-resize {
  cursor: nw-resize;
}

.cursor-ne-resize {
  cursor: ne-resize;
}

.cursor-sw-resize {
  cursor: sw-resize;
}

.cursor-se-resize {
  cursor: se-resize;
}

.square-image-cropper {
  max-width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.text-secondary {
  color: var(--text-color-secondary);
}

.preview-description {
  margin-bottom: 1rem;
}

.preview-container {
  padding-top: 1rem;
}

.upload-icon {
  width: 80px;
  height: 80px;
  transition: all 0.3s ease;
}

.file-drop-area:hover .upload-icon {
  opacity: 1;
  transform: scale(1.05);
}

.file-drop-area {
  border: 2px dashed var(--text-color-secondary);
  border-radius: 0.5rem;
  padding: 1rem;
  transition: all 0.3s ease;
  background-color: transparent;
  cursor: pointer;
}

.file-drop-area:hover {
  border-color: var(--primary-color) !important;
  background-color: #f0f0f0 !important;
}

.drag-over {
  border-color: var(--primary-color);
  background-color: var(--primary-color-text);
}
</style> 