<script setup lang="ts">
import type { Component } from 'vue';

export interface Field {
    label: string;
    component: Component;
    props?: Record<string, unknown>;
}

interface Props {
    fields: Field[];
}

defineProps<Props>();
</script>

<template>
  <div class="bravo-field-panel">
    <div
      v-for="(field, index) in fields"
      :key="index"
      class="field-row"
    >
      <div class="field-label">
        {{ field.label }}
      </div>
      <div class="field-input">
        <component
          :is="field.component"
          v-bind="field.props"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.bravo-field-panel {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
}

.field-row {
    display: grid;
    grid-template-columns: 200px 1fr;
    align-items: center;
    gap: 1rem;
}

.field-label {
    color: var(--text-color-secondary);
    font-weight: 500;
}

.field-input {
    flex: 1;
    min-width: 0;
}
</style>
