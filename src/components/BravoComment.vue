<script setup lang="ts">
import { BravoButton } from './index';

defineProps({
  commenterName: {
    type: String,
    required: true
  },
  commenterAvatar: {
    type: String,
    default: ''
  },
  commentDate: {
    type: Date,
    required: true
  },
  articleTitle: {
    type: String,
    required: false,
    default: ''
  },
  articleUrl: {
    type: String,
    required: false,
    default: ''
  },
  commentBody: {
    type: String,
    required: true
  },
  showArticleURL: {
    type: Boolean,
    default: true
  },
  showResolveButton: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['resolve']);

const handleResolve = () => {
  emit('resolve');
};
</script>

<template>
  <div class="bravo-comment">
    <div class="comment-header">
      <div
        v-if="commenterAvatar"
        class="avatar-container"
      >
        <img
          :src="commenterAvatar"
          alt="Commenter Avatar"
          class="commenter-avatar"
        />
      </div>
      <div class="comment-header-info">
        <div class="commenter-name">
          {{ commenterName }}
        </div>
        <div class="comment-date">
          {{ new Date(commentDate).toLocaleDateString() }}
        </div>
      </div>
    </div>
    <div class="comment-content">
      <div
        v-if="showArticleURL && articleTitle"
        class="article-info"
      >
        <a
          :href="articleUrl"
          class="article-link"
          target="_blank"
        >{{ articleTitle }}</a>
      </div>
      <div class="comment-body">
        {{ commentBody }}
      </div>
      <div
        v-if="showResolveButton"
        class="resolve-action"
      >
        <BravoButton 
          label="Resolve" 
          severity="secondary"
          size="small"
          @click="handleResolve" 
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.bravo-comment {
  width: 100%;
  padding: 1rem;
  border-radius: 8px;
  background-color: var(--surface-50);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  .comment-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    
    .avatar-container {
      margin-right: 0.75rem;
      
      .commenter-avatar {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 8px;
        object-fit: cover;
      }
    }
    
    .comment-header-info {
      .commenter-name {
        font-weight: 600;
        color: var(--text-color-primary);
      }
      
      .comment-date {
        font-size: 0.9rem;
        color: var(--text-color-secondary);
      }
    }
  }
  
  .comment-content {
    .article-info {
      font-size: 0.9rem;
      margin-bottom: 0.75rem;
      
      .article-link {
        color: var(--primary-650);
        text-decoration: none;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
    
    .comment-body {
      line-height: 1.4;
      margin-bottom: 0.75rem;
      color: var(--text-color-primary);
      word-wrap: break-word;
    }
    
    .resolve-action {
      margin-top: 1rem;
      text-align: right;
    }
  }
}
</style> 