<script setup lang="ts">
import OrderList from 'primevue/orderlist';

interface Props {
    modelValue?: any[];
    dataKey?: string;
    dragdrop?: boolean;
    listStyle?: object;
    selection?: any[];
    metaKeySelection?: boolean;
    breakpoint?: string;
    pt?: object;
}

const props = withDefaults(defineProps<Props>(), {
    dataKey: 'id',
    dragdrop: false,
    metaKeySelection: true,
    breakpoint: '960px',
});

const emit = defineEmits(['update:modelValue', 'update:selection']);
</script>

<template>
  <OrderList
    class="bravo-orderlist"
    :model-value="props.modelValue"
    :selection="props.selection"
    :data-key="props.dataKey"
    :dragdrop="props.dragdrop"
    :list-style="props.listStyle"
    :meta-key-selection="props.metaKeySelection"
    :breakpoint="props.breakpoint"
    :pt="props.pt"
    v-bind="$attrs"
    @update:model-value="$emit('update:modelValue', $event)"
    @update:selection="$emit('update:selection', $event)"
  >
    <slot></slot>
    <template
      v-if="$slots.item"
      #item="slotProps"
    >
      <slot
        name="item"
        v-bind="slotProps"
      ></slot>
    </template>
    <template
      v-if="$slots.option"
      #option="slotProps"
    >
      <slot
        name="option"
        v-bind="slotProps"
      ></slot>
    </template>
  </OrderList>
</template>

<style lang="scss" scoped></style>
