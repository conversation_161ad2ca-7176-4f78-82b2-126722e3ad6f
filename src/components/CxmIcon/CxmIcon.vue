<template>
  <component :is="name + '-icon'" />
</template>

<script lang="ts">
import ArrowCaretDownLargeIcon from './icons/ArrowCaretDownLargeIcon.vue';
import ArrowCaretDownSmallIcon from './icons/ArrowCaretDownSmallIcon.vue';
import ArrowCaretLeftLargeIcon from './icons/ArrowCaretLeftLargeIcon.vue';
import ArrowCaretLeftSmallIcon from './icons/ArrowCaretLeftSmallIcon.vue';
import ArrowCaretRightLargeIcon from './icons/ArrowCaretRightLargeIcon.vue';
import ArrowCaretRightSmallIcon from './icons/ArrowCaretRightSmallIcon.vue';
import ArrowCaretUpLargeIcon from './icons/ArrowCaretUpLargeIcon.vue';
import ArrowCaretUpSmallIcon from './icons/ArrowCaretUpSmallIcon.vue';
import ArrowDropDownIcon from './icons/ArrowDropDownIcon.vue';
import ArrowDropLeftIcon from './icons/ArrowDropLeftIcon.vue';
import ArrowDropRightIcon from './icons/ArrowDropRightIcon.vue';
import ArrowDropUpIcon from './icons/ArrowDropUpIcon.vue';
import ArrowPointerDownLargeIcon from './icons/ArrowPointerDownLargeIcon.vue';
import ArrowPointerDownSmallIcon from './icons/ArrowPointerDownSmallIcon.vue';
import ArrowPointerLeftLargeIcon from './icons/ArrowPointerLeftLargeIcon.vue';
import ArrowPointerLeftSmallIcon from './icons/ArrowPointerLeftSmallIcon.vue';
import ArrowPointerRightLargeIcon from './icons/ArrowPointerRightLargeIcon.vue';
import ArrowPointerRightSmallIcon from './icons/ArrowPointerRightSmallIcon.vue';
import ArrowPointerUpLargeIcon from './icons/ArrowPointerUpLargeIcon.vue';
import ArrowPointerUpSmallIcon from './icons/ArrowPointerUpSmallIcon.vue';
import CalendarIcon from './icons/CalendarIcon.vue';
import CollapseIcon from './icons/CollapseIcon.vue';
import DeleteIcon from './icons/DeleteIcon.vue';
import EditIcon from './icons/EditIcon.vue';
import DragIcon from './icons/DragIcon.vue';
import EllipsesIcon from './icons/EllipsesIcon.vue';
import ExpandIcon from './icons/ExpandIcon.vue';
import FileIcon from './icons/FileIcon.vue';
import FilterIcon from './icons/FilterIcon.vue';
import InfoIcon from './icons/InfoIcon.vue';
import JourneyCompleteIcon from './icons/JourneyCompleteIcon.vue';
import JourneyTodoIcon from './icons/JourneyTodoIcon.vue';
import JourneyCanceledIcon from './icons/JourneyCanceledIcon.vue';
import JourneyPartialCompleteIcon from './icons/JourneyPartialCompleteIcon.vue';
import JourneyPartialIncompleteIcon from './icons/JourneyPartialIncompleteIcon.vue';
import JourneySkippedIcon from './icons/JourneySkippedIcon.vue';
import JourneyErrorIcon from './icons/JourneyErrorIcon.vue';
import LightningIcon from './icons/LightningIcon.vue';
import MarkerIcon from './icons/MarkerIcon.vue';
import OpenNewIcon from './icons/OpenNewIcon.vue';
import PathIcon from './icons/PathIcon.vue';
import ParallelIcon from './icons/ParallelIcon.vue';
import PhoneIcon from './icons/PhoneIcon.vue';
import PlusIcon from './icons/PlusIcon.vue';
import QuestionLargeIcon from './icons/QuestionLargeIcon.vue';
import QuestionSmallIcon from './icons/QuestionSmallIcon.vue';
import ResizeIcon from './icons/ResizeIcon.vue';
import SearchIcon from './icons/SearchIcon.vue';
import StepIcon from './icons/StepIcon.vue';
import StageIcon from './icons/StageIcon.vue';
import TaskIcon from './icons/TaskIcon.vue';
import TaskListIcon from './icons/TaskListIcon.vue';
import TimesXIcon from './icons/TimesXIcon.vue';
import WarningCircleIcon from './icons/WarningCircleIcon.vue';
import WarningTriangleIcon from './icons/WarningTriangleIcon.vue';
import NumberBubbleIcon from './icons/NumberBubbleIcon.vue';
import StepCompletedIcon from './icons/StepCompletedIcon.vue';
import ReloadIcon from './icons/ReloadIcon.vue';

export default {
    name: 'CxmIcon',
    components: {
        ArrowCaretDownLargeIcon,
        ArrowCaretDownSmallIcon,
        ArrowCaretLeftLargeIcon,
        ArrowCaretLeftSmallIcon,
        ArrowCaretRightLargeIcon,
        ArrowCaretRightSmallIcon,
        ArrowCaretUpLargeIcon,
        ArrowCaretUpSmallIcon,
        ArrowDropDownIcon,
        ArrowDropLeftIcon,
        ArrowDropRightIcon,
        ArrowDropUpIcon,
        ArrowPointerDownLargeIcon,
        ArrowPointerDownSmallIcon,
        ArrowPointerLeftLargeIcon,
        ArrowPointerLeftSmallIcon,
        ArrowPointerRightLargeIcon,
        ArrowPointerRightSmallIcon,
        ArrowPointerUpLargeIcon,
        ArrowPointerUpSmallIcon,
        CalendarIcon,
        CollapseIcon,
        DeleteIcon,
        DragIcon,
        EditIcon,
        EllipsesIcon,
        ExpandIcon,
        FileIcon,
        FilterIcon,
        JourneyCompleteIcon,
        JourneyPartialIncompleteIcon,
        JourneyPartialCompleteIcon,
        JourneySkippedIcon,
        JourneyErrorIcon,
        JourneyTodoIcon,
        JourneyCanceledIcon,
        LightningIcon,
        MarkerIcon,
        OpenNewIcon,
        PathIcon,
        ParallelIcon,
        PhoneIcon,
        PlusIcon,
        QuestionLargeIcon,
        QuestionSmallIcon,
        ResizeIcon,
        SearchIcon,
        StepIcon,
        StageIcon,
        TaskIcon,
        TaskListIcon,
        TimesXIcon,
        InfoIcon,
        WarningCircleIcon,
        WarningTriangleIcon,
        NumberBubbleIcon,
        StepCompletedIcon,
        ReloadIcon,
    },
    props: {
        name: {
            type: String,
        },
    },
};
</script>
