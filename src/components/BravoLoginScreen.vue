<script setup lang="ts">
import { ref } from 'vue';
import BravoButton from './BravoButton.vue';
import BravoInputText from './BravoInputText.vue';
import BravoPassword from './BravoPassword.vue';
import BravoCheckbox from './BravoCheckbox.vue';

const email = ref('');
const password = ref('');
const rememberMe = ref(false);
const loading = ref(false);
const error = ref('');

const props = defineProps<{
    submitHandler?: (credentials: { email: string; password: string; rememberMe: boolean }) => Promise<void>;
}>();

const emit = defineEmits<{
    submit: [{ email: string; password: string; rememberMe: boolean }];
    forgotPassword: [];
}>();

const handleSignIn = async () => {
    loading.value = true;
    error.value = '';
    const trimmedEmail = email.value.trim();

    try {
        emit('submit', {
            email: trimmedEmail,
            password: password.value,
            rememberMe: rememberMe.value,
        });

        if (props.submitHandler) {
            await props.submitHandler({
                email: trimmedEmail,
                password: password.value,
                rememberMe: rememberMe.value,
            });
        }
    } catch (err) {
        error.value = err instanceof Error ? err.message : 'Login failed. Please try again.';
    } finally {
        loading.value = false;
    }
};
</script>

<template>
  <div class="login-wrapper">
    <div class="login-container">
      <h1>Sign in to your account</h1>
      <div
        v-if="error"
        class="error-message"
      >
        {{ error }}
      </div>

      <form @submit.prevent="handleSignIn">
        <div class="form-group">
          <label>Email</label>
          <BravoInputText
            v-model="email"
            type="email"
            :disabled="loading"
            tabindex="1"
          />
        </div>

        <div class="form-group">
          <label>Password</label>
          <BravoPassword
            v-model="password"
            toggle-mask
            :feedback="false"
            :disabled="loading"
            tabindex="2"
          />
        </div>

        <div class="form-group remember-me">
          <BravoCheckbox
            v-model="rememberMe"
            :binary="true"
            :disabled="loading"
            input-id="remember-me-checkbox"
            tabindex="4"
          />
          <label
            class="remember-me-label"
            for="remember-me-checkbox"
          >Remember me on this device</label>
        </div>

        <BravoButton
          type="submit"
          :label="loading ? 'Signing in...' : 'Sign in'"
          class="sign-in-button"
          tabindex="3"
          :loading="loading"
          :disabled="loading"
        />
      </form>

      <div class="links-container">
        <a
          href="#"
          class="secondary-link"
          tabindex="5"
        >Use single sign-on (SSO)</a>
        <BravoButton
          type="button"
          label="Forgot your password?"
          class="secondary-link"
          variant="link"
          tabindex="6"
          @click="emit('forgotPassword')"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    width: 440px;
    padding: 4rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
}

.login-container h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--surface-850);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 14px;
    color: var(--surface-850);
}

.sign-in-button {
    width: 100%;
    background: var(--primary-600);
    padding: 0.75rem;
    font-weight: 500;
    margin: 1rem 0 1rem 0;
}

.links-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.secondary-link {
    font-size: 13px;
    color: var(--primary-600);
    text-decoration: none;
}

.secondary-link :deep(.p-button-label) {
  font-weight: 400;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.remember-me label {
    margin-bottom: 1px;
}

.remember-me-label {
    margin: 0;
    padding: 0;
    cursor: pointer;
    font-size: 14px;
    color: var(--surface-850);
    display: inline-flex;
    align-items: center;
    line-height: 1;
}

:deep(.p-checkbox),
:deep(.p-checkbox-box) {
    display: flex;
    align-items: center;
}

:deep(.p-password input),
:deep(.p-inputtext) {
    width: 100%;
    height: 40px !important;
}

:deep(.p-password) {
    width: 100%;
    display: block;
}

.error-message {
    background-color: var(--red-100);
    color: var(--red-700);
    padding: 0.75rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    font-size: 14px;
}
</style>
