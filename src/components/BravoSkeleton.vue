<script setup lang="ts">
import Skeleton from 'primevue/skeleton';

interface Props {
    shape?: 'rectangle' | 'circle';
    size?: string;
    width?: string;
    height?: string;
    borderRadius?: string;
    animation?: 'wave' | 'none';
}

defineProps<Props>();
</script>

<template>
  <Skeleton
    class="bravo-skeleton"
    :shape="shape"
    :size="size"
    :width="width"
    :height="height"
    :border-radius="borderRadius"
    :animation="animation"
    v-bind="$attrs"
  />
</template>

<style lang="scss" scoped></style>
