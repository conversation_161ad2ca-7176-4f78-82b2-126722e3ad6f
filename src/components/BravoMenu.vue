<script setup lang="ts">
import Menu from 'primevue/menu';
import { ref } from 'vue';

const menuRef = ref();

// Expose the toggle method to parent components
defineExpose({
    toggle: (event: Event) => menuRef.value?.toggle(event),
});
</script>

<template>
  <Menu
    ref="menuRef"
    class="bravo-menu"
    v-bind="$attrs"
  >
    <template
      v-for="(_, name) in $slots"
      #[name]="slotData"
    >
      <slot
        :name="name"
        v-bind="slotData || {}"
      ></slot>
    </template>
  </Menu>
</template>

<style lang="scss" scoped></style>
