<script setup lang="ts">
import ContextMenu from 'primevue/contextmenu';
import { ref } from 'vue';

const menuRef = ref();

// Expose the toggle method to parent components
defineExpose({
    toggle: (event: Event) => menuRef.value?.toggle(event),
    show: (event: Event) => menuRef.value?.show(event),
    hide: () => menuRef.value?.hide(),
});
</script>

<template>
  <ContextMenu
    ref="menuRef"
    class="bravo-context-menu"
    v-bind="$attrs"
  >
    <slot></slot>
  </ContextMenu>
</template>

<style lang="scss" scoped></style>
