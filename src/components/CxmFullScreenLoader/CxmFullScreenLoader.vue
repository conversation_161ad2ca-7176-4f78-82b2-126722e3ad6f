<script lang="ts" setup>
type BaseFullScreenLoaderProps = {
    isLoading: boolean;
};
const props = withDefaults(defineProps<BaseFullScreenLoaderProps>(), {});
</script>

<template>
  <div
    v-if="props.isLoading"
    class="cxm-full-screen-loader"
  >
    <span class="fullScreen-loader"></span>
  </div>
</template>

<style scoped>
.cxm-full-screen-loader {
    display: flex;
}

.fullScreen-loader {
    animation: rotate 1.5s infinite;
    height: 50px;
    width: 50px;
}

.fullScreen-loader:before,
.fullScreen-loader:after {
    border-radius: 50%;
    content: '';
    display: block;
    height: 20px;
    width: 20px;
}

.fullScreen-loader:before {
    animation: ball1 1.5s infinite;
    background-color: #bcbfc2;
    box-shadow: 30px 0 0 #006acc;
    margin-bottom: 10px;
}

.fullScreen-loader:after {
    animation: ball2 1.5s infinite;
    background-color: #006acc;
    box-shadow: 30px 0 0 #fff;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg) scale(0.8);
    }

    50% {
        transform: rotate(360deg) scale(1.2);
    }

    100% {
        transform: rotate(720deg) scale(0.8);
    }
}

@keyframes ball1 {
    0% {
        box-shadow: 30px 0 0 #006acc;
    }

    50% {
        box-shadow: 0 0 0 #006acc;
        margin-bottom: 0;
        transform: translate(15px, 15px);
    }

    100% {
        box-shadow: 30px 0 0 #006acc;
        margin-bottom: 10px;
    }
}

@keyframes ball2 {
    0% {
        box-shadow: 30px 0 0 #bcbfc2;
    }

    50% {
        box-shadow: 0 0 0 #bcbfc2;
        margin-top: -20px;
        transform: translate(15px, 15px);
    }

    100% {
        box-shadow: 30px 0 0 #bcbfc2;
        margin-top: 0;
    }
}
</style>
