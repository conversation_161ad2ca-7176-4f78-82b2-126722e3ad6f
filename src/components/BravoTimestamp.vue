<script lang="ts" setup>
import { computed } from 'vue';
import { formatRelativeTimestamp } from '../utils';
import BravoCaption2 from './BravoTypography/BravoCaption2.vue';

type TimestampProps = {
    datetime: string | number;
};

const props = withDefaults(defineProps<TimestampProps>(), {});

const formattedTimestamp = computed(() => {
    return formatRelativeTimestamp(props.datetime);
});
</script>
<template>
  <BravoCaption2>{{ formattedTimestamp }}</BravoCaption2>
</template>

<style lang="scss" scoped></style>
