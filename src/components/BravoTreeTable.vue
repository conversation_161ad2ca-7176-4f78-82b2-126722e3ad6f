<script setup lang="ts">
import PrimeTreeTable from 'primevue/treetable';
import type { TreeNode } from 'primevue/treenode';
import { computed } from 'vue';

// Define props with default values
const props = defineProps({
    value: {
        type: Array as () => TreeNode[],
        default: () => [],
    },
});

// Ensure value is always an array
const tableValue = computed(() => {
    // Make sure we're returning a valid array even if value is null or undefined
    return Array.isArray(props.value) ? props.value : [];
});
</script>

<template>
  <div class="bravo-treetable-wrapper">
    <PrimeTreeTable
      class="bravo-treetable"
      :value="tableValue"
      v-bind="$attrs"
    >
      <slot></slot>
    </PrimeTreeTable>
  </div>
</template>

<style lang="scss">
.bravo-treetable-wrapper {
    .bravo-treetable {
        /* Apply custom styling using PrimeVue CSS variables */
        --p-treetable-background: var(--p-treetable-background, var(--surface-0));
        --p-treetable-border-color: var(--p-treetable-border-color, var(--surface-200));
        --p-treetable-border-radius: var(--p-treetable-border-radius, var(--border-radius-md));
        --p-treetable-header-padding: var(--p-treetable-header-padding, 1rem);
        --p-treetable-header-background: var(--p-treetable-header-background, var(--surface-0));
        --p-treetable-header-border-color: var(--p-treetable-header-border-color, var(--surface-200));
        --p-treetable-header-color: var(--p-treetable-header-color, var(--surface-900));
        --p-treetable-cell-padding: var(--p-treetable-cell-padding, 1rem);
        --p-treetable-cell-border-color: var(--p-treetable-cell-border-color, var(--surface-200));
        --p-treetable-row-hover-background: var(--p-treetable-row-hover-background, var(--surface-100));
        --p-treetable-row-stripe-background: var(--p-treetable-row-stripe-background, var(--surface-50));

        /* Tree node specific styling */
        --p-tree-node-hover-background: var(--p-tree-node-hover-background, var(--surface-100));
        --p-tree-node-selected-background: var(--p-tree-node-selected-background, var(--primary-50));
        --p-tree-node-selected-color: var(--p-tree-node-selected-color, var(--primary-600));
        --p-tree-toggler-width: var(--p-tree-toggler-width, 2rem);
        --p-tree-toggler-height: var(--p-tree-toggler-height, 2rem);
        --p-tree-toggler-border-radius: var(--p-tree-toggler-border-radius, 50%);
        --p-tree-toggler-hover-background: var(--p-tree-toggler-hover-background, var(--surface-100));
    }
}
</style>
