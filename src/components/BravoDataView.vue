<script setup lang="ts">
import DataView from 'primevue/dataview';

defineProps<{
    layout?: 'list' | 'grid';
    value?: any[];
    paginator?: boolean;
    rows?: number;
    sortField?: string;
    sortOrder?: number;
    dataKey: string;
    paginatorTemplate?: string;
    paginatorPosition?: 'top' | 'bottom' | 'both';
    emptyMessage?: string;
}>();
</script>

<template>
  <DataView
    class="bravo-dataview"
    v-bind="$attrs"
    :value="value"
    :data-key="dataKey"
    :layout="layout"
    :paginator="paginator"
    :rows="rows"
    :sort-field="sortField"
    :sort-order="sortOrder"
    :paginator-template="
      paginatorTemplate || 'FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown'
    "
    :paginator-position="paginatorPosition || 'bottom'"
    :empty-message="emptyMessage || 'No records found'"
  >
    <template
      v-if="$slots.header"
      #header
    >
      <slot name="header"></slot>
    </template>
    <template
      v-if="$slots.list"
      #list="slotProps"
    >
      <slot
        name="list"
        v-bind="slotProps"
      ></slot>
    </template>
    <template
      v-if="$slots.grid"
      #grid="slotProps"
    >
      <slot
        name="grid"
        v-bind="slotProps"
      ></slot>
    </template>
    <template
      v-if="$slots.empty"
      #empty
    >
      <slot name="empty"></slot>
    </template>
    <template
      v-if="$slots.paginatorstart"
      #paginatorstart
    >
      <slot name="paginatorstart"></slot>
    </template>
    <template
      v-if="$slots.paginatorend"
      #paginatorend
    >
      <slot name="paginatorend"></slot>
    </template>
  </DataView>
</template>

<style lang="scss" scoped>
.bravo-dataview {
    :deep(.p-dataview-header) {
        padding: 1.25rem;
    }

    :deep(.p-dataview-content) {
        padding: 0;
    }

    :deep(.p-paginator) {
        padding: 1rem;
    }
}
</style>
