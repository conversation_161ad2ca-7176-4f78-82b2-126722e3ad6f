<script setup lang="ts">
import InputText from 'primevue/inputtext';

defineProps({
  displayMode: {
    type: String,
    default: 'edit',
    validator: (value: string) => ['edit', 'view'].includes(value)
  }
});
</script>

<template>
  <template v-if="displayMode === 'edit'">
    <InputText
      class="bravo-inputtext"
      v-bind="$attrs"
    />
  </template>
  
  <template v-else>
    <div class="bravo-inputtext-view">
      {{ $attrs.modelValue }}
    </div>
  </template>
</template>

<style lang="scss" scoped>
.bravo-inputtext-view {
  padding: 0.5rem 0.75rem 0.5rem .821rem;
  background-color: transparent;
}
</style>
