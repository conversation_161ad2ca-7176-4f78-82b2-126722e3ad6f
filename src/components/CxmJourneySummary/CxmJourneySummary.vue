<script lang="ts" setup>
import CxmIcon from '../CxmIcon/CxmIcon.vue';
import { computed } from 'vue';

type Action = {
    internalName: string;
    status: string;
    type: string;
    icon?: string;
};
type JourneyWorkflow = {
    name: string;
    internalName?: string;
    description: string;
    actions: Action[];
    status: string;
};

type JourneySummaryProps = {
    workflow: JourneyWorkflow;
    dataTestId?: string;
    truncateDescription?: boolean;
};

const props = withDefaults(defineProps<JourneySummaryProps>(), {
    dataTestId: '',
    truncateDescription: false,
});

const statusIcons: { [key: string]: string } = {
    NotStarted: 'journey-todo',
    Started: 'journey-partial-complete',
    Skipped: 'journey-skipped',
    Completed: 'journey-complete',
    InProgress: 'journey-partial-complete',
    Initialized: 'journey-partial-complete',
    Cancelled: 'journey-canceled',
    PartiallyComplete: 'journey-partial-complete',
};

const statusDisplay = computed(() => {
    if (props.workflow.status === 'Cancelled') {
        return 'Closed';
    } else if (props.workflow.status === 'Completed') {
        return 'Completed';
    } else {
        const totalStages = stages.value.length;
        const completedStages = stages.value.filter(
            (stage: Action) => stage.status === 'Completed' || stage.status === 'Cancelled'
        ).length;
        return `${completedStages} of ${totalStages}`;
    }
});

const stages = computed(() => {
    const stages = props.workflow.actions.filter((action: Action) => {
        return action.type === 'com.ovationcxm.journeys.stage';
    });
    return stages.map((stage: Action) => {
        return {
            internalName: stage.internalName,
            status: stage.status,
            type: stage.type,
            icon: statusIcons[stage.status] || '',
        };
    });
});
</script>
<template>
  <div
    class="journey-summary"
    :class="{ 'hide-overflow': props.truncateDescription }"
  >
    <div class="journey-title">
      {{ props.workflow.internalName ?? props.workflow.name }}
    </div>
    <div
      v-if="props.workflow.description"
      class="journey-description"
    >
      {{ props.workflow.description }}
    </div>
    <div class="journey-stages">
      <div
        class="journey-status"
        :class="{ warning: statusDisplay === 'Closed' }"
      >
        {{ statusDisplay }}
      </div>
      <div
        v-for="(stage, index) of stages"
        :key="index"
        class="icon-wrapper"
        :title="stage.internalName"
      >
        <CxmIcon
          class="stage-icon"
          :name="stage.icon"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.journey-title {
    color: var(--gray-gray-1600);
    font-size: 15px;
    font-weight: 500;
    line-height: normal;
    .hide-overflow & {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}
.journey-description {
    color: var(--gray-gray-1100);
    font-size: 12px;
    font-weight: 400;
    line-height: normal;
    margin-top: 6px;
    .hide-overflow & {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}

.journey-stages {
    margin-top: 6px;
}

.journey-status {
    display: inline-block;
    vertical-align: top;
    margin-right: 4px;
    line-height: 18px;
    font-size: 13px;
    color: var(--gray-1100);

    &.warning {
        color: var(--orange-800);
        font-weight: 700;
    }
}

.icon-wrapper {
    display: inline-block;
    margin-right: 6px;
}
.stage-icon {
    height: 16px;
    width: 16px;
}
</style>
