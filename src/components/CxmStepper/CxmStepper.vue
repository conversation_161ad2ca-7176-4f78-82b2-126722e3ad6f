<!-- eslint-disable @typescript-eslint/no-explicit-any -->
<script setup lang="ts">
type StepperProps = {
    activeStep: number;
    steps: Array<any>;
    preRequired: boolean;
    dataTestId?: string;
    id: string;
};

const props = withDefaults(defineProps<StepperProps>(), {});

import { CxmIcon } from '../CxmIcon';

const getStatus = (id: number, activeStep: number) => {
    if (id < activeStep) {
        return 'completed';
    } else if (id === activeStep) {
        return 'active';
    } else {
        return 'inactive';
    }
};

const STATUS = {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    COMPLETED: 'completed',
};
</script>

<template>
  <div
    class="stepper-wrapper"
    :data-testid="props.dataTestId ? props.dataTestId : props.id ? props.id : 'stepper-wrapper'"
  >
    <div
      v-for="item in steps"
      :id="item.id"
      :key="item.id"
      class="step-item"
      :data-testid="
        item.dataTestId
          ? item.dataTestId
          : `${props.dataTestId ? props.dataTestId : props.id ? props.id : 'stepper-wrapper'}-step-${item.id}`
      "
    >
      <!-- For preRequired and Non-preRequired steps Number-Bubble-->
      <!-- When steps change from first to next -->

      <div
        v-if="
          getStatus(item.id, props.activeStep) === STATUS.ACTIVE ||
            (getStatus(item.id, props.activeStep) === STATUS.INACTIVE && !props.preRequired)
        "
        class="step-counter"
      >
        {{ item.id }}
      </div>

      <div
        v-if="getStatus(item.id, props.activeStep) === STATUS.COMPLETED"
        class="step-completed"
      >
        <CxmIcon name="step-completed" />
      </div>

      <div
        v-if="getStatus(item.id, props.activeStep) === STATUS.INACTIVE && props.preRequired"
        class="step-counter inactive"
      >
        {{ item.id }}
      </div>

      <!-- For preRequired and Non-preRequired steps title -->
      <!-- Each steps title CSS when steps change from first to next -->

      <div
        v-if="
          (getStatus(item.id, props.activeStep) === STATUS.ACTIVE && !props.preRequired) ||
            (getStatus(item.id, props.activeStep) === STATUS.ACTIVE && props.preRequired)
        "
        class="step-counter-title"
      >
        {{ item.title }}
      </div>

      <div
        v-if="getStatus(item.id, props.activeStep) === STATUS.INACTIVE && props.preRequired"
        class="step-counter-title inactive"
      >
        {{ item.title }}
      </div>

      <div
        v-if="
          (!props.preRequired && getStatus(item.id, props.activeStep) === STATUS.COMPLETED) ||
            (!props.preRequired && getStatus(item.id, props.activeStep) === STATUS.INACTIVE) ||
            (getStatus(item.id, props.activeStep) === STATUS.COMPLETED && props.preRequired)
        "
        class="step-counter-title completed"
      >
        {{ item.title }}
      </div>

      <!-- For right arrow on each step  -->
      <!-- Last index of steps will be disable for arrow by if condition -->

      <div
        v-if="steps.length > item.id"
        class="step-arrow"
      >
        <CxmIcon name="arrow-caret-right-large" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.stepper-wrapper {
    padding: 10px;
    display: flex;
    flex-flow: row wrap;
}

.step-item {
    display: flex;
    flex-direction: row;
    padding: 0px 10px 0px 10px;
    width: auto;
    justify-content: center;
}

.step-counter {
    position: relative;
    z-index: 5;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 16px;
    height: 16px;
    top: 0.2rem;
    font-size: 12px;
    border-radius: 24px;
    background: #006acc;
    margin-bottom: 6px;
    color: #ffff;
}

.step-counter.inactive {
    background: #6d7379 !important;
}

.step-counter-title {
    font-family: Inter;
    font-size: 13px;
    font-weight: 500;
    line-height: 23px;
    letter-spacing: 0px;
    text-align: left;
    padding-left: 10px;
    color: #303336 !important;
}

.step-counter-title.completed {
    color: #005db2 !important;
}

.step-counter-title.inactive {
    color: #6d7379 !important;
}

.step-completed {
    margin-top: 0.1rem;
}

.step-arrow {
    padding-left: 10px;
    color: #6d7379 !important;
    width: 12px;
    height: 8px;
}
</style>
