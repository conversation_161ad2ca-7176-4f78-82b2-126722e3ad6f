<script setup lang="ts">
import ProgressBar from 'primevue/progressbar';

interface Props {
    value?: number;
    mode?: 'determinate' | 'indeterminate';
    showValue?: boolean;
    unit?: string;
    color?: string;
}

defineProps<Props>();
</script>

<template>
  <ProgressBar
    class="bravo-progressbar"
    :value="value"
    :mode="mode"
    :show-value="showValue"
    :unit="unit"
    :color="color"
    v-bind="$attrs"
  >
    <slot></slot>
  </ProgressBar>
</template>

<style lang="scss" scoped></style>
