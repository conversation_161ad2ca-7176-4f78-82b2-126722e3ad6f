<script setup lang="ts">
import TabPanel from 'primevue/tabpanel';

interface Props {
    value: string | number;
    as?: string;
    asChild?: boolean;
}

const props = defineProps<Props>();

defineOptions({
    name: 'BravoTabPanel',
});
</script>

<template>
  <TabPanel
    class="bravo-tabpanel"
    :value="props.value"
    :as="props.as"
    :as-child="props.asChild"
    v-bind="$attrs"
  >
    <slot></slot>
  </TabPanel>
</template>

<style lang="scss" scoped>
</style> 