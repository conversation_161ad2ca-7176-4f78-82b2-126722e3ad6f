<script setup lang="ts">
import RadioButton from 'primevue/radiobutton';

type BaseScaleProps = {
    fromLabel: string;
    toLabel: string;
    label: string;
    options: number;
    modelValue: string;
};

const props = withDefaults(defineProps<BaseScaleProps>(), {
    modelValue: '',
});
const emit = defineEmits(['update:modelValue']);
</script>

<template>
  <div class="cxm-scale-container">
    <div class="cxm-scale-label">
      {{ props.label }}
    </div>
    <div class="scale-options">
      <div
        v-for="option in options"
        :key="option"
        class=""
        style="margin-bottom: 4px"
      >
        <RadioButton
          :model-value="props.modelValue"
          :value="option.toString()"
          @input="emit('update:modelValue', $event.target.value)"
        />
      </div>
    </div>

    <div class="cxm-scale-under-label">
      <span class="left">{{ props.fromLabel }}</span>
      <span class="right">{{ props.toLabel }}</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.scale-options {
    display: flex;
    width: 100%;
    justify-content: space-between;
    background: #f7f7f8;
    padding: 13px;
    border-radius: 8px;
}
.cxm-scale-label {
    color: #6d7379;
}
.cxm-scale-under-label {
    display: flex;
    justify-content: space-between;
    color: #6d7379;
}

.left {
    margin-left: 5px;
}
.right {
    margin-right: 5px;
}
</style>

<style lang="scss"></style>
