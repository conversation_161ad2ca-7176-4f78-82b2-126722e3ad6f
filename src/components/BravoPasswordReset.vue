<script setup lang="ts">
import { ref } from 'vue';
import BravoButton from './BravoButton.vue';
import BravoInputText from './BravoInputText.vue';

const email = ref('');
const loading = ref(false);
const error = ref('');
const isSubmitSuccess = ref(false);
const isSsoUser = ref(false);

const props = defineProps<{
  submitHandler?: (email: string) => Promise<void>;
  checkSsoUser?: (email: string) => Promise<boolean>;
}>();

const emit = defineEmits<{
  submit: [{ email: string }];
  backToLogin: [];
}>();

const handleSubmit = async () => {
    if (!email.value) {
        error.value = 'Please enter your email address';
        return;
    }

    loading.value = true;
    error.value = '';
    const trimmedEmail = email.value.trim();

    try {
        // Check if user is SSO user if handler is provided
        if (props.checkSsoUser) {
            const isSso = await props.checkSsoUser(trimmedEmail);
            if (isSso) {
                isSsoUser.value = true;
                loading.value = false;
                return;
            }
        }
        
        // Emit the submit event with the email
        emit('submit', { email: trimmedEmail });
        
        // If submitHandler prop is provided, call it
        if (props.submitHandler) {
            await props.submitHandler(trimmedEmail);
        }
        
        // Set success state
        isSubmitSuccess.value = true;
    } catch (err) {
        error.value = err instanceof Error ? err.message : 'Failed to process request. Please try again.';
    } finally {
        loading.value = false;
    }
};

const handleBackToLogin = () => {
    emit('backToLogin');
};
</script>

<template>
  <div class="login-wrapper">
    <div class="content-column">
      <div class="login-container">
        <!-- Request Form -->
        <div v-if="!isSubmitSuccess && !isSsoUser">
          <h1>Forgot your password?</h1>
          <p class="subtitle">
            Enter your email to receive a link to reset your password.
          </p>          
          <div
            v-if="error"
            class="error-message"
          >
            {{ error }}
          </div>

          <form @submit.prevent="handleSubmit">
            <div class="form-group email-field-container">
              <label>Email</label>
              <BravoInputText
                v-model="email"
                type="email"
                :disabled="loading"
                tabindex="1"
              />
            </div>

            <BravoButton
              type="submit"
              label="Submit"
              class="submit-button"
              tabindex="2"
              :loading="loading"
              :disabled="loading"
            />
          </form>
        </div>
                
        <!-- Success Screen -->
        <div
          v-else-if="isSubmitSuccess"
          class="success-container"
        >
          <h1>Password reset link sent</h1>
          <p class="subtitle">
            We have sent an email to <strong>{{ email }}</strong>. 
            Please click the link in that email to reset your password.
          </p>
                    
          <BravoButton
            label="Back to Login"
            class="back-to-login-button"
            tabindex="1"
            @click="handleBackToLogin"
          />
        </div>
                
        <!-- SSO User Screen -->
        <div
          v-else-if="isSsoUser"
          class="sso-user-container"
        >
          <h1>Password not reset</h1>
          <p class="subtitle">
            Your user is set up to log in with SSO and not a password. Please enter your email 
            on the login screen to log in with SSO. If you need to reset your password or are 
            having additional issues logging in, please contact your company administrator.
          </p>
                    
          <BravoButton
            label="Back to Login"
            class="back-to-login-button"
            tabindex="1"
            @click="handleBackToLogin"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.content-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 440px;
}

.login-container {
    width: 100%;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
}

.login-container h1 {
    font-size: 20px;
    line-height: 28px;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--surface-850);
    text-align: left;
}

.subtitle {
    font-size: 14px;
    line-height: 20px;
    color: var(--surface-700);
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 14px;
    color: var(--surface-850);
}

.submit-button, .back-to-login-button {
    width: 100%;
    background: var(--primary-600);
    padding: 0.75rem;
    font-weight: 500;
    margin: 1rem 0 1rem 0;
    min-height: 44px;
}

:deep(.p-inputtext) {
    width: 100%;
    height: 44px !important;
}

.error-message {
    background-color: var(--red-100);
    color: var(--red-700);
    padding: 0.75rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    font-size: 14px;
}

.success-container strong, .sso-user-container strong {
    font-weight: 600;
}

@media (min-width: 640px) {
    .login-container {
        padding: 3rem;
    }

    .login-container h1 {
        font-size: 22px;
        padding-bottom: 0.5rem;
    }
}

@media (min-width: 768px) {
    .login-container {
        padding: 4rem;
    }

    .login-container h1 {
        font-size: 24px;
        padding-bottom: 0.5rem;
    }
}
</style>
