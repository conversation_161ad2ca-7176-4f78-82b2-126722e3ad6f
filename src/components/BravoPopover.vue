<script setup lang="ts">
import OverlayPanel from 'primevue/overlaypanel';
import { ref, watch } from 'vue';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
  target: {
    type: Object as () => HTMLElement | null,
    default: null,
  },
});

const emit = defineEmits(['show', 'hide', 'update:visible']);

const op = ref();
const isVisible = ref(props.visible);

watch(
    () => props.visible,
    (newValue) => {
        isVisible.value = newValue;
    }
);

watch(isVisible, (newValue) => {
    emit('update:visible', newValue);
});

const toggle = (event?: MouseEvent, overrideTarget?: HTMLElement | null) => {
  const resolvedTarget = overrideTarget || props.target;

  if (!op.value) {
    return;
  }

  if (isVisible.value) {
    op.value.hide();
  } else {
    // Note: OverlayPanel.toggle() in PrimeVue accepts (event, target?)
    op.value.toggle(event, resolvedTarget);
    // emit both pieces so parent/consumer knows exactly what was used
    emit('show', event!, resolvedTarget);
  }
};

/**
 * Show explicitly (bypass toggle). Behaves almost the same, but only opens if closed.
 */
const show = (event: MouseEvent, overrideTarget?: HTMLElement | null) => {
  const resolvedTarget = overrideTarget || props.target;

  if (!op.value || isVisible.value) {
    return;
  }

  op.value.show(event, resolvedTarget);
  emit('show', event, resolvedTarget);
};

const hide = () => {
    if (op.value && isVisible.value) {
        op.value.hide();
    }
};

const onShow = () => {
    isVisible.value = true;
    emit('show');
};

const onHide = () => {
    isVisible.value = false;
    emit('hide');
};

defineExpose({
    toggle,
    show,
    hide,
});
</script>

<template>
  <OverlayPanel
    ref="op"
    class="bravo-popover"
    v-bind="$attrs"
    @show="onShow"
    @hide="onHide"
  >
    <slot></slot>
  </OverlayPanel>
</template>
