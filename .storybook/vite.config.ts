import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

export default defineConfig({
  plugins: [vue()],
  optimizeDeps: {
    include: ['vue', 'primevue']
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, '../src')
    },
    dedupe: ['vue']
  },
  build: {
    outDir: resolve(__dirname, '../storybook-static'),
    cssCodeSplit: true,
    rollupOptions: {
      output: {
        manualChunks: {
          'primevue': ['primevue'],
          'theme': [
            '@/themes/theme-v4',
            '@/themes/global.css',
            '@/themes/primevue-variables.css',
            'primeicons/primeicons.css',
            'quill/dist/quill.core.css',
            'quill/dist/quill.snow.css'
          ]
        },
        assetFileNames: (assetInfo) => {
          if (assetInfo.name === 'global.css' || assetInfo.name === 'primevue-variables.css') {
            return 'themes/[name][extname]';
          }
          return 'assets/[name]-[hash][extname]';
        }
      }
    }
  },
  define: {
    'process.env': {}
  },
  publicDir: resolve(__dirname, '../src')
}); 