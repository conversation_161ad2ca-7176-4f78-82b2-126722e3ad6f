import type { Preview } from '@storybook/vue3';
import { setup } from '@storybook/vue3';
import PrimeVue from 'primevue/config';
import ToastService from 'primevue/toastservice';
import ConfirmationService from 'primevue/confirmationservice';
import Ripple from 'primevue/ripple';
import Tooltip from 'primevue/tooltip';
import MyAuraPreset from '../src/themes/theme-v4';
import postcssConfig from '../postcss.config.js';

// Import base styles first
import '../src/themes/global.css'

// Import PrimeVue styles after base styles
import 'primeicons/primeicons.css'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'

// Import PrimeVue variables last
import '../src/themes/primevue-variables.css'

setup((app) => {
  app.use(PrimeVue, {
    theme: {
      preset: MyAuraPreset,
      options: {
        prefix: 'p',
        darkModeSelector: 'system',
        cssLayer: false
      }
    },
    ripple: true,
    unstyled: false,
    pt: {}
  });
  app.use(ToastService);
  app.use(ConfirmationService);
  app.directive('ripple', Ripple);
  app.directive('tooltip', Tooltip);
});

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    postcss: {
      postcssOptions: postcssConfig
    }
  },
};

export default preview;




