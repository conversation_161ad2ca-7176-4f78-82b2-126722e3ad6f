import { expect, test } from 'vitest';
import { formatRelativeTimestamp } from '../src/utils/dateUtils';
import { DateTime } from 'luxon';

test('Format Seconds Ago time Stamp', () => {
  const datetime = DateTime.fromJSDate(new Date()).minus(3000);
  const timestamp = datetime.toISO() || '';
  const formattedTimeStamp = formatRelativeTimestamp(timestamp);
  expect(formattedTimeStamp).to.equal(
    datetime.toFormat('h:mm a') + ' (0m ago)'
  );
});

test('Format Minutes Ago time Stamp', () => {
  const datetime = DateTime.fromJSDate(new Date()).minus(3000 * 60);
  const timestamp = datetime.toISO() || '';
  const formattedTimeStamp = formatRelativeTimestamp(timestamp);
  expect(formattedTimeStamp).to.equal(
    datetime.toFormat('h:mm a') + ' (3m ago)'
  );
});

test('Format Hours Ago time Stamp', () => {
  const datetime = DateTime.fromJSDate(new Date()).minus(5000 * 60 * 60);
  const timestamp = datetime.toISO() || '';
  const formattedTimeStamp = formatRelativeTimestamp(timestamp);
  expect(formattedTimeStamp).to.equal(
    datetime.toFormat('h:mm a') + ' (5h ago)'
  );
});

test('Format Days Ago time Stamp', () => {
  const datetime = DateTime.fromJSDate(new Date()).minus(1000 * 60 * 60 * 48);
  const timestamp = datetime.toISO() || '';
  const formattedTimeStamp = formatRelativeTimestamp(timestamp);
  expect(formattedTimeStamp).to.equal(
    datetime.toFormat('MMM d, h:mm a') + ' (2d ago)'
  );
});

test('Format Month Ago Days time Stamp', () => {
  const datetime = DateTime.fromJSDate(new Date()).minus(
    1000 * 60 * 60 * 24 * 32
  );
  const timestamp = datetime.toISO() || '';
  const formattedTimeStamp = formatRelativeTimestamp(timestamp);
  expect(formattedTimeStamp).to.equal(datetime.toFormat('MMM d, y, h:mm a'));
});

test('Format Seconds From Now time Stamp', () => {
  const datetime = DateTime.fromJSDate(new Date()).plus(5000);
  const timestamp = datetime.toISO() || '';
  const formattedTimeStamp = formatRelativeTimestamp(timestamp);
  expect(formattedTimeStamp).to.equal(datetime.toFormat('h:mm a') + ' (in 0m)');
});

test('Format Minutes From Now time Stamp', () => {
  const datetime = DateTime.fromJSDate(new Date()).plus(5000 * 60);
  const timestamp = datetime.toISO() || '';
  const formattedTimeStamp = formatRelativeTimestamp(timestamp);
  expect(formattedTimeStamp).to.equal(datetime.toFormat('h:mm a') + ' (in 5m)');
});

test('Format Hours From Now time Stamp', () => {
  const datetime = DateTime.fromJSDate(new Date()).plus(5000 * 60 * 60);
  const timestamp = datetime.toISO() || '';
  const formattedTimeStamp = formatRelativeTimestamp(timestamp);
  expect(formattedTimeStamp).to.equal(datetime.toFormat('h:mm a') + ' (in 5h)');
});

test('Format Days From Now time Stamp', () => {
  const datetime = DateTime.fromJSDate(new Date()).plus(1000 * 60 * 60 * 48);
  const timestamp = datetime.toISO() || '';
  const formattedTimeStamp = formatRelativeTimestamp(timestamp);
  expect(formattedTimeStamp).to.equal(
    datetime.toFormat('MMM d, h:mm a') + ' (in 2d)'
  );
});

test('Format Month From Now Days time Stamp', () => {
  const datetime = DateTime.fromJSDate(new Date()).plus(
    1000 * 60 * 60 * 24 * 32
  );
  const timestamp = datetime.toISO() || '';
  const formattedTimeStamp = formatRelativeTimestamp(timestamp);
  expect(formattedTimeStamp).to.equal(datetime.toFormat('MMM d, y, h:mm a'));
});

test('Format future date from Unix Integer', () => {
  const datetime = DateTime.fromJSDate(new Date()).plus(1000 * 60 * 60 * 48);
  const timestamp = datetime.toUnixInteger() * 1000;
  const formattedTimeStamp = formatRelativeTimestamp(timestamp);
  expect(formattedTimeStamp).to.equal(
    datetime.toFormat('MMM d, h:mm a') + ' (in 2d)'
  );
});

test('Format past date from Unix Integer', () => {
  const datetime = DateTime.fromJSDate(new Date()).minus(1000 * 60 * 60 * 48);
  const timestamp = datetime.toUnixInteger() * 1000;
  const formattedTimeStamp = formatRelativeTimestamp(timestamp);
  expect(formattedTimeStamp).to.equal(
    datetime.toFormat('MMM d, h:mm a') + ' (2d ago)'
  );
});
